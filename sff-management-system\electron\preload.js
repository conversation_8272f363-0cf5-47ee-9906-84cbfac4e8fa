const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App info
  getVersion: () => ipcRenderer.invoke('app-version'),
  
  // Dialog methods
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  
  // Platform info
  platform: process.platform,
  
  // Environment
  isDev: process.env.NODE_ENV === 'development'
})

// Expose a limited API for the SFF Management System
contextBridge.exposeInMainWorld('sffAPI', {
  // Export data
  exportData: (data, filename) => {
    return ipcRenderer.invoke('export-data', { data, filename })
  },
  
  // Import data
  importData: () => {
    return ipcRenderer.invoke('import-data')
  },
  
  // Print functionality
  print: (content) => {
    return ipcRenderer.invoke('print-content', content)
  },
  
  // Backup functionality
  createBackup: () => {
    return ipcRenderer.invoke('create-backup')
  },
  
  // System notifications
  showNotification: (title, body) => {
    return ipcRenderer.invoke('show-notification', { title, body })
  }
})
