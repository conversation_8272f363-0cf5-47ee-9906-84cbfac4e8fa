{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\nimport {\n  HomeIcon,\n  CubeIcon,\n  BeakerIcon,\n  CogIcon,\n  DocumentTextIcon,\n  UsersIcon,\n  ChartBarIcon,\n  ClipboardDocumentListIcon,\n} from '@heroicons/react/24/outline'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { clsx } from 'clsx'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/', icon: HomeIcon },\n  { name: 'Inventory', href: '/inventory', icon: CubeIcon },\n  { name: 'Recipes', href: '/recipes', icon: ClipboardDocumentListIcon },\n  { name: 'Production', href: '/production', icon: CogIcon },\n  { name: 'Quality', href: '/quality', icon: BeakerIcon },\n  { name: 'Reports', href: '/reports', icon: ChartBarIcon },\n  { name: 'Documents', href: '/documents', icon: DocumentTextIcon },\n  { name: 'Users', href: '/users', icon: UsersIcon },\n]\n\ninterface SidebarProps {\n  open: boolean\n  setOpen: (open: boolean) => void\n}\n\nexport function Sidebar({ open, setOpen }: SidebarProps) {\n  const pathname = usePathname()\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <Transition.Root show={open} as={Fragment}>\n        <Dialog as=\"div\" className=\"relative z-50 lg:hidden\" onClose={setOpen}>\n          <Transition.Child\n            as={Fragment}\n            enter=\"transition-opacity ease-linear duration-300\"\n            enterFrom=\"opacity-0\"\n            enterTo=\"opacity-100\"\n            leave=\"transition-opacity ease-linear duration-300\"\n            leaveFrom=\"opacity-100\"\n            leaveTo=\"opacity-0\"\n          >\n            <div className=\"fixed inset-0 bg-gray-900/80\" />\n          </Transition.Child>\n\n          <div className=\"fixed inset-0 flex\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"transition ease-in-out duration-300 transform\"\n              enterFrom=\"-translate-x-full\"\n              enterTo=\"translate-x-0\"\n              leave=\"transition ease-in-out duration-300 transform\"\n              leaveFrom=\"translate-x-0\"\n              leaveTo=\"-translate-x-full\"\n            >\n              <Dialog.Panel className=\"relative mr-16 flex w-full max-w-xs flex-1\">\n                <Transition.Child\n                  as={Fragment}\n                  enter=\"ease-in-out duration-300\"\n                  enterFrom=\"opacity-0\"\n                  enterTo=\"opacity-100\"\n                  leave=\"ease-in-out duration-300\"\n                  leaveFrom=\"opacity-100\"\n                  leaveTo=\"opacity-0\"\n                >\n                  <div className=\"absolute left-full top-0 flex w-16 justify-center pt-5\">\n                    <button type=\"button\" className=\"-m-2.5 p-2.5\" onClick={() => setOpen(false)}>\n                      <span className=\"sr-only\">Close sidebar</span>\n                      <XMarkIcon className=\"h-6 w-6 text-white\" aria-hidden=\"true\" />\n                    </button>\n                  </div>\n                </Transition.Child>\n                <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4\">\n                  <div className=\"flex h-16 shrink-0 items-center\">\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">SFF</span>\n                    </div>\n                    <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n                  </div>\n                  <nav className=\"flex flex-1 flex-col\">\n                    <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n                      <li>\n                        <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                          {navigation.map((item) => (\n                            <li key={item.name}>\n                              <Link\n                                href={item.href}\n                                className={clsx(\n                                  pathname === item.href\n                                    ? 'bg-green-50 text-green-700'\n                                    : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                                  'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                                )}\n                              >\n                                <item.icon\n                                  className={clsx(\n                                    pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                                    'h-6 w-6 shrink-0'\n                                  )}\n                                  aria-hidden=\"true\"\n                                />\n                                {item.name}\n                              </Link>\n                            </li>\n                          ))}\n                        </ul>\n                      </li>\n                    </ul>\n                  </nav>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </Dialog>\n      </Transition.Root>\n\n      {/* Static sidebar for desktop */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 bg-white px-6 pb-4\">\n          <div className=\"flex h-16 shrink-0 items-center\">\n            <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-sm\">SFF</span>\n            </div>\n            <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n          </div>\n          <nav className=\"flex flex-1 flex-col\">\n            <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n              <li>\n                <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                  {navigation.map((item) => (\n                    <li key={item.name}>\n                      <Link\n                        href={item.href}\n                        className={clsx(\n                          pathname === item.href\n                            ? 'bg-green-50 text-green-700'\n                            : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                          'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                        )}\n                      >\n                        <item.icon\n                          className={clsx(\n                            pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                            'h-6 w-6 shrink-0'\n                          )}\n                          aria-hidden=\"true\"\n                        />\n                        {item.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </li>\n            </ul>\n          </nav>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;;;AAjBA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,kNAAA,CAAA,WAAQ;IAAC;IAC/C;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,oPAAA,CAAA,4BAAyB;IAAC;IACrE;QAAE,MAAM;QAAc,MAAM;QAAe,MAAM,gNAAA,CAAA,UAAO;IAAC;IACzD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,sNAAA,CAAA,aAAU;IAAC;IACtD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,0NAAA,CAAA,eAAY;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kOAAA,CAAA,mBAAgB;IAAC;IAChE;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,oNAAA,CAAA,YAAS;IAAC;CAClD;AAOM,SAAS,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAgB;;IACrD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE;;0BAEE,6LAAC,0LAAA,CAAA,aAAU,CAAC,IAAI;gBAAC,MAAM;gBAAM,IAAI,6JAAA,CAAA,WAAQ;0BACvC,cAAA,6LAAC,kLAAA,CAAA,SAAM;oBAAC,IAAG;oBAAM,WAAU;oBAA0B,SAAS;;sCAC5D,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,6JAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;sCAGjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;gCACf,IAAI,6JAAA,CAAA,WAAQ;gCACZ,OAAM;gCACN,WAAU;gCACV,SAAQ;gCACR,OAAM;gCACN,WAAU;gCACV,SAAQ;0CAER,cAAA,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;oCAAC,WAAU;;sDACtB,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4CACf,IAAI,6JAAA,CAAA,WAAQ;4CACZ,OAAM;4CACN,WAAU;4CACV,SAAQ;4CACR,OAAM;4CACN,WAAU;4CACV,SAAQ;sDAER,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAO,MAAK;oDAAS,WAAU;oDAAe,SAAS,IAAM,QAAQ;;sEACpE,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;4DAAqB,eAAY;;;;;;;;;;;;;;;;;;;;;;sDAI5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,6LAAC;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;8DAE7D,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAG,MAAK;wDAAO,WAAU;kEACxB,cAAA,6LAAC;sEACC,cAAA,6LAAC;gEAAG,MAAK;gEAAO,WAAU;0EACvB,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;kFACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4EACH,MAAM,KAAK,IAAI;4EACf,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;8FAGF,6LAAC,KAAK,IAAI;oFACR,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;oFAEF,eAAY;;;;;;gFAEb,KAAK,IAAI;;;;;;;uEAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiCxC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAE7D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,MAAK;gCAAO,WAAU;0CACxB,cAAA,6LAAC;8CACC,cAAA,6LAAC;wCAAG,MAAK;wCAAO,WAAU;kDACvB,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;sEAGF,6LAAC,KAAK,IAAI;4DACR,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;4DAEF,eAAY;;;;;;wDAEb,KAAK,IAAI;;;;;;;+CAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BtC;GAtIgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Menu, Transition } from '@headlessui/react'\nimport { Bars3Icon, BellIcon } from '@heroicons/react/24/outline'\nimport { useAuth } from '@/components/providers'\nimport { clsx } from 'clsx'\n\ninterface HeaderProps {\n  setSidebarOpen: (open: boolean) => void\n}\n\nexport function Header({ setSidebarOpen }: HeaderProps) {\n  const { user, signOut } = useAuth()\n\n  const userNavigation = [\n    { name: 'Your profile', href: '#' },\n    { name: 'Settings', href: '#' },\n    { name: 'Sign out', href: '#', onClick: signOut },\n  ]\n\n  return (\n    <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n      <button\n        type=\"button\"\n        className=\"-m-2.5 p-2.5 text-gray-700 lg:hidden\"\n        onClick={() => setSidebarOpen(true)}\n      >\n        <span className=\"sr-only\">Open sidebar</span>\n        <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n      </button>\n\n      {/* Separator */}\n      <div className=\"h-6 w-px bg-gray-200 lg:hidden\" aria-hidden=\"true\" />\n\n      <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n        <div className=\"relative flex flex-1\">\n          {/* Search can be added here later */}\n        </div>\n        <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n          <button type=\"button\" className=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\">\n            <span className=\"sr-only\">View notifications</span>\n            <BellIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n\n          {/* Separator */}\n          <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200\" aria-hidden=\"true\" />\n\n          {/* Profile dropdown */}\n          <Menu as=\"div\" className=\"relative\">\n            <Menu.Button className=\"-m-1.5 flex items-center p-1.5\">\n              <span className=\"sr-only\">Open user menu</span>\n              <div className=\"h-8 w-8 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.username?.charAt(0).toUpperCase() || 'U'}\n                </span>\n              </div>\n              <span className=\"hidden lg:flex lg:items-center\">\n                <span className=\"ml-4 text-sm font-semibold leading-6 text-gray-900\" aria-hidden=\"true\">\n                  {user?.username || 'User'}\n                </span>\n                <span className=\"ml-2 text-xs text-gray-500 capitalize\">\n                  ({user?.role?.replace('_', ' ') || 'Role'})\n                </span>\n              </span>\n            </Menu.Button>\n            <Transition\n              as={Fragment}\n              enter=\"transition ease-out duration-100\"\n              enterFrom=\"transform opacity-0 scale-95\"\n              enterTo=\"transform opacity-100 scale-100\"\n              leave=\"transition ease-in duration-75\"\n              leaveFrom=\"transform opacity-100 scale-100\"\n              leaveTo=\"transform opacity-0 scale-95\"\n            >\n              <Menu.Items className=\"absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none\">\n                {userNavigation.map((item) => (\n                  <Menu.Item key={item.name}>\n                    {({ active }) => (\n                      <button\n                        onClick={item.onClick}\n                        className={clsx(\n                          active ? 'bg-gray-50' : '',\n                          'block w-full text-left px-3 py-1 text-sm leading-6 text-gray-900'\n                        )}\n                      >\n                        {item.name}\n                      </button>\n                    )}\n                  </Menu.Item>\n                ))}\n              </Menu.Items>\n            </Transition>\n          </Menu>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;;;AANA;;;;;;AAYO,SAAS,OAAO,EAAE,cAAc,EAAe;;IACpD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAAgB,MAAM;QAAI;QAClC;YAAE,MAAM;YAAY,MAAM;QAAI;QAC9B;YAAE,MAAM;YAAY,MAAM;YAAK,SAAS;QAAQ;KACjD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,eAAe;;kCAE9B,6LAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;wBAAU,eAAY;;;;;;;;;;;;0BAI7C,6LAAC;gBAAI,WAAU;gBAAiC,eAAY;;;;;;0BAE5D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,MAAK;gCAAS,WAAU;;kDAC9B,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;gCAAgD,eAAY;;;;;;0CAG3E,6LAAC,8KAAA,CAAA,OAAI;gCAAC,IAAG;gCAAM,WAAU;;kDACvB,6LAAC,8KAAA,CAAA,OAAI,CAAC,MAAM;wCAAC,WAAU;;0DACrB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb,MAAM,UAAU,OAAO,GAAG,iBAAiB;;;;;;;;;;;0DAGhD,6LAAC;gDAAK,WAAU;;kEACd,6LAAC;wDAAK,WAAU;wDAAqD,eAAY;kEAC9E,MAAM,YAAY;;;;;;kEAErB,6LAAC;wDAAK,WAAU;;4DAAwC;4DACpD,MAAM,MAAM,QAAQ,KAAK,QAAQ;4DAAO;;;;;;;;;;;;;;;;;;;kDAIhD,6LAAC,0LAAA,CAAA,aAAU;wCACT,IAAI,6JAAA,CAAA,WAAQ;wCACZ,OAAM;wCACN,WAAU;wCACV,SAAQ;wCACR,OAAM;wCACN,WAAU;wCACV,SAAQ;kDAER,cAAA,6LAAC,8KAAA,CAAA,OAAI,CAAC,KAAK;4CAAC,WAAU;sDACnB,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;8DACP,CAAC,EAAE,MAAM,EAAE,iBACV,6LAAC;4DACC,SAAS,KAAK,OAAO;4DACrB,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,SAAS,eAAe,IACxB;sEAGD,KAAK,IAAI;;;;;;mDATA,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqB3C;GAtFgB;;QACY,kIAAA,CAAA,UAAO;;;KADnB", "debugId": null}}, {"offset": {"line": 680, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/components/providers'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const { user } = useAuth()\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Sidebar open={sidebarOpen} setOpen={setSidebarOpen} />\n      \n      <div className=\"lg:pl-72\">\n        <Header setSidebarOpen={setSidebarOpen} />\n        \n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAWO,SAAS,WAAW,EAAE,QAAQ,EAAmB;;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAO;gBAAC,MAAM;gBAAa,SAAS;;;;;;0BAErC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,SAAM;wBAAC,gBAAgB;;;;;;kCAExB,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAnBgB;;QAEG,kIAAA,CAAA,UAAO;;;KAFV", "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createClientComponentClient } from '@supabase/auth-helpers-nextjs'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Client component client\nexport const createSupabaseClient = () => createClientComponentClient()\n\n// Admin client (server-side only)\nexport const createSupabaseAdminClient = () => {\n  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n  return createClient(supabaseUrl, serviceRoleKey, {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  })\n}\n\n// Database types\nexport interface Database {\n  public: {\n    Tables: {\n      profiles: {\n        Row: {\n          id: string\n          email: string\n          full_name: string\n          role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department: string | null\n          phone: string | null\n          is_active: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          email: string\n          full_name: string\n          role?: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department?: string | null\n          phone?: string | null\n          is_active?: boolean\n        }\n        Update: {\n          email?: string\n          full_name?: string\n          role?: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department?: string | null\n          phone?: string | null\n          is_active?: boolean\n        }\n      }\n      inventory_categories: {\n        Row: {\n          id: string\n          name: string\n          description: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          description?: string | null\n        }\n        Update: {\n          name?: string\n          description?: string | null\n        }\n      }\n      inventory_items: {\n        Row: {\n          id: string\n          name: string\n          sku: string\n          category_id: string | null\n          description: string | null\n          unit_of_measure: string\n          current_stock: number\n          minimum_stock: number\n          maximum_stock: number | null\n          unit_cost: number\n          supplier_info: any | null\n          storage_conditions: string | null\n          expiry_date: string | null\n          batch_number: string | null\n          is_active: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          sku: string\n          category_id?: string | null\n          description?: string | null\n          unit_of_measure: string\n          current_stock?: number\n          minimum_stock?: number\n          maximum_stock?: number | null\n          unit_cost?: number\n          supplier_info?: any | null\n          storage_conditions?: string | null\n          expiry_date?: string | null\n          batch_number?: string | null\n          is_active?: boolean\n        }\n        Update: {\n          name?: string\n          sku?: string\n          category_id?: string | null\n          description?: string | null\n          unit_of_measure?: string\n          current_stock?: number\n          minimum_stock?: number\n          maximum_stock?: number | null\n          unit_cost?: number\n          supplier_info?: any | null\n          storage_conditions?: string | null\n          expiry_date?: string | null\n          batch_number?: string | null\n          is_active?: boolean\n        }\n      }\n      recipes: {\n        Row: {\n          id: string\n          name: string\n          code: string\n          description: string | null\n          category: string | null\n          version: number\n          batch_size: number\n          unit_of_measure: string\n          estimated_cost: number | null\n          preparation_time: number | null\n          instructions: string | null\n          notes: string | null\n          is_active: boolean\n          created_by: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          code: string\n          description?: string | null\n          category?: string | null\n          version?: number\n          batch_size: number\n          unit_of_measure: string\n          estimated_cost?: number | null\n          preparation_time?: number | null\n          instructions?: string | null\n          notes?: string | null\n          is_active?: boolean\n          created_by?: string | null\n        }\n        Update: {\n          name?: string\n          code?: string\n          description?: string | null\n          category?: string | null\n          version?: number\n          batch_size?: number\n          unit_of_measure?: string\n          estimated_cost?: number | null\n          preparation_time?: number | null\n          instructions?: string | null\n          notes?: string | null\n          is_active?: boolean\n        }\n      }\n      production_batches: {\n        Row: {\n          id: string\n          batch_number: string\n          recipe_id: string\n          batch_type: 'test' | 'production'\n          planned_quantity: number\n          actual_quantity: number | null\n          unit_of_measure: string\n          status: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date: string | null\n          actual_start_date: string | null\n          planned_end_date: string | null\n          actual_end_date: string | null\n          production_cost: number | null\n          yield_percentage: number | null\n          quality_approved: boolean | null\n          notes: string | null\n          created_by: string | null\n          assigned_to: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          batch_number: string\n          recipe_id: string\n          batch_type: 'test' | 'production'\n          planned_quantity: number\n          actual_quantity?: number | null\n          unit_of_measure: string\n          status?: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority?: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date?: string | null\n          actual_start_date?: string | null\n          planned_end_date?: string | null\n          actual_end_date?: string | null\n          production_cost?: number | null\n          yield_percentage?: number | null\n          quality_approved?: boolean | null\n          notes?: string | null\n          created_by?: string | null\n          assigned_to?: string | null\n        }\n        Update: {\n          batch_number?: string\n          recipe_id?: string\n          batch_type?: 'test' | 'production'\n          planned_quantity?: number\n          actual_quantity?: number | null\n          unit_of_measure?: string\n          status?: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority?: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date?: string | null\n          actual_start_date?: string | null\n          planned_end_date?: string | null\n          actual_end_date?: string | null\n          production_cost?: number | null\n          yield_percentage?: number | null\n          quality_approved?: boolean | null\n          notes?: string | null\n          assigned_to?: string | null\n        }\n      }\n    }\n    Views: {\n      [_ in never]: never\n    }\n    Functions: {\n      check_recipe_availability: {\n        Args: {\n          recipe_uuid: string\n          batch_size_multiplier?: number\n        }\n        Returns: {\n          item_id: string\n          item_name: string\n          required_quantity: number\n          available_quantity: number\n          is_sufficient: boolean\n        }[]\n      }\n      calculate_recipe_cost: {\n        Args: {\n          recipe_uuid: string\n          batch_size_multiplier?: number\n        }\n        Returns: number\n      }\n    }\n    Enums: {\n      user_role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAGoB;AAHpB;AACA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,uBAAuB,IAAM,CAAA,GAAA,2KAAA,CAAA,8BAA2B,AAAD;AAG7D,MAAM,4BAA4B;IACvC,MAAM,iBAAiB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB;IAC5D,OAAO,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa,gBAAgB;QAC/C,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 797, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/lib/database.ts"], "sourcesContent": ["import { createSupabaseClient } from './supabase'\n\nconst supabase = createSupabaseClient()\n\n// Inventory Items\nexport interface InventoryItem {\n  id: string\n  name: string\n  sku: string\n  category_id: string\n  current_stock: number\n  minimum_stock: number\n  unit_of_measure: string\n  unit_cost: number\n  supplier?: string\n  description?: string\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface InventoryCategory {\n  id: string\n  name: string\n  description?: string\n  is_active: boolean\n}\n\n// Recipes\nexport interface Recipe {\n  id: string\n  name: string\n  code: string\n  category: string\n  batch_size: number\n  unit_of_measure: string\n  instructions?: string\n  is_active: boolean\n  version: number\n  created_at: string\n  updated_at: string\n}\n\n// Production Batches\nexport interface ProductionBatch {\n  id: string\n  batch_number: string\n  recipe_id: string\n  batch_type: 'test' | 'production'\n  planned_quantity: number\n  actual_quantity?: number\n  unit_of_measure: string\n  status: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n  priority: 'low' | 'normal' | 'high' | 'urgent'\n  planned_start_date?: string\n  planned_end_date?: string\n  actual_start_date?: string\n  actual_end_date?: string\n  assigned_to?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\n// Quality Documents\nexport interface QualityDocument {\n  id: string\n  document_type: 'msds' | 'coa' | 'tds' | 'quality_spec' | 'lab_report'\n  title: string\n  document_number: string\n  version: string\n  item_id?: string\n  batch_id?: string\n  status: 'draft' | 'review' | 'approved' | 'expired'\n  valid_from?: string\n  valid_until?: string\n  file_path?: string\n  created_by: string\n  created_at: string\n  updated_at: string\n}\n\n// Database service functions\nexport class DatabaseService {\n  // Inventory Items\n  static async getInventoryItems(): Promise<InventoryItem[]> {\n    try {\n      const { data, error } = await supabase\n        .from('inventory_items')\n        .select(`\n          *,\n          inventory_categories(name)\n        `)\n        .eq('is_active', true)\n        .order('name')\n\n      if (error) {\n        console.error('Error fetching inventory items:', error)\n        // Return demo data when database is not available\n        return this.getDemoInventoryItems()\n      }\n\n      // If no data, return demo data\n      if (!data || data.length === 0) {\n        return this.getDemoInventoryItems()\n      }\n\n      return data\n    } catch (error) {\n      console.error('Database connection error:', error)\n      return this.getDemoInventoryItems()\n    }\n  }\n\n  static getDemoInventoryItems(): InventoryItem[] {\n    return [\n      {\n        id: 'demo_item_1',\n        name: 'Vanilla Extract Premium',\n        sku: 'VAN-001',\n        category_id: 'demo_cat_1',\n        current_stock: 25.5,\n        minimum_stock: 10.0,\n        unit_of_measure: 'liters',\n        unit_cost: 12.50,\n        supplier: 'Premium Ingredients Co.',\n        description: 'High-quality vanilla extract for premium flavoring applications',\n        is_active: true,\n        created_at: '2024-01-15T08:00:00Z',\n        updated_at: '2024-01-15T08:00:00Z'\n      },\n      {\n        id: 'demo_item_2',\n        name: 'Strawberry Concentrate',\n        sku: 'STR-002',\n        category_id: 'demo_cat_1',\n        current_stock: 8.2,\n        minimum_stock: 15.0,\n        unit_of_measure: 'liters',\n        unit_cost: 18.75,\n        supplier: 'Fruit Essences Ltd.',\n        description: 'Natural strawberry concentrate for beverage and dessert applications',\n        is_active: true,\n        created_at: '2024-01-20T10:30:00Z',\n        updated_at: '2024-01-20T10:30:00Z'\n      },\n      {\n        id: 'demo_item_3',\n        name: 'Citric Acid Food Grade',\n        sku: 'CIT-003',\n        category_id: 'demo_cat_2',\n        current_stock: 45.0,\n        minimum_stock: 20.0,\n        unit_of_measure: 'kg',\n        unit_cost: 3.25,\n        supplier: 'Chemical Solutions Inc.',\n        description: 'Food-grade citric acid for pH adjustment and preservation',\n        is_active: true,\n        created_at: '2024-02-01T14:15:00Z',\n        updated_at: '2024-02-01T14:15:00Z'\n      },\n      {\n        id: 'demo_item_4',\n        name: 'Glass Bottles 500ml',\n        sku: 'BTL-004',\n        category_id: 'demo_cat_3',\n        current_stock: 0,\n        minimum_stock: 100,\n        unit_of_measure: 'pieces',\n        unit_cost: 0.85,\n        supplier: 'Packaging Solutions Ltd.',\n        description: 'Clear glass bottles with screw caps for beverage packaging',\n        is_active: true,\n        created_at: '2024-02-10T09:45:00Z',\n        updated_at: '2024-02-10T09:45:00Z'\n      },\n      {\n        id: 'demo_item_5',\n        name: 'Natural Lemon Oil',\n        sku: 'LEM-005',\n        category_id: 'demo_cat_1',\n        current_stock: 3.8,\n        minimum_stock: 5.0,\n        unit_of_measure: 'liters',\n        unit_cost: 45.00,\n        supplier: 'Essential Oils Direct',\n        description: 'Cold-pressed natural lemon oil for citrus flavoring',\n        is_active: true,\n        created_at: '2024-02-15T11:20:00Z',\n        updated_at: '2024-02-15T11:20:00Z'\n      }\n    ]\n  }\n\n  static async createInventoryItem(item: Omit<InventoryItem, 'id' | 'created_at' | 'updated_at'>): Promise<InventoryItem | null> {\n    try {\n      const { data, error } = await supabase\n        .from('inventory_items')\n        .insert([item])\n        .select()\n        .single()\n\n      if (error) {\n        console.error('Error creating inventory item:', error)\n        // For demo purposes, create a mock item when database is not available\n        return this.createDemoInventoryItem(item)\n      }\n\n      return data\n    } catch (error) {\n      console.error('Database connection error:', error)\n      // For demo purposes, create a mock item when database is not available\n      return this.createDemoInventoryItem(item)\n    }\n  }\n\n  static createDemoInventoryItem(item: Omit<InventoryItem, 'id' | 'created_at' | 'updated_at'>): InventoryItem {\n    return {\n      ...item,\n      id: `demo_item_${Date.now()}`,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }\n  }\n\n  static async updateInventoryItem(id: string, updates: Partial<InventoryItem>): Promise<InventoryItem | null> {\n    const { data, error } = await supabase\n      .from('inventory_items')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error updating inventory item:', error)\n      return null\n    }\n\n    return data\n  }\n\n  // Inventory Categories\n  static async getInventoryCategories(): Promise<InventoryCategory[]> {\n    try {\n      const { data, error } = await supabase\n        .from('inventory_categories')\n        .select('*')\n        .eq('is_active', true)\n        .order('name')\n\n      if (error) {\n        console.error('Error fetching inventory categories:', error)\n        return this.getDemoInventoryCategories()\n      }\n\n      // If no data, return demo data\n      if (!data || data.length === 0) {\n        return this.getDemoInventoryCategories()\n      }\n\n      return data\n    } catch (error) {\n      console.error('Database connection error:', error)\n      return this.getDemoInventoryCategories()\n    }\n  }\n\n  static getDemoInventoryCategories(): InventoryCategory[] {\n    return [\n      {\n        id: 'demo_cat_1',\n        name: 'Flavoring Agents',\n        description: 'Natural and artificial flavoring compounds and extracts',\n        is_active: true\n      },\n      {\n        id: 'demo_cat_2',\n        name: 'Preservatives & Additives',\n        description: 'Food-grade preservatives, stabilizers, and additives',\n        is_active: true\n      },\n      {\n        id: 'demo_cat_3',\n        name: 'Packaging Materials',\n        description: 'Bottles, caps, labels, and packaging supplies',\n        is_active: true\n      },\n      {\n        id: 'demo_cat_4',\n        name: 'Raw Materials',\n        description: 'Base ingredients and raw materials for production',\n        is_active: true\n      },\n      {\n        id: 'demo_cat_5',\n        name: 'Quality Control',\n        description: 'Testing materials and quality control supplies',\n        is_active: true\n      }\n    ]\n  }\n\n  // Recipes\n  static async getRecipes(): Promise<Recipe[]> {\n    const { data, error } = await supabase\n      .from('recipes')\n      .select('*')\n      .eq('is_active', true)\n      .order('name')\n\n    if (error) {\n      console.error('Error fetching recipes:', error)\n      return []\n    }\n\n    return data || []\n  }\n\n  static async createRecipe(recipe: Omit<Recipe, 'id' | 'created_at' | 'updated_at'>): Promise<Recipe | null> {\n    const { data, error } = await supabase\n      .from('recipes')\n      .insert([recipe])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error creating recipe:', error)\n      return null\n    }\n\n    return data\n  }\n\n  // Production Batches\n  static async getProductionBatches(): Promise<ProductionBatch[]> {\n    const { data, error } = await supabase\n      .from('production_batches')\n      .select(`\n        *,\n        recipes(name, code)\n      `)\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      console.error('Error fetching production batches:', error)\n      return []\n    }\n\n    return data || []\n  }\n\n  static async createProductionBatch(batch: Omit<ProductionBatch, 'id' | 'created_at' | 'updated_at'>): Promise<ProductionBatch | null> {\n    const { data, error } = await supabase\n      .from('production_batches')\n      .insert([batch])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error creating production batch:', error)\n      return null\n    }\n\n    return data\n  }\n\n  // Quality Documents\n  static async getQualityDocuments(): Promise<QualityDocument[]> {\n    const { data, error } = await supabase\n      .from('quality_documents')\n      .select('*')\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      console.error('Error fetching quality documents:', error)\n      return []\n    }\n\n    return data || []\n  }\n\n  static async createQualityDocument(doc: Omit<QualityDocument, 'id' | 'created_at' | 'updated_at'>): Promise<QualityDocument | null> {\n    const { data, error } = await supabase\n      .from('quality_documents')\n      .insert([doc])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error creating quality document:', error)\n      return null\n    }\n\n    return data\n  }\n\n  // User Management\n  static async getUsers(): Promise<any[]> {\n    try {\n      const { data, error } = await supabase\n        .from('profiles')\n        .select('*')\n        .order('created_at', { ascending: false })\n\n      if (error) {\n        // Silently handle database errors and return demo users\n        // This is expected when the database is empty or has constraints\n        return this.getDemoUsers()\n      }\n\n      // If no users in database, return demo users\n      if (!data || data.length === 0) {\n        return this.getDemoUsers()\n      }\n\n      return data\n    } catch (error) {\n      // Silently handle connection errors and return demo users\n      return this.getDemoUsers()\n    }\n  }\n\n  static getDemoUsers(): any[] {\n    return [\n      {\n        id: 'demo_admin',\n        username: 'admin',\n        full_name: 'System Administrator',\n        email: '<EMAIL>',\n        role: 'admin',\n        department: 'IT',\n        phone: '+****************',\n        is_active: true,\n        created_at: '2024-01-15T08:00:00Z',\n        updated_at: '2024-01-15T08:00:00Z'\n      },\n      {\n        id: 'demo_quality',\n        username: 'quality',\n        full_name: 'Sarah Johnson',\n        email: '<EMAIL>',\n        role: 'quality_manager',\n        department: 'Quality Assurance',\n        phone: '+****************',\n        is_active: true,\n        created_at: '2024-02-01T10:00:00Z',\n        updated_at: '2024-02-01T10:00:00Z'\n      },\n      {\n        id: 'demo_production',\n        username: 'production',\n        full_name: 'Mike Wilson',\n        email: '<EMAIL>',\n        role: 'production_manager',\n        department: 'Production',\n        phone: '+****************',\n        is_active: true,\n        created_at: '2024-02-15T14:30:00Z',\n        updated_at: '2024-02-15T14:30:00Z'\n      },\n      {\n        id: 'demo_employee',\n        username: 'employee',\n        full_name: 'Emily Davis',\n        email: '<EMAIL>',\n        role: 'employee',\n        department: 'Production',\n        phone: '+****************',\n        is_active: true,\n        created_at: '2024-03-01T09:00:00Z',\n        updated_at: '2024-03-01T09:00:00Z'\n      }\n    ]\n  }\n\n  static async createUser(userData: {\n    username: string\n    email: string\n    password_hash: string\n    role: string\n    full_name: string\n    department: string\n    phone?: string\n    is_active: boolean\n  }): Promise<any | null> {\n    try {\n      // For demo purposes, since we can't create auth users directly,\n      // we'll simulate user creation and return a demo user object\n      const newUser = {\n        id: `demo_${Date.now()}`,\n        username: userData.username,\n        email: userData.email,\n        full_name: userData.full_name,\n        role: userData.role,\n        department: userData.department,\n        phone: userData.phone || null,\n        is_active: userData.is_active,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }\n\n      // In a real implementation, this would create the user in the database\n      // For now, we'll just return the user object for demo purposes\n      return newUser\n\n      // Commented out actual database insertion due to auth constraints\n      /*\n      const { data, error } = await supabase\n        .from('profiles')\n        .insert([{\n          ...userData,\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        }])\n        .select()\n        .single()\n\n      if (error) {\n        console.error('Error creating user:', error)\n        return null\n      }\n\n      return data\n      */\n    } catch (error) {\n      return null\n    }\n  }\n\n  static async updateUser(id: string, updates: any): Promise<any | null> {\n    const { data, error } = await supabase\n      .from('profiles')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error updating user:', error)\n      return null\n    }\n\n    return data\n  }\n\n  static async toggleUserStatus(id: string, isActive: boolean): Promise<boolean> {\n    try {\n      // For demo users (those with demo_ prefix), just return success\n      if (id.startsWith('demo_')) {\n        return true\n      }\n\n      const { error } = await supabase\n        .from('profiles')\n        .update({ is_active: isActive, updated_at: new Date().toISOString() })\n        .eq('id', id)\n\n      if (error) {\n        return false\n      }\n\n      return true\n    } catch (error) {\n      return false\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;AAiF7B,MAAM;IACX,kBAAkB;IAClB,aAAa,oBAA8C;QACzD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,mBACL,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,kDAAkD;gBAClD,OAAO,IAAI,CAAC,qBAAqB;YACnC;YAEA,+BAA+B;YAC/B,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;gBAC9B,OAAO,IAAI,CAAC,qBAAqB;YACnC;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,IAAI,CAAC,qBAAqB;QACnC;IACF;IAEA,OAAO,wBAAyC;QAC9C,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;SACD;IACH;IAEA,aAAa,oBAAoB,IAA6D,EAAiC;QAC7H,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,mBACL,MAAM,CAAC;gBAAC;aAAK,EACb,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,uEAAuE;gBACvE,OAAO,IAAI,CAAC,uBAAuB,CAAC;YACtC;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,uEAAuE;YACvE,OAAO,IAAI,CAAC,uBAAuB,CAAC;QACtC;IACF;IAEA,OAAO,wBAAwB,IAA6D,EAAiB;QAC3G,OAAO;YACL,GAAG,IAAI;YACP,IAAI,CAAC,UAAU,EAAE,KAAK,GAAG,IAAI;YAC7B,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;IACF;IAEA,aAAa,oBAAoB,EAAU,EAAE,OAA+B,EAAiC;QAC3G,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,mBACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,uBAAuB;IACvB,aAAa,yBAAuD;QAClE,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,wBACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,OAAO,IAAI,CAAC,0BAA0B;YACxC;YAEA,+BAA+B;YAC/B,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;gBAC9B,OAAO,IAAI,CAAC,0BAA0B;YACxC;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,IAAI,CAAC,0BAA0B;QACxC;IACF;IAEA,OAAO,6BAAkD;QACvD,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;SACD;IACH;IAEA,UAAU;IACV,aAAa,aAAgC;QAC3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,EAAE;QACX;QAEA,OAAO,QAAQ,EAAE;IACnB;IAEA,aAAa,aAAa,MAAwD,EAA0B;QAC1G,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,WACL,MAAM,CAAC;YAAC;SAAO,EACf,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,qBAAqB;IACrB,aAAa,uBAAmD;QAC9D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,sBACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO,EAAE;QACX;QAEA,OAAO,QAAQ,EAAE;IACnB;IAEA,aAAa,sBAAsB,KAAgE,EAAmC;QACpI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,sBACL,MAAM,CAAC;YAAC;SAAM,EACd,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,oBAAoB;IACpB,aAAa,sBAAkD;QAC7D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO,EAAE;QACX;QAEA,OAAO,QAAQ,EAAE;IACnB;IAEA,aAAa,sBAAsB,GAA8D,EAAmC;QAClI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC;YAAC;SAAI,EACZ,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,kBAAkB;IAClB,aAAa,WAA2B;QACtC,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,wDAAwD;gBACxD,iEAAiE;gBACjE,OAAO,IAAI,CAAC,YAAY;YAC1B;YAEA,6CAA6C;YAC7C,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;gBAC9B,OAAO,IAAI,CAAC,YAAY;YAC1B;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,0DAA0D;YAC1D,OAAO,IAAI,CAAC,YAAY;QAC1B;IACF;IAEA,OAAO,eAAsB;QAC3B,OAAO;YACL;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;SACD;IACH;IAEA,aAAa,WAAW,QASvB,EAAuB;QACtB,IAAI;YACF,gEAAgE;YAChE,6DAA6D;YAC7D,MAAM,UAAU;gBACd,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;gBACxB,UAAU,SAAS,QAAQ;gBAC3B,OAAO,SAAS,KAAK;gBACrB,WAAW,SAAS,SAAS;gBAC7B,MAAM,SAAS,IAAI;gBACnB,YAAY,SAAS,UAAU;gBAC/B,OAAO,SAAS,KAAK,IAAI;gBACzB,WAAW,SAAS,SAAS;gBAC7B,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,uEAAuE;YACvE,+DAA+D;YAC/D,OAAO;QAEP,kEAAkE;QAClE;;;;;;;;;;;;;;;;;MAiBA,GACF,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,aAAa,WAAW,EAAU,EAAE,OAAY,EAAuB;QACrE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,aAAa,iBAAiB,EAAU,EAAE,QAAiB,EAAoB;QAC7E,IAAI;YACF,gEAAgE;YAChE,IAAI,GAAG,UAAU,CAAC,UAAU;gBAC1B,OAAO;YACT;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,YACL,MAAM,CAAC;gBAAE,WAAW;gBAAU,YAAY,IAAI,OAAO,WAAW;YAAG,GACnE,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO;gBACT,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 1210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/app/documents/new/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { useAuth } from '@/components/providers'\nimport { DatabaseService } from '@/lib/database'\nimport { \n  DocumentTextIcon,\n  CloudArrowUpIcon,\n  CalendarIcon,\n  UserIcon,\n  BuildingOfficeIcon,\n  ExclamationTriangleIcon,\n  CheckCircleIcon,\n  ArrowLeftIcon,\n  DocumentDuplicateIcon,\n  EyeIcon,\n  PrinterIcon\n} from '@heroicons/react/24/outline'\nimport { useRouter } from 'next/navigation'\n\ninterface DocumentFormData {\n  document_type: 'msds' | 'coa' | 'tds' | 'quality_spec' | 'lab_report'\n  title: string\n  document_number: string\n  version: string\n  item_id: string\n  batch_id: string\n  status: 'draft' | 'review' | 'approved' | 'expired'\n  valid_from: string\n  valid_until: string\n  description: string\n  content: string\n  attachments: File[]\n  metadata: {\n    author: string\n    department: string\n    reviewer: string\n    approver: string\n    keywords: string[]\n    classification: 'public' | 'internal' | 'confidential' | 'restricted'\n  }\n}\n\nexport default function NewDocumentPage() {\n  const { user } = useAuth()\n  const router = useRouter()\n  const [currentStep, setCurrentStep] = useState(1)\n  const [isCreating, setIsCreating] = useState(false)\n  const [validationErrors, setValidationErrors] = useState<string[]>([])\n  const [previewMode, setPreviewMode] = useState(false)\n  \n  const [formData, setFormData] = useState<DocumentFormData>({\n    document_type: 'quality_spec',\n    title: '',\n    document_number: '',\n    version: '1.0',\n    item_id: '',\n    batch_id: '',\n    status: 'draft',\n    valid_from: new Date().toISOString().split('T')[0],\n    valid_until: '',\n    description: '',\n    content: '',\n    attachments: [],\n    metadata: {\n      author: user?.full_name || '',\n      department: user?.department || '',\n      reviewer: '',\n      approver: '',\n      keywords: [],\n      classification: 'internal'\n    }\n  })\n\n  // Auto-generate document number\n  const generateDocumentNumber = () => {\n    const typeCode = {\n      'msds': 'MSDS',\n      'coa': 'COA',\n      'tds': 'TDS',\n      'quality_spec': 'QS',\n      'lab_report': 'LAB'\n    }[formData.document_type]\n    \n    const date = new Date().toISOString().slice(0, 10).replace(/-/g, '')\n    const sequence = Math.floor(Math.random() * 1000).toString().padStart(3, '0')\n    return `${typeCode}-${date}-${sequence}`\n  }\n\n  useEffect(() => {\n    if (!formData.document_number) {\n      setFormData(prev => ({\n        ...prev,\n        document_number: generateDocumentNumber()\n      }))\n    }\n  }, [formData.document_type])\n\n  // Validation function\n  const validateForm = () => {\n    const errors: string[] = []\n    \n    if (!formData.title.trim()) errors.push('Document title is required')\n    if (!formData.document_number.trim()) errors.push('Document number is required')\n    if (!formData.version.trim()) errors.push('Version is required')\n    if (!formData.description.trim()) errors.push('Description is required')\n    if (!formData.content.trim()) errors.push('Document content is required')\n    if (!formData.valid_from) errors.push('Valid from date is required')\n    if (!formData.metadata.author.trim()) errors.push('Author is required')\n    if (!formData.metadata.department.trim()) errors.push('Department is required')\n    \n    if (formData.valid_until && new Date(formData.valid_until) <= new Date(formData.valid_from)) {\n      errors.push('Valid until date must be after valid from date')\n    }\n    \n    if (formData.title.length < 5) errors.push('Title must be at least 5 characters')\n    if (formData.content.length < 50) errors.push('Content must be at least 50 characters')\n    \n    setValidationErrors(errors)\n    return errors.length === 0\n  }\n\n  const handleSubmit = async () => {\n    if (!validateForm()) return\n\n    try {\n      setIsCreating(true)\n      \n      const documentData = {\n        ...formData,\n        created_by: user?.id || 'demo_user',\n        metadata: JSON.stringify(formData.metadata)\n      }\n\n      const createdDocument = await DatabaseService.createQualityDocument(documentData)\n\n      if (createdDocument) {\n        alert(`✅ Document \"${formData.title}\" created successfully!\\n\\n📋 Document Details:\\n• Number: ${formData.document_number}\\n• Type: ${getDocumentTypeName(formData.document_type)}\\n• Version: ${formData.version}\\n• Status: ${formData.status.toUpperCase()}\\n• Author: ${formData.metadata.author}`)\n        router.push('/documents')\n      } else {\n        alert('❌ Failed to create document. Please try again.')\n      }\n    } catch (error) {\n      alert('❌ Error creating document. Please check the form and try again.')\n    } finally {\n      setIsCreating(false)\n    }\n  }\n\n  const getDocumentTypeName = (type: string) => {\n    const names = {\n      'msds': 'Material Safety Data Sheet',\n      'coa': 'Certificate of Analysis',\n      'tds': 'Technical Data Sheet',\n      'quality_spec': 'Quality Specification',\n      'lab_report': 'Laboratory Report'\n    }\n    return names[type as keyof typeof names] || type\n  }\n\n  const handleFileUpload = (files: FileList | null) => {\n    if (files) {\n      const newFiles = Array.from(files)\n      setFormData(prev => ({\n        ...prev,\n        attachments: [...prev.attachments, ...newFiles]\n      }))\n    }\n  }\n\n  const removeAttachment = (index: number) => {\n    setFormData(prev => ({\n      ...prev,\n      attachments: prev.attachments.filter((_, i) => i !== index)\n    }))\n  }\n\n  const addKeyword = (keyword: string) => {\n    if (keyword.trim() && !formData.metadata.keywords.includes(keyword.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        metadata: {\n          ...prev.metadata,\n          keywords: [...prev.metadata.keywords, keyword.trim()]\n        }\n      }))\n    }\n  }\n\n  const removeKeyword = (index: number) => {\n    setFormData(prev => ({\n      ...prev,\n      metadata: {\n        ...prev.metadata,\n        keywords: prev.metadata.keywords.filter((_, i) => i !== index)\n      }\n    }))\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return (\n    <MainLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"sm:flex sm:items-center sm:justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <button\n              onClick={() => router.push('/documents')}\n              className=\"inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n            >\n              <ArrowLeftIcon className=\"h-4 w-4 mr-2\" />\n              Back to Documents\n            </button>\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Create New Document</h1>\n              <p className=\"mt-2 text-sm text-gray-700\">\n                Professional document creation with comprehensive metadata and version control\n              </p>\n            </div>\n          </div>\n          <div className=\"mt-4 sm:mt-0 flex space-x-3\">\n            <button\n              onClick={() => setPreviewMode(!previewMode)}\n              className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n            >\n              <EyeIcon className=\"h-4 w-4 mr-2\" />\n              {previewMode ? 'Edit Mode' : 'Preview'}\n            </button>\n          </div>\n        </div>\n\n        {/* Progress Steps */}\n        <div className=\"bg-white shadow rounded-lg p-6\">\n          <div className=\"flex items-center justify-between\">\n            {[1, 2, 3, 4].map((step) => (\n              <div key={step} className=\"flex items-center\">\n                <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${\n                  step <= currentStep \n                    ? 'bg-green-600 border-green-600 text-white' \n                    : 'border-gray-300 text-gray-500'\n                }`}>\n                  {step < currentStep ? (\n                    <CheckCircleIcon className=\"w-5 h-5\" />\n                  ) : (\n                    <span className=\"text-sm font-medium\">{step}</span>\n                  )}\n                </div>\n                <div className=\"ml-2 text-sm font-medium text-gray-900\">\n                  {step === 1 && 'Document Type & Basic Info'}\n                  {step === 2 && 'Content & Description'}\n                  {step === 3 && 'Metadata & Classification'}\n                  {step === 4 && 'Review & Submit'}\n                </div>\n                {step < 4 && (\n                  <div className={`ml-4 w-16 h-0.5 ${\n                    step < currentStep ? 'bg-green-600' : 'bg-gray-300'\n                  }`} />\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Validation Errors */}\n        {validationErrors.length > 0 && (\n          <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n            <div className=\"flex\">\n              <ExclamationTriangleIcon className=\"h-5 w-5 text-red-400\" />\n              <div className=\"ml-3\">\n                <h3 className=\"text-sm font-medium text-red-800\">Please fix the following errors:</h3>\n                <ul className=\"mt-2 text-sm text-red-700 list-disc list-inside\">\n                  {validationErrors.map((error, index) => (\n                    <li key={index}>{error}</li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Main Form */}\n        <div className=\"bg-white shadow rounded-lg\">\n          <div className=\"px-6 py-8\">\n            {/* Step 1: Document Type & Basic Info */}\n            {currentStep === 1 && (\n              <div className=\"space-y-6\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Document Type & Basic Information</h3>\n\n                <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Document Type *</label>\n                    <select\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                      value={formData.document_type}\n                      onChange={(e) => setFormData({ ...formData, document_type: e.target.value as any })}\n                    >\n                      <option value=\"quality_spec\">Quality Specification</option>\n                      <option value=\"msds\">Material Safety Data Sheet (MSDS)</option>\n                      <option value=\"coa\">Certificate of Analysis (COA)</option>\n                      <option value=\"tds\">Technical Data Sheet (TDS)</option>\n                      <option value=\"lab_report\">Laboratory Report</option>\n                    </select>\n                    <p className=\"mt-1 text-xs text-gray-500\">Select the type of document you want to create</p>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Document Number *</label>\n                    <div className=\"mt-1 flex rounded-md shadow-sm\">\n                      <input\n                        type=\"text\"\n                        className=\"flex-1 block w-full rounded-l-md border-gray-300 focus:border-green-500 focus:ring-green-500\"\n                        value={formData.document_number}\n                        onChange={(e) => setFormData({ ...formData, document_number: e.target.value })}\n                        placeholder=\"e.g., QS-20241211-001\"\n                      />\n                      <button\n                        type=\"button\"\n                        onClick={() => setFormData({ ...formData, document_number: generateDocumentNumber() })}\n                        className=\"inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 text-sm hover:bg-gray-100\"\n                      >\n                        <DocumentDuplicateIcon className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                    <p className=\"mt-1 text-xs text-gray-500\">Unique identifier for this document</p>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Document Title *</label>\n                    <input\n                      type=\"text\"\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                      value={formData.title}\n                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}\n                      placeholder=\"e.g., Vanilla Extract Quality Specification\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Version *</label>\n                    <input\n                      type=\"text\"\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                      value={formData.version}\n                      onChange={(e) => setFormData({ ...formData, version: e.target.value })}\n                      placeholder=\"e.g., 1.0, 2.1, 3.0\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Status</label>\n                    <select\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                      value={formData.status}\n                      onChange={(e) => setFormData({ ...formData, status: e.target.value as any })}\n                    >\n                      <option value=\"draft\">Draft</option>\n                      <option value=\"review\">Under Review</option>\n                      <option value=\"approved\">Approved</option>\n                      <option value=\"expired\">Expired</option>\n                    </select>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Valid From *</label>\n                    <input\n                      type=\"date\"\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                      value={formData.valid_from}\n                      onChange={(e) => setFormData({ ...formData, valid_from: e.target.value })}\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Valid Until</label>\n                    <input\n                      type=\"date\"\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                      value={formData.valid_until}\n                      onChange={(e) => setFormData({ ...formData, valid_until: e.target.value })}\n                    />\n                  </div>\n\n                  <div className=\"sm:col-span-2\">\n                    <label className=\"block text-sm font-medium text-gray-700\">Description *</label>\n                    <textarea\n                      rows={3}\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                      value={formData.description}\n                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n                      placeholder=\"Brief description of the document purpose and scope...\"\n                    />\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Step 2: Content & Description */}\n            {currentStep === 2 && (\n              <div className=\"space-y-6\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Document Content</h3>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Document Content *</label>\n                  <div className=\"mt-1\">\n                    <textarea\n                      rows={20}\n                      className=\"block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 font-mono text-sm\"\n                      value={formData.content}\n                      onChange={(e) => setFormData({ ...formData, content: e.target.value })}\n                      placeholder={`Enter the detailed content for your ${getDocumentTypeName(formData.document_type)}...\n\nExample content structure:\n1. Purpose and Scope\n2. Specifications\n3. Test Methods\n4. Acceptance Criteria\n5. References\n\nYou can use markdown formatting for better structure.`}\n                    />\n                  </div>\n                  <p className=\"mt-2 text-sm text-gray-500\">\n                    Enter the complete document content. You can use markdown formatting for headers, lists, and emphasis.\n                  </p>\n                </div>\n\n                {/* File Attachments */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Attachments</label>\n                  <div className=\"mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md\">\n                    <div className=\"space-y-1 text-center\">\n                      <CloudArrowUpIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                      <div className=\"flex text-sm text-gray-600\">\n                        <label className=\"relative cursor-pointer bg-white rounded-md font-medium text-green-600 hover:text-green-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-green-500\">\n                          <span>Upload files</span>\n                          <input\n                            type=\"file\"\n                            className=\"sr-only\"\n                            multiple\n                            onChange={(e) => handleFileUpload(e.target.files)}\n                          />\n                        </label>\n                        <p className=\"pl-1\">or drag and drop</p>\n                      </div>\n                      <p className=\"text-xs text-gray-500\">PDF, DOC, XLS, IMG up to 10MB each</p>\n                    </div>\n                  </div>\n\n                  {/* Attachment List */}\n                  {formData.attachments.length > 0 && (\n                    <div className=\"mt-4\">\n                      <h4 className=\"text-sm font-medium text-gray-900\">Attached Files:</h4>\n                      <ul className=\"mt-2 divide-y divide-gray-200\">\n                        {formData.attachments.map((file, index) => (\n                          <li key={index} className=\"py-2 flex items-center justify-between\">\n                            <div className=\"flex items-center\">\n                              <DocumentTextIcon className=\"h-5 w-5 text-gray-400 mr-2\" />\n                              <span className=\"text-sm text-gray-900\">{file.name}</span>\n                              <span className=\"text-xs text-gray-500 ml-2\">({(file.size / 1024 / 1024).toFixed(2)} MB)</span>\n                            </div>\n                            <button\n                              onClick={() => removeAttachment(index)}\n                              className=\"text-red-600 hover:text-red-800 text-sm\"\n                            >\n                              Remove\n                            </button>\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  )}\n                </div>\n              </div>\n            )}\n\n            {/* Step 3: Metadata & Classification */}\n            {currentStep === 3 && (\n              <div className=\"space-y-6\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Metadata & Classification</h3>\n\n                <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Author *</label>\n                    <div className=\"mt-1 relative\">\n                      <UserIcon className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                      <input\n                        type=\"text\"\n                        className=\"pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={formData.metadata.author}\n                        onChange={(e) => setFormData({\n                          ...formData,\n                          metadata: { ...formData.metadata, author: e.target.value }\n                        })}\n                        placeholder=\"Document author name\"\n                      />\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Department *</label>\n                    <div className=\"mt-1 relative\">\n                      <BuildingOfficeIcon className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                      <input\n                        type=\"text\"\n                        className=\"pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={formData.metadata.department}\n                        onChange={(e) => setFormData({\n                          ...formData,\n                          metadata: { ...formData.metadata, department: e.target.value }\n                        })}\n                        placeholder=\"e.g., Quality Assurance\"\n                      />\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Reviewer</label>\n                    <input\n                      type=\"text\"\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                      value={formData.metadata.reviewer}\n                      onChange={(e) => setFormData({\n                        ...formData,\n                        metadata: { ...formData.metadata, reviewer: e.target.value }\n                      })}\n                      placeholder=\"Document reviewer name\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Approver</label>\n                    <input\n                      type=\"text\"\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                      value={formData.metadata.approver}\n                      onChange={(e) => setFormData({\n                        ...formData,\n                        metadata: { ...formData.metadata, approver: e.target.value }\n                      })}\n                      placeholder=\"Document approver name\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Classification</label>\n                    <select\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                      value={formData.metadata.classification}\n                      onChange={(e) => setFormData({\n                        ...formData,\n                        metadata: { ...formData.metadata, classification: e.target.value as any }\n                      })}\n                    >\n                      <option value=\"public\">Public</option>\n                      <option value=\"internal\">Internal</option>\n                      <option value=\"confidential\">Confidential</option>\n                      <option value=\"restricted\">Restricted</option>\n                    </select>\n                  </div>\n\n                  <div className=\"sm:col-span-2\">\n                    <label className=\"block text-sm font-medium text-gray-700\">Keywords</label>\n                    <div className=\"mt-1\">\n                      <input\n                        type=\"text\"\n                        className=\"block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        placeholder=\"Type a keyword and press Enter\"\n                        onKeyPress={(e) => {\n                          if (e.key === 'Enter') {\n                            e.preventDefault()\n                            addKeyword(e.currentTarget.value)\n                            e.currentTarget.value = ''\n                          }\n                        }}\n                      />\n                    </div>\n                    <p className=\"mt-1 text-xs text-gray-500\">Press Enter to add keywords for better searchability</p>\n\n                    {/* Keywords Display */}\n                    {formData.metadata.keywords.length > 0 && (\n                      <div className=\"mt-3 flex flex-wrap gap-2\">\n                        {formData.metadata.keywords.map((keyword, index) => (\n                          <span\n                            key={index}\n                            className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\"\n                          >\n                            {keyword}\n                            <button\n                              type=\"button\"\n                              onClick={() => removeKeyword(index)}\n                              className=\"ml-1 text-green-600 hover:text-green-800\"\n                            >\n                              ×\n                            </button>\n                          </span>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Step 4: Review & Submit */}\n            {currentStep === 4 && (\n              <div className=\"space-y-6\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Review & Submit</h3>\n\n                {/* Document Summary */}\n                <div className=\"bg-gray-50 rounded-lg p-6\">\n                  <h4 className=\"text-lg font-medium text-gray-900 mb-4\">Document Summary</h4>\n                  <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                    <div>\n                      <span className=\"text-sm font-medium text-gray-500\">Type:</span>\n                      <p className=\"text-sm text-gray-900\">{getDocumentTypeName(formData.document_type)}</p>\n                    </div>\n                    <div>\n                      <span className=\"text-sm font-medium text-gray-500\">Document Number:</span>\n                      <p className=\"text-sm text-gray-900\">{formData.document_number}</p>\n                    </div>\n                    <div>\n                      <span className=\"text-sm font-medium text-gray-500\">Title:</span>\n                      <p className=\"text-sm text-gray-900\">{formData.title}</p>\n                    </div>\n                    <div>\n                      <span className=\"text-sm font-medium text-gray-500\">Version:</span>\n                      <p className=\"text-sm text-gray-900\">{formData.version}</p>\n                    </div>\n                    <div>\n                      <span className=\"text-sm font-medium text-gray-500\">Status:</span>\n                      <p className=\"text-sm text-gray-900 capitalize\">{formData.status}</p>\n                    </div>\n                    <div>\n                      <span className=\"text-sm font-medium text-gray-500\">Author:</span>\n                      <p className=\"text-sm text-gray-900\">{formData.metadata.author}</p>\n                    </div>\n                    <div>\n                      <span className=\"text-sm font-medium text-gray-500\">Valid From:</span>\n                      <p className=\"text-sm text-gray-900\">{formData.valid_from}</p>\n                    </div>\n                    <div>\n                      <span className=\"text-sm font-medium text-gray-500\">Valid Until:</span>\n                      <p className=\"text-sm text-gray-900\">{formData.valid_until || 'No expiration'}</p>\n                    </div>\n                    <div className=\"sm:col-span-2\">\n                      <span className=\"text-sm font-medium text-gray-500\">Description:</span>\n                      <p className=\"text-sm text-gray-900\">{formData.description}</p>\n                    </div>\n                    <div className=\"sm:col-span-2\">\n                      <span className=\"text-sm font-medium text-gray-500\">Content Length:</span>\n                      <p className=\"text-sm text-gray-900\">{formData.content.length} characters</p>\n                    </div>\n                    <div className=\"sm:col-span-2\">\n                      <span className=\"text-sm font-medium text-gray-500\">Attachments:</span>\n                      <p className=\"text-sm text-gray-900\">{formData.attachments.length} files</p>\n                    </div>\n                    {formData.metadata.keywords.length > 0 && (\n                      <div className=\"sm:col-span-2\">\n                        <span className=\"text-sm font-medium text-gray-500\">Keywords:</span>\n                        <p className=\"text-sm text-gray-900\">{formData.metadata.keywords.join(', ')}</p>\n                      </div>\n                    )}\n                  </div>\n                </div>\n\n                {/* Final Confirmation */}\n                <div className=\"bg-green-50 border border-green-200 rounded-md p-4\">\n                  <div className=\"flex\">\n                    <CheckCircleIcon className=\"h-5 w-5 text-green-400\" />\n                    <div className=\"ml-3\">\n                      <h3 className=\"text-sm font-medium text-green-800\">Ready to Create Document</h3>\n                      <div className=\"mt-2 text-sm text-green-700\">\n                        <p>\n                          Your document is ready to be created. Please review all information above and click \"Create Document\" to proceed.\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Navigation Buttons */}\n            <div className=\"flex justify-between pt-6 border-t border-gray-200\">\n              <button\n                type=\"button\"\n                onClick={() => {\n                  if (currentStep === 1) {\n                    router.push('/documents')\n                  } else {\n                    setCurrentStep(currentStep - 1)\n                  }\n                }}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n                disabled={isCreating}\n              >\n                {currentStep === 1 ? 'Cancel' : 'Previous'}\n              </button>\n\n              <div className=\"flex space-x-3\">\n                {currentStep < 4 ? (\n                  <button\n                    type=\"button\"\n                    onClick={() => setCurrentStep(currentStep + 1)}\n                    disabled={\n                      (currentStep === 1 && (!formData.title || !formData.document_number || !formData.description)) ||\n                      (currentStep === 2 && !formData.content) ||\n                      (currentStep === 3 && (!formData.metadata.author || !formData.metadata.department))\n                    }\n                    className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400\"\n                  >\n                    Next Step\n                  </button>\n                ) : (\n                  <button\n                    type=\"button\"\n                    onClick={handleSubmit}\n                    disabled={isCreating}\n                    className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400\"\n                  >\n                    {isCreating ? (\n                      <>\n                        <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\n                          <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                          <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                        </svg>\n                        Creating Document...\n                      </>\n                    ) : (\n                      <>\n                        <CheckCircleIcon className=\"-ml-1 mr-2 h-4 w-4\" />\n                        Create Document\n                      </>\n                    )}\n                  </button>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </MainLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;;;AAnBA;;;;;;;AA4Ce,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QACzD,eAAe;QACf,OAAO;QACP,iBAAiB;QACjB,SAAS;QACT,SAAS;QACT,UAAU;QACV,QAAQ;QACR,YAAY,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAClD,aAAa;QACb,aAAa;QACb,SAAS;QACT,aAAa,EAAE;QACf,UAAU;YACR,QAAQ,MAAM,aAAa;YAC3B,YAAY,MAAM,cAAc;YAChC,UAAU;YACV,UAAU;YACV,UAAU,EAAE;YACZ,gBAAgB;QAClB;IACF;IAEA,gCAAgC;IAChC,MAAM,yBAAyB;QAC7B,MAAM,WAAW;YACf,QAAQ;YACR,OAAO;YACP,OAAO;YACP,gBAAgB;YAChB,cAAc;QAChB,CAAC,CAAC,SAAS,aAAa,CAAC;QAEzB,MAAM,OAAO,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;QACjE,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG;QACzE,OAAO,GAAG,SAAS,CAAC,EAAE,KAAK,CAAC,EAAE,UAAU;IAC1C;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,CAAC,SAAS,eAAe,EAAE;gBAC7B;iDAAY,CAAA,OAAQ,CAAC;4BACnB,GAAG,IAAI;4BACP,iBAAiB;wBACnB,CAAC;;YACH;QACF;oCAAG;QAAC,SAAS,aAAa;KAAC;IAE3B,sBAAsB;IACtB,MAAM,eAAe;QACnB,MAAM,SAAmB,EAAE;QAE3B,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC;QACxC,IAAI,CAAC,SAAS,eAAe,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC;QAClD,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC;QAC1C,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC;QAC9C,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC;QAC1C,IAAI,CAAC,SAAS,UAAU,EAAE,OAAO,IAAI,CAAC;QACtC,IAAI,CAAC,SAAS,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC;QAClD,IAAI,CAAC,SAAS,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC;QAEtD,IAAI,SAAS,WAAW,IAAI,IAAI,KAAK,SAAS,WAAW,KAAK,IAAI,KAAK,SAAS,UAAU,GAAG;YAC3F,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,SAAS,KAAK,CAAC,MAAM,GAAG,GAAG,OAAO,IAAI,CAAC;QAC3C,IAAI,SAAS,OAAO,CAAC,MAAM,GAAG,IAAI,OAAO,IAAI,CAAC;QAE9C,oBAAoB;QACpB,OAAO,OAAO,MAAM,KAAK;IAC3B;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,cAAc;YAEd,MAAM,eAAe;gBACnB,GAAG,QAAQ;gBACX,YAAY,MAAM,MAAM;gBACxB,UAAU,KAAK,SAAS,CAAC,SAAS,QAAQ;YAC5C;YAEA,MAAM,kBAAkB,MAAM,yHAAA,CAAA,kBAAe,CAAC,qBAAqB,CAAC;YAEpE,IAAI,iBAAiB;gBACnB,MAAM,CAAC,YAAY,EAAE,SAAS,KAAK,CAAC,2DAA2D,EAAE,SAAS,eAAe,CAAC,UAAU,EAAE,oBAAoB,SAAS,aAAa,EAAE,aAAa,EAAE,SAAS,OAAO,CAAC,YAAY,EAAE,SAAS,MAAM,CAAC,WAAW,GAAG,YAAY,EAAE,SAAS,QAAQ,CAAC,MAAM,EAAE;gBACtS,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,QAAQ;YACZ,QAAQ;YACR,OAAO;YACP,OAAO;YACP,gBAAgB;YAChB,cAAc;QAChB;QACA,OAAO,KAAK,CAAC,KAA2B,IAAI;IAC9C;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,OAAO;YACT,MAAM,WAAW,MAAM,IAAI,CAAC;YAC5B,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,aAAa;2BAAI,KAAK,WAAW;2BAAK;qBAAS;gBACjD,CAAC;QACH;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,aAAa,KAAK,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YACvD,CAAC;IACH;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,QAAQ,IAAI,MAAM,CAAC,SAAS,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,IAAI,KAAK;YAC1E,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,UAAU;wBACR,GAAG,KAAK,QAAQ;wBAChB,UAAU;+BAAI,KAAK,QAAQ,CAAC,QAAQ;4BAAE,QAAQ,IAAI;yBAAG;oBACvD;gBACF,CAAC;QACH;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,UAAU;oBACR,GAAG,KAAK,QAAQ;oBAChB,UAAU,KAAK,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;gBAC1D;YACF,CAAC;IACH;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC,iJAAA,CAAA,aAAU;kBACT,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,WAAU;;sDAEV,6LAAC,4NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAG5C,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;sCAK9C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,eAAe,CAAC;gCAC/B,WAAU;;kDAEV,6LAAC,gNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAClB,cAAc,cAAc;;;;;;;;;;;;;;;;;;8BAMnC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,qBACjB,6LAAC;gCAAe,WAAU;;kDACxB,6LAAC;wCAAI,WAAW,CAAC,+DAA+D,EAC9E,QAAQ,cACJ,6CACA,iCACJ;kDACC,OAAO,4BACN,6LAAC,gOAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;iEAE3B,6LAAC;4CAAK,WAAU;sDAAuB;;;;;;;;;;;kDAG3C,6LAAC;wCAAI,WAAU;;4CACZ,SAAS,KAAK;4CACd,SAAS,KAAK;4CACd,SAAS,KAAK;4CACd,SAAS,KAAK;;;;;;;oCAEhB,OAAO,mBACN,6LAAC;wCAAI,WAAW,CAAC,gBAAgB,EAC/B,OAAO,cAAc,iBAAiB,eACtC;;;;;;;+BArBI;;;;;;;;;;;;;;;gBA6Bf,iBAAiB,MAAM,GAAG,mBACzB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gPAAA,CAAA,0BAAuB;gCAAC,WAAU;;;;;;0CACnC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAG,WAAU;kDACX,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,6LAAC;0DAAgB;+CAAR;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASrB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BAEZ,gBAAgB,mBACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAElD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA0C;;;;;;kEAC3D,6LAAC;wDACC,WAAU;wDACV,OAAO,SAAS,aAAa;wDAC7B,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,eAAe,EAAE,MAAM,CAAC,KAAK;4DAAQ;;0EAEjF,6LAAC;gEAAO,OAAM;0EAAe;;;;;;0EAC7B,6LAAC;gEAAO,OAAM;0EAAO;;;;;;0EACrB,6LAAC;gEAAO,OAAM;0EAAM;;;;;;0EACpB,6LAAC;gEAAO,OAAM;0EAAM;;;;;;0EACpB,6LAAC;gEAAO,OAAM;0EAAa;;;;;;;;;;;;kEAE7B,6LAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;0DAG5C,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA0C;;;;;;kEAC3D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,MAAK;gEACL,WAAU;gEACV,OAAO,SAAS,eAAe;gEAC/B,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;oEAAC;gEAC5E,aAAY;;;;;;0EAEd,6LAAC;gEACC,MAAK;gEACL,SAAS,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,iBAAiB;oEAAyB;gEACpF,WAAU;0EAEV,cAAA,6LAAC,4OAAA,CAAA,wBAAqB;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAGrC,6LAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;0DAG5C,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA0C;;;;;;kEAC3D,6LAAC;wDACC,MAAK;wDACL,WAAU;wDACV,OAAO,SAAS,KAAK;wDACrB,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAClE,aAAY;;;;;;;;;;;;0DAIhB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA0C;;;;;;kEAC3D,6LAAC;wDACC,MAAK;wDACL,WAAU;wDACV,OAAO,SAAS,OAAO;wDACvB,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACpE,aAAY;;;;;;;;;;;;0DAIhB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA0C;;;;;;kEAC3D,6LAAC;wDACC,WAAU;wDACV,OAAO,SAAS,MAAM;wDACtB,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4DAAQ;;0EAE1E,6LAAC;gEAAO,OAAM;0EAAQ;;;;;;0EACtB,6LAAC;gEAAO,OAAM;0EAAS;;;;;;0EACvB,6LAAC;gEAAO,OAAM;0EAAW;;;;;;0EACzB,6LAAC;gEAAO,OAAM;0EAAU;;;;;;;;;;;;;;;;;;0DAI5B,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA0C;;;;;;kEAC3D,6LAAC;wDACC,MAAK;wDACL,WAAU;wDACV,OAAO,SAAS,UAAU;wDAC1B,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,YAAY,EAAE,MAAM,CAAC,KAAK;4DAAC;;;;;;;;;;;;0DAI3E,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA0C;;;;;;kEAC3D,6LAAC;wDACC,MAAK;wDACL,WAAU;wDACV,OAAO,SAAS,WAAW;wDAC3B,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC;;;;;;;;;;;;0DAI5E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAA0C;;;;;;kEAC3D,6LAAC;wDACC,MAAM;wDACN,WAAU;wDACV,OAAO,SAAS,WAAW;wDAC3B,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACxE,aAAY;;;;;;;;;;;;;;;;;;;;;;;;4BAQrB,gBAAgB,mBACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAElD,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAC3D,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,MAAM;oDACN,WAAU;oDACV,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACpE,aAAa,CAAC,oCAAoC,EAAE,oBAAoB,SAAS,aAAa,EAAE;;;;;;;;;qDASjE,CAAC;;;;;;;;;;;0DAGpC,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;kDAM5C,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAC3D,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,kOAAA,CAAA,mBAAgB;4DAAC,WAAU;;;;;;sEAC5B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAM,WAAU;;sFACf,6LAAC;sFAAK;;;;;;sFACN,6LAAC;4EACC,MAAK;4EACL,WAAU;4EACV,QAAQ;4EACR,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8EAGpD,6LAAC;oEAAE,WAAU;8EAAO;;;;;;;;;;;;sEAEtB,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;4CAKxC,SAAS,WAAW,CAAC,MAAM,GAAG,mBAC7B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,6LAAC;wDAAG,WAAU;kEACX,SAAS,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC/B,6LAAC;gEAAe,WAAU;;kFACxB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,kOAAA,CAAA,mBAAgB;gFAAC,WAAU;;;;;;0FAC5B,6LAAC;gFAAK,WAAU;0FAAyB,KAAK,IAAI;;;;;;0FAClD,6LAAC;gFAAK,WAAU;;oFAA6B;oFAAE,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;oFAAG;;;;;;;;;;;;;kFAEtF,6LAAC;wEACC,SAAS,IAAM,iBAAiB;wEAChC,WAAU;kFACX;;;;;;;+DATM;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAsBtB,gBAAgB,mBACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAElD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA0C;;;;;;kEAC3D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,kNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;gEACC,MAAK;gEACL,WAAU;gEACV,OAAO,SAAS,QAAQ,CAAC,MAAM;gEAC/B,UAAU,CAAC,IAAM,YAAY;wEAC3B,GAAG,QAAQ;wEACX,UAAU;4EAAE,GAAG,SAAS,QAAQ;4EAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wEAAC;oEAC3D;gEACA,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA0C;;;;;;kEAC3D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,sOAAA,CAAA,qBAAkB;gEAAC,WAAU;;;;;;0EAC9B,6LAAC;gEACC,MAAK;gEACL,WAAU;gEACV,OAAO,SAAS,QAAQ,CAAC,UAAU;gEACnC,UAAU,CAAC,IAAM,YAAY;wEAC3B,GAAG,QAAQ;wEACX,UAAU;4EAAE,GAAG,SAAS,QAAQ;4EAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wEAAC;oEAC/D;gEACA,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA0C;;;;;;kEAC3D,6LAAC;wDACC,MAAK;wDACL,WAAU;wDACV,OAAO,SAAS,QAAQ,CAAC,QAAQ;wDACjC,UAAU,CAAC,IAAM,YAAY;gEAC3B,GAAG,QAAQ;gEACX,UAAU;oEAAE,GAAG,SAAS,QAAQ;oEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gEAAC;4DAC7D;wDACA,aAAY;;;;;;;;;;;;0DAIhB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA0C;;;;;;kEAC3D,6LAAC;wDACC,MAAK;wDACL,WAAU;wDACV,OAAO,SAAS,QAAQ,CAAC,QAAQ;wDACjC,UAAU,CAAC,IAAM,YAAY;gEAC3B,GAAG,QAAQ;gEACX,UAAU;oEAAE,GAAG,SAAS,QAAQ;oEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gEAAC;4DAC7D;wDACA,aAAY;;;;;;;;;;;;0DAIhB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA0C;;;;;;kEAC3D,6LAAC;wDACC,WAAU;wDACV,OAAO,SAAS,QAAQ,CAAC,cAAc;wDACvC,UAAU,CAAC,IAAM,YAAY;gEAC3B,GAAG,QAAQ;gEACX,UAAU;oEAAE,GAAG,SAAS,QAAQ;oEAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;gEAAQ;4DAC1E;;0EAEA,6LAAC;gEAAO,OAAM;0EAAS;;;;;;0EACvB,6LAAC;gEAAO,OAAM;0EAAW;;;;;;0EACzB,6LAAC;gEAAO,OAAM;0EAAe;;;;;;0EAC7B,6LAAC;gEAAO,OAAM;0EAAa;;;;;;;;;;;;;;;;;;0DAI/B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAA0C;;;;;;kEAC3D,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,MAAK;4DACL,WAAU;4DACV,aAAY;4DACZ,YAAY,CAAC;gEACX,IAAI,EAAE,GAAG,KAAK,SAAS;oEACrB,EAAE,cAAc;oEAChB,WAAW,EAAE,aAAa,CAAC,KAAK;oEAChC,EAAE,aAAa,CAAC,KAAK,GAAG;gEAC1B;4DACF;;;;;;;;;;;kEAGJ,6LAAC;wDAAE,WAAU;kEAA6B;;;;;;oDAGzC,SAAS,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,mBACnC,6LAAC;wDAAI,WAAU;kEACZ,SAAS,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACxC,6LAAC;gEAEC,WAAU;;oEAET;kFACD,6LAAC;wEACC,MAAK;wEACL,SAAS,IAAM,cAAc;wEAC7B,WAAU;kFACX;;;;;;;+DARI;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAqBpB,gBAAgB,mBACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAGlD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAoC;;;;;;0EACpD,6LAAC;gEAAE,WAAU;0EAAyB,oBAAoB,SAAS,aAAa;;;;;;;;;;;;kEAElF,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAoC;;;;;;0EACpD,6LAAC;gEAAE,WAAU;0EAAyB,SAAS,eAAe;;;;;;;;;;;;kEAEhE,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAoC;;;;;;0EACpD,6LAAC;gEAAE,WAAU;0EAAyB,SAAS,KAAK;;;;;;;;;;;;kEAEtD,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAoC;;;;;;0EACpD,6LAAC;gEAAE,WAAU;0EAAyB,SAAS,OAAO;;;;;;;;;;;;kEAExD,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAoC;;;;;;0EACpD,6LAAC;gEAAE,WAAU;0EAAoC,SAAS,MAAM;;;;;;;;;;;;kEAElE,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAoC;;;;;;0EACpD,6LAAC;gEAAE,WAAU;0EAAyB,SAAS,QAAQ,CAAC,MAAM;;;;;;;;;;;;kEAEhE,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAoC;;;;;;0EACpD,6LAAC;gEAAE,WAAU;0EAAyB,SAAS,UAAU;;;;;;;;;;;;kEAE3D,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAoC;;;;;;0EACpD,6LAAC;gEAAE,WAAU;0EAAyB,SAAS,WAAW,IAAI;;;;;;;;;;;;kEAEhE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAoC;;;;;;0EACpD,6LAAC;gEAAE,WAAU;0EAAyB,SAAS,WAAW;;;;;;;;;;;;kEAE5D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAoC;;;;;;0EACpD,6LAAC;gEAAE,WAAU;;oEAAyB,SAAS,OAAO,CAAC,MAAM;oEAAC;;;;;;;;;;;;;kEAEhE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAoC;;;;;;0EACpD,6LAAC;gEAAE,WAAU;;oEAAyB,SAAS,WAAW,CAAC,MAAM;oEAAC;;;;;;;;;;;;;oDAEnE,SAAS,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,mBACnC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAoC;;;;;;0EACpD,6LAAC;gEAAE,WAAU;0EAAyB,SAAS,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;kDAO9E,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gOAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;;8DAC3B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;0EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS;4CACP,IAAI,gBAAgB,GAAG;gDACrB,OAAO,IAAI,CAAC;4CACd,OAAO;gDACL,eAAe,cAAc;4CAC/B;wCACF;wCACA,WAAU;wCACV,UAAU;kDAET,gBAAgB,IAAI,WAAW;;;;;;kDAGlC,6LAAC;wCAAI,WAAU;kDACZ,cAAc,kBACb,6LAAC;4CACC,MAAK;4CACL,SAAS,IAAM,eAAe,cAAc;4CAC5C,UACE,AAAC,gBAAgB,KAAK,CAAC,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,eAAe,IAAI,CAAC,SAAS,WAAW,KAC3F,gBAAgB,KAAK,CAAC,SAAS,OAAO,IACtC,gBAAgB,KAAK,CAAC,CAAC,SAAS,QAAQ,CAAC,MAAM,IAAI,CAAC,SAAS,QAAQ,CAAC,UAAU;4CAEnF,WAAU;sDACX;;;;;iEAID,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU;4CACV,WAAU;sDAET,2BACC;;kEACE,6LAAC;wDAAI,WAAU;wDAA6C,MAAK;wDAAO,SAAQ;;0EAC9E,6LAAC;gEAAO,WAAU;gEAAa,IAAG;gEAAK,IAAG;gEAAK,GAAE;gEAAK,QAAO;gEAAe,aAAY;;;;;;0EACxF,6LAAC;gEAAK,WAAU;gEAAa,MAAK;gEAAe,GAAE;;;;;;;;;;;;oDAC/C;;6EAIR;;kEACE,6LAAC,gOAAA,CAAA,kBAAe;wDAAC,WAAU;;;;;;oDAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa1E;GA/rBwB;;QACL,kIAAA,CAAA,UAAO;QACT,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}