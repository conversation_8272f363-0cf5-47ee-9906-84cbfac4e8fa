const { app, shell, dialog } = require('electron')

// Arabic menu template for the SFF Management System
const createMenuTemplate = (mainWindow) => {
  return [
    {
      label: 'ملف',
      submenu: [
        {
          label: 'مشروع جديد',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('menu-new-project')
          }
        },
        {
          label: 'فتح مشروع',
          accelerator: 'CmdOrCtrl+O',
          click: async () => {
            const result = await dialog.showOpenDialog(mainWindow, {
              title: 'فتح مشروع',
              properties: ['openFile'],
              filters: [
                { name: 'ملفات SFF', extensions: ['sff'] },
                { name: 'جميع الملفات', extensions: ['*'] }
              ]
            })
            
            if (!result.canceled) {
              mainWindow.webContents.send('menu-open-project', result.filePaths[0])
            }
          }
        },
        { type: 'separator' },
        {
          label: 'حفظ',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            mainWindow.webContents.send('menu-save')
          }
        },
        {
          label: 'حفظ باسم',
          accelerator: 'CmdOrCtrl+Shift+S',
          click: async () => {
            const result = await dialog.showSaveDialog(mainWindow, {
              title: 'حفظ باسم',
              defaultPath: 'sff-project.sff',
              filters: [
                { name: 'ملفات SFF', extensions: ['sff'] },
                { name: 'جميع الملفات', extensions: ['*'] }
              ]
            })
            
            if (!result.canceled) {
              mainWindow.webContents.send('menu-save-as', result.filePath)
            }
          }
        },
        { type: 'separator' },
        {
          label: 'تصدير البيانات',
          submenu: [
            {
              label: 'تصدير إلى Excel',
              click: () => {
                mainWindow.webContents.send('menu-export-excel')
              }
            },
            {
              label: 'تصدير إلى PDF',
              click: () => {
                mainWindow.webContents.send('menu-export-pdf')
              }
            },
            {
              label: 'تصدير إلى CSV',
              click: () => {
                mainWindow.webContents.send('menu-export-csv')
              }
            }
          ]
        },
        {
          label: 'استيراد البيانات',
          click: () => {
            mainWindow.webContents.send('menu-import-data')
          }
        },
        { type: 'separator' },
        {
          label: 'طباعة',
          accelerator: 'CmdOrCtrl+P',
          click: () => {
            mainWindow.webContents.print()
          }
        },
        { type: 'separator' },
        {
          label: 'خروج',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: 'تحرير',
      submenu: [
        { label: 'تراجع', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: 'إعادة', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: 'قص', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: 'نسخ', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: 'لصق', accelerator: 'CmdOrCtrl+V', role: 'paste' },
        { label: 'تحديد الكل', accelerator: 'CmdOrCtrl+A', role: 'selectAll' },
        { type: 'separator' },
        {
          label: 'بحث',
          accelerator: 'CmdOrCtrl+F',
          click: () => {
            mainWindow.webContents.send('menu-search')
          }
        },
        {
          label: 'بحث واستبدال',
          accelerator: 'CmdOrCtrl+H',
          click: () => {
            mainWindow.webContents.send('menu-find-replace')
          }
        }
      ]
    },
    {
      label: 'عرض',
      submenu: [
        { label: 'إعادة تحميل', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: 'فرض إعادة التحميل', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
        { label: 'أدوات المطور', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: 'الحجم الفعلي', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
        { label: 'تكبير', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
        { label: 'تصغير', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
        { type: 'separator' },
        { label: 'ملء الشاشة', accelerator: 'F11', role: 'togglefullscreen' },
        { type: 'separator' },
        {
          label: 'لوحة التحكم',
          accelerator: 'CmdOrCtrl+1',
          click: () => {
            mainWindow.webContents.send('navigate-to', '/dashboard')
          }
        },
        {
          label: 'إدارة المخزون',
          accelerator: 'CmdOrCtrl+2',
          click: () => {
            mainWindow.webContents.send('navigate-to', '/inventory')
          }
        },
        {
          label: 'إدارة الوصفات',
          accelerator: 'CmdOrCtrl+3',
          click: () => {
            mainWindow.webContents.send('navigate-to', '/recipes')
          }
        },
        {
          label: 'إدارة الإنتاج',
          accelerator: 'CmdOrCtrl+4',
          click: () => {
            mainWindow.webContents.send('navigate-to', '/production')
          }
        },
        {
          label: 'إدارة الجودة',
          accelerator: 'CmdOrCtrl+5',
          click: () => {
            mainWindow.webContents.send('navigate-to', '/documents')
          }
        }
      ]
    },
    {
      label: 'أدوات',
      submenu: [
        {
          label: 'نسخة احتياطية',
          click: () => {
            mainWindow.webContents.send('menu-backup')
          }
        },
        {
          label: 'استعادة من نسخة احتياطية',
          click: () => {
            mainWindow.webContents.send('menu-restore')
          }
        },
        { type: 'separator' },
        {
          label: 'إعدادات',
          accelerator: 'CmdOrCtrl+,',
          click: () => {
            mainWindow.webContents.send('menu-settings')
          }
        },
        {
          label: 'تحديث قاعدة البيانات',
          click: () => {
            mainWindow.webContents.send('menu-update-database')
          }
        }
      ]
    },
    {
      label: 'نافذة',
      submenu: [
        { label: 'تصغير', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
        { label: 'إغلاق', accelerator: 'CmdOrCtrl+W', role: 'close' },
        { type: 'separator' },
        {
          label: 'إحضار الكل للمقدمة',
          role: 'front'
        }
      ]
    },
    {
      label: 'مساعدة',
      submenu: [
        {
          label: 'دليل المستخدم',
          click: () => {
            shell.openExternal('https://docs.sff-management.com')
          }
        },
        {
          label: 'اختصارات لوحة المفاتيح',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'اختصارات لوحة المفاتيح',
              message: 'اختصارات مفيدة',
              detail: `
الاختصارات العامة:
Ctrl+N - مشروع جديد
Ctrl+O - فتح مشروع
Ctrl+S - حفظ
Ctrl+P - طباعة
Ctrl+Q - خروج

اختصارات التنقل:
Ctrl+1 - لوحة التحكم
Ctrl+2 - إدارة المخزون
Ctrl+3 - إدارة الوصفات
Ctrl+4 - إدارة الإنتاج
Ctrl+5 - إدارة الجودة

اختصارات العرض:
F11 - ملء الشاشة
F12 - أدوات المطور
Ctrl+Plus - تكبير
Ctrl+Minus - تصغير
              `,
              buttons: ['موافق']
            })
          }
        },
        { type: 'separator' },
        {
          label: 'تحقق من التحديثات',
          click: () => {
            mainWindow.webContents.send('menu-check-updates')
          }
        },
        {
          label: 'الإبلاغ عن مشكلة',
          click: () => {
            shell.openExternal('https://github.com/sff-management/issues')
          }
        },
        { type: 'separator' },
        {
          label: 'حول SFF Management',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'حول التطبيق',
              message: 'SFF Production & Quality Management System',
              detail: `نظام إدارة الإنتاج والجودة المتقدم
الإصدار: ${app.getVersion()}
تطبيق سطح مكتب

المطور: فريق SFF
الموقع: https://sff-management.com
البريد الإلكتروني: <EMAIL>

حقوق الطبع والنشر © 2024 SFF Company
جميع الحقوق محفوظة`,
              buttons: ['موافق']
            })
          }
        }
      ]
    }
  ]
}

module.exports = { createMenuTemplate }
