{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\nimport {\n  HomeIcon,\n  CubeIcon,\n  BeakerIcon,\n  CogIcon,\n  DocumentTextIcon,\n  UsersIcon,\n  ChartBarIcon,\n  ClipboardDocumentListIcon,\n} from '@heroicons/react/24/outline'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { clsx } from 'clsx'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/', icon: HomeIcon },\n  { name: 'Inventory', href: '/inventory', icon: CubeIcon },\n  { name: 'Recipes', href: '/recipes', icon: ClipboardDocumentListIcon },\n  { name: 'Production', href: '/production', icon: CogIcon },\n  { name: 'Quality', href: '/quality', icon: BeakerIcon },\n  { name: 'Reports', href: '/reports', icon: ChartBarIcon },\n  { name: 'Documents', href: '/documents', icon: DocumentTextIcon },\n  { name: 'Users', href: '/users', icon: UsersIcon },\n]\n\ninterface SidebarProps {\n  open: boolean\n  setOpen: (open: boolean) => void\n}\n\nexport function Sidebar({ open, setOpen }: SidebarProps) {\n  const pathname = usePathname()\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <Transition.Root show={open} as={Fragment}>\n        <Dialog as=\"div\" className=\"relative z-50 lg:hidden\" onClose={setOpen}>\n          <Transition.Child\n            as={Fragment}\n            enter=\"transition-opacity ease-linear duration-300\"\n            enterFrom=\"opacity-0\"\n            enterTo=\"opacity-100\"\n            leave=\"transition-opacity ease-linear duration-300\"\n            leaveFrom=\"opacity-100\"\n            leaveTo=\"opacity-0\"\n          >\n            <div className=\"fixed inset-0 bg-gray-900/80\" />\n          </Transition.Child>\n\n          <div className=\"fixed inset-0 flex\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"transition ease-in-out duration-300 transform\"\n              enterFrom=\"-translate-x-full\"\n              enterTo=\"translate-x-0\"\n              leave=\"transition ease-in-out duration-300 transform\"\n              leaveFrom=\"translate-x-0\"\n              leaveTo=\"-translate-x-full\"\n            >\n              <Dialog.Panel className=\"relative mr-16 flex w-full max-w-xs flex-1\">\n                <Transition.Child\n                  as={Fragment}\n                  enter=\"ease-in-out duration-300\"\n                  enterFrom=\"opacity-0\"\n                  enterTo=\"opacity-100\"\n                  leave=\"ease-in-out duration-300\"\n                  leaveFrom=\"opacity-100\"\n                  leaveTo=\"opacity-0\"\n                >\n                  <div className=\"absolute left-full top-0 flex w-16 justify-center pt-5\">\n                    <button type=\"button\" className=\"-m-2.5 p-2.5\" onClick={() => setOpen(false)}>\n                      <span className=\"sr-only\">Close sidebar</span>\n                      <XMarkIcon className=\"h-6 w-6 text-white\" aria-hidden=\"true\" />\n                    </button>\n                  </div>\n                </Transition.Child>\n                <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4\">\n                  <div className=\"flex h-16 shrink-0 items-center\">\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">SFF</span>\n                    </div>\n                    <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n                  </div>\n                  <nav className=\"flex flex-1 flex-col\">\n                    <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n                      <li>\n                        <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                          {navigation.map((item) => (\n                            <li key={item.name}>\n                              <Link\n                                href={item.href}\n                                className={clsx(\n                                  pathname === item.href\n                                    ? 'bg-green-50 text-green-700'\n                                    : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                                  'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                                )}\n                              >\n                                <item.icon\n                                  className={clsx(\n                                    pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                                    'h-6 w-6 shrink-0'\n                                  )}\n                                  aria-hidden=\"true\"\n                                />\n                                {item.name}\n                              </Link>\n                            </li>\n                          ))}\n                        </ul>\n                      </li>\n                    </ul>\n                  </nav>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </Dialog>\n      </Transition.Root>\n\n      {/* Static sidebar for desktop */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 bg-white px-6 pb-4\">\n          <div className=\"flex h-16 shrink-0 items-center\">\n            <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-sm\">SFF</span>\n            </div>\n            <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n          </div>\n          <nav className=\"flex flex-1 flex-col\">\n            <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n              <li>\n                <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                  {navigation.map((item) => (\n                    <li key={item.name}>\n                      <Link\n                        href={item.href}\n                        className={clsx(\n                          pathname === item.href\n                            ? 'bg-green-50 text-green-700'\n                            : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                          'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                        )}\n                      >\n                        <item.icon\n                          className={clsx(\n                            pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                            'h-6 w-6 shrink-0'\n                          )}\n                          aria-hidden=\"true\"\n                        />\n                        {item.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </li>\n            </ul>\n          </nav>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AAjBA;;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,+MAAA,CAAA,WAAQ;IAAC;IAC/C;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,+MAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,iPAAA,CAAA,4BAAyB;IAAC;IACrE;QAAE,MAAM;QAAc,MAAM;QAAe,MAAM,6MAAA,CAAA,UAAO;IAAC;IACzD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,mNAAA,CAAA,aAAU;IAAC;IACtD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,uNAAA,CAAA,eAAY;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,+NAAA,CAAA,mBAAgB;IAAC;IAChE;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,iNAAA,CAAA,YAAS;IAAC;CAClD;AAOM,SAAS,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAgB;IACrD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE;;0BAEE,8OAAC,uLAAA,CAAA,aAAU,CAAC,IAAI;gBAAC,MAAM;gBAAM,IAAI,qMAAA,CAAA,WAAQ;0BACvC,cAAA,8OAAC,+KAAA,CAAA,SAAM;oBAAC,IAAG;oBAAM,WAAU;oBAA0B,SAAS;;sCAC5D,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,qMAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;gCACf,IAAI,qMAAA,CAAA,WAAQ;gCACZ,OAAM;gCACN,WAAU;gCACV,SAAQ;gCACR,OAAM;gCACN,WAAU;gCACV,SAAQ;0CAER,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;oCAAC,WAAU;;sDACtB,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;4CACf,IAAI,qMAAA,CAAA,WAAQ;4CACZ,OAAM;4CACN,WAAU;4CACV,SAAQ;4CACR,OAAM;4CACN,WAAU;4CACV,SAAQ;sDAER,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAO,MAAK;oDAAS,WAAU;oDAAe,SAAS,IAAM,QAAQ;;sEACpE,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC,iNAAA,CAAA,YAAS;4DAAC,WAAU;4DAAqB,eAAY;;;;;;;;;;;;;;;;;;;;;;sDAI5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,8OAAC;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;8DAE7D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAG,MAAK;wDAAO,WAAU;kEACxB,cAAA,8OAAC;sEACC,cAAA,8OAAC;gEAAG,MAAK;gEAAO,WAAU;0EACvB,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;kFACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4EACH,MAAM,KAAK,IAAI;4EACf,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;8FAGF,8OAAC,KAAK,IAAI;oFACR,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;oFAEF,eAAY;;;;;;gFAEb,KAAK,IAAI;;;;;;;uEAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiCxC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAE7D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,MAAK;gCAAO,WAAU;0CACxB,cAAA,8OAAC;8CACC,cAAA,8OAAC;wCAAG,MAAK;wCAAO,WAAU;kDACvB,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;sEAGF,8OAAC,KAAK,IAAI;4DACR,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;4DAEF,eAAY;;;;;;wDAEb,KAAK,IAAI;;;;;;;+CAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BtC", "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Menu, Transition } from '@headlessui/react'\nimport { Bars3Icon, BellIcon } from '@heroicons/react/24/outline'\nimport { useAuth } from '@/components/providers'\nimport { clsx } from 'clsx'\n\ninterface HeaderProps {\n  setSidebarOpen: (open: boolean) => void\n}\n\nexport function Header({ setSidebarOpen }: HeaderProps) {\n  const { user, signOut } = useAuth()\n\n  const userNavigation = [\n    { name: 'Your profile', href: '#' },\n    { name: 'Settings', href: '#' },\n    { name: 'Sign out', href: '#', onClick: signOut },\n  ]\n\n  return (\n    <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n      <button\n        type=\"button\"\n        className=\"-m-2.5 p-2.5 text-gray-700 lg:hidden\"\n        onClick={() => setSidebarOpen(true)}\n      >\n        <span className=\"sr-only\">Open sidebar</span>\n        <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n      </button>\n\n      {/* Separator */}\n      <div className=\"h-6 w-px bg-gray-200 lg:hidden\" aria-hidden=\"true\" />\n\n      <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n        <div className=\"relative flex flex-1\">\n          {/* Search can be added here later */}\n        </div>\n        <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n          <button type=\"button\" className=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\">\n            <span className=\"sr-only\">View notifications</span>\n            <BellIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n\n          {/* Separator */}\n          <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200\" aria-hidden=\"true\" />\n\n          {/* Profile dropdown */}\n          <Menu as=\"div\" className=\"relative\">\n            <Menu.Button className=\"-m-1.5 flex items-center p-1.5\">\n              <span className=\"sr-only\">Open user menu</span>\n              <div className=\"h-8 w-8 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.username?.charAt(0).toUpperCase() || 'U'}\n                </span>\n              </div>\n              <span className=\"hidden lg:flex lg:items-center\">\n                <span className=\"ml-4 text-sm font-semibold leading-6 text-gray-900\" aria-hidden=\"true\">\n                  {user?.username || 'User'}\n                </span>\n                <span className=\"ml-2 text-xs text-gray-500 capitalize\">\n                  ({user?.role?.replace('_', ' ') || 'Role'})\n                </span>\n              </span>\n            </Menu.Button>\n            <Transition\n              as={Fragment}\n              enter=\"transition ease-out duration-100\"\n              enterFrom=\"transform opacity-0 scale-95\"\n              enterTo=\"transform opacity-100 scale-100\"\n              leave=\"transition ease-in duration-75\"\n              leaveFrom=\"transform opacity-100 scale-100\"\n              leaveTo=\"transform opacity-0 scale-95\"\n            >\n              <Menu.Items className=\"absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none\">\n                {userNavigation.map((item) => (\n                  <Menu.Item key={item.name}>\n                    {({ active }) => (\n                      <button\n                        onClick={item.onClick}\n                        className={clsx(\n                          active ? 'bg-gray-50' : '',\n                          'block w-full text-left px-3 py-1 text-sm leading-6 text-gray-900'\n                        )}\n                      >\n                        {item.name}\n                      </button>\n                    )}\n                  </Menu.Item>\n                ))}\n              </Menu.Items>\n            </Transition>\n          </Menu>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;AANA;;;;;;;AAYO,SAAS,OAAO,EAAE,cAAc,EAAe;IACpD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAAgB,MAAM;QAAI;QAClC;YAAE,MAAM;YAAY,MAAM;QAAI;QAC9B;YAAE,MAAM;YAAY,MAAM;YAAK,SAAS;QAAQ;KACjD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,eAAe;;kCAE9B,8OAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,8OAAC,iNAAA,CAAA,YAAS;wBAAC,WAAU;wBAAU,eAAY;;;;;;;;;;;;0BAI7C,8OAAC;gBAAI,WAAU;gBAAiC,eAAY;;;;;;0BAE5D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,MAAK;gCAAS,WAAU;;kDAC9B,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC,+MAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;0CAI5C,8OAAC;gCAAI,WAAU;gCAAgD,eAAY;;;;;;0CAG3E,8OAAC,2KAAA,CAAA,OAAI;gCAAC,IAAG;gCAAM,WAAU;;kDACvB,8OAAC,2KAAA,CAAA,OAAI,CAAC,MAAM;wCAAC,WAAU;;0DACrB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,MAAM,UAAU,OAAO,GAAG,iBAAiB;;;;;;;;;;;0DAGhD,8OAAC;gDAAK,WAAU;;kEACd,8OAAC;wDAAK,WAAU;wDAAqD,eAAY;kEAC9E,MAAM,YAAY;;;;;;kEAErB,8OAAC;wDAAK,WAAU;;4DAAwC;4DACpD,MAAM,MAAM,QAAQ,KAAK,QAAQ;4DAAO;;;;;;;;;;;;;;;;;;;kDAIhD,8OAAC,uLAAA,CAAA,aAAU;wCACT,IAAI,qMAAA,CAAA,WAAQ;wCACZ,OAAM;wCACN,WAAU;wCACV,SAAQ;wCACR,OAAM;wCACN,WAAU;wCACV,SAAQ;kDAER,cAAA,8OAAC,2KAAA,CAAA,OAAI,CAAC,KAAK;4CAAC,WAAU;sDACnB,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,2KAAA,CAAA,OAAI,CAAC,IAAI;8DACP,CAAC,EAAE,MAAM,EAAE,iBACV,8OAAC;4DACC,SAAS,KAAK,OAAO;4DACrB,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,SAAS,eAAe,IACxB;sEAGD,KAAK,IAAI;;;;;;mDATA,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqB3C", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/components/providers'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const { user } = useAuth()\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Sidebar open={sidebarOpen} setOpen={setSidebarOpen} />\n      \n      <div className=\"lg:pl-72\">\n        <Header setSidebarOpen={setSidebarOpen} />\n        \n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAWO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAa,SAAS;;;;;;0BAErC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,SAAM;wBAAC,gBAAgB;;;;;;kCAExB,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 838, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createClientComponentClient } from '@supabase/auth-helpers-nextjs'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Client component client\nexport const createSupabaseClient = () => createClientComponentClient()\n\n// Admin client (server-side only)\nexport const createSupabaseAdminClient = () => {\n  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n  return createClient(supabaseUrl, serviceRoleKey, {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  })\n}\n\n// Database types\nexport interface Database {\n  public: {\n    Tables: {\n      profiles: {\n        Row: {\n          id: string\n          email: string\n          full_name: string\n          role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department: string | null\n          phone: string | null\n          is_active: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          email: string\n          full_name: string\n          role?: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department?: string | null\n          phone?: string | null\n          is_active?: boolean\n        }\n        Update: {\n          email?: string\n          full_name?: string\n          role?: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department?: string | null\n          phone?: string | null\n          is_active?: boolean\n        }\n      }\n      inventory_categories: {\n        Row: {\n          id: string\n          name: string\n          description: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          description?: string | null\n        }\n        Update: {\n          name?: string\n          description?: string | null\n        }\n      }\n      inventory_items: {\n        Row: {\n          id: string\n          name: string\n          sku: string\n          category_id: string | null\n          description: string | null\n          unit_of_measure: string\n          current_stock: number\n          minimum_stock: number\n          maximum_stock: number | null\n          unit_cost: number\n          supplier_info: any | null\n          storage_conditions: string | null\n          expiry_date: string | null\n          batch_number: string | null\n          is_active: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          sku: string\n          category_id?: string | null\n          description?: string | null\n          unit_of_measure: string\n          current_stock?: number\n          minimum_stock?: number\n          maximum_stock?: number | null\n          unit_cost?: number\n          supplier_info?: any | null\n          storage_conditions?: string | null\n          expiry_date?: string | null\n          batch_number?: string | null\n          is_active?: boolean\n        }\n        Update: {\n          name?: string\n          sku?: string\n          category_id?: string | null\n          description?: string | null\n          unit_of_measure?: string\n          current_stock?: number\n          minimum_stock?: number\n          maximum_stock?: number | null\n          unit_cost?: number\n          supplier_info?: any | null\n          storage_conditions?: string | null\n          expiry_date?: string | null\n          batch_number?: string | null\n          is_active?: boolean\n        }\n      }\n      recipes: {\n        Row: {\n          id: string\n          name: string\n          code: string\n          description: string | null\n          category: string | null\n          version: number\n          batch_size: number\n          unit_of_measure: string\n          estimated_cost: number | null\n          preparation_time: number | null\n          instructions: string | null\n          notes: string | null\n          is_active: boolean\n          created_by: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          code: string\n          description?: string | null\n          category?: string | null\n          version?: number\n          batch_size: number\n          unit_of_measure: string\n          estimated_cost?: number | null\n          preparation_time?: number | null\n          instructions?: string | null\n          notes?: string | null\n          is_active?: boolean\n          created_by?: string | null\n        }\n        Update: {\n          name?: string\n          code?: string\n          description?: string | null\n          category?: string | null\n          version?: number\n          batch_size?: number\n          unit_of_measure?: string\n          estimated_cost?: number | null\n          preparation_time?: number | null\n          instructions?: string | null\n          notes?: string | null\n          is_active?: boolean\n        }\n      }\n      production_batches: {\n        Row: {\n          id: string\n          batch_number: string\n          recipe_id: string\n          batch_type: 'test' | 'production'\n          planned_quantity: number\n          actual_quantity: number | null\n          unit_of_measure: string\n          status: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date: string | null\n          actual_start_date: string | null\n          planned_end_date: string | null\n          actual_end_date: string | null\n          production_cost: number | null\n          yield_percentage: number | null\n          quality_approved: boolean | null\n          notes: string | null\n          created_by: string | null\n          assigned_to: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          batch_number: string\n          recipe_id: string\n          batch_type: 'test' | 'production'\n          planned_quantity: number\n          actual_quantity?: number | null\n          unit_of_measure: string\n          status?: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority?: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date?: string | null\n          actual_start_date?: string | null\n          planned_end_date?: string | null\n          actual_end_date?: string | null\n          production_cost?: number | null\n          yield_percentage?: number | null\n          quality_approved?: boolean | null\n          notes?: string | null\n          created_by?: string | null\n          assigned_to?: string | null\n        }\n        Update: {\n          batch_number?: string\n          recipe_id?: string\n          batch_type?: 'test' | 'production'\n          planned_quantity?: number\n          actual_quantity?: number | null\n          unit_of_measure?: string\n          status?: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority?: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date?: string | null\n          actual_start_date?: string | null\n          planned_end_date?: string | null\n          actual_end_date?: string | null\n          production_cost?: number | null\n          yield_percentage?: number | null\n          quality_approved?: boolean | null\n          notes?: string | null\n          assigned_to?: string | null\n        }\n      }\n    }\n    Views: {\n      [_ in never]: never\n    }\n    Functions: {\n      check_recipe_availability: {\n        Args: {\n          recipe_uuid: string\n          batch_size_multiplier?: number\n        }\n        Returns: {\n          item_id: string\n          item_name: string\n          required_quantity: number\n          available_quantity: number\n          is_sufficient: boolean\n        }[]\n      }\n      calculate_recipe_cost: {\n        Args: {\n          recipe_uuid: string\n          batch_size_multiplier?: number\n        }\n        Returns: number\n      }\n    }\n    Enums: {\n      user_role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,uBAAuB,IAAM,CAAA,GAAA,wKAAA,CAAA,8BAA2B,AAAD;AAG7D,MAAM,4BAA4B;IACvC,MAAM,iBAAiB,QAAQ,GAAG,CAAC,yBAAyB;IAC5D,OAAO,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,gBAAgB;QAC/C,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 866, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/dashboard/dashboard-stats.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { createSupabaseClient } from '@/lib/supabase'\nimport {\n  CubeIcon,\n  ClipboardDocumentListIcon,\n  CogIcon,\n  ExclamationTriangleIcon,\n} from '@heroicons/react/24/outline'\n\ninterface Stats {\n  totalItems: number\n  lowStockItems: number\n  activeRecipes: number\n  activeBatches: number\n}\n\nexport function DashboardStats() {\n  const [stats, setStats] = useState<Stats>({\n    totalItems: 0,\n    lowStockItems: 0,\n    activeRecipes: 0,\n    activeBatches: 0,\n  })\n  const [loading, setLoading] = useState(true)\n  const supabase = createSupabaseClient()\n\n  useEffect(() => {\n    async function fetchStats() {\n      try {\n        // For demo purposes, use mock data\n        // In production, you would fetch from your actual database\n        const mockStats = {\n          totalItems: 6,\n          lowStockItems: 3,\n          activeRecipes: 2,\n          activeBatches: 1,\n        }\n\n        // Simulate API delay\n        await new Promise(resolve => setTimeout(resolve, 1000))\n\n        setStats(mockStats)\n      } catch (error) {\n        console.error('Error fetching stats:', error)\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    fetchStats()\n  }, [])\n\n  const statItems = [\n    {\n      name: 'Total Inventory Items',\n      value: stats.totalItems,\n      icon: CubeIcon,\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-100',\n    },\n    {\n      name: 'Low Stock Alerts',\n      value: stats.lowStockItems,\n      icon: ExclamationTriangleIcon,\n      color: 'text-red-600',\n      bgColor: 'bg-red-100',\n    },\n    {\n      name: 'Active Recipes',\n      value: stats.activeRecipes,\n      icon: ClipboardDocumentListIcon,\n      color: 'text-green-600',\n      bgColor: 'bg-green-100',\n    },\n    {\n      name: 'Active Batches',\n      value: stats.activeBatches,\n      icon: CogIcon,\n      color: 'text-purple-600',\n      bgColor: 'bg-purple-100',\n    },\n  ]\n\n  if (loading) {\n    return (\n      <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n        {[...Array(4)].map((_, i) => (\n          <div key={i} className=\"bg-white overflow-hidden shadow rounded-lg animate-pulse\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"h-8 w-8 bg-gray-200 rounded-md\"></div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                  <div className=\"h-6 bg-gray-200 rounded w-1/2\"></div>\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n      {statItems.map((item) => (\n        <div key={item.name} className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className={`p-2 rounded-md ${item.bgColor}`}>\n                  <item.icon className={`h-6 w-6 ${item.color}`} aria-hidden=\"true\" />\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">{item.name}</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{item.value}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAkBO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;QACxC,YAAY;QACZ,eAAe;QACf,eAAe;QACf,eAAe;IACjB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;IAEpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;YACb,IAAI;gBACF,mCAAmC;gBACnC,2DAA2D;gBAC3D,MAAM,YAAY;oBAChB,YAAY;oBACZ,eAAe;oBACf,eAAe;oBACf,eAAe;gBACjB;gBAEA,qBAAqB;gBACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,SAAS;YACX,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;YACzC,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB;YACE,MAAM;YACN,OAAO,MAAM,UAAU;YACvB,MAAM,+MAAA,CAAA,WAAQ;YACd,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO,MAAM,aAAa;YAC1B,MAAM,6OAAA,CAAA,0BAAuB;YAC7B,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO,MAAM,aAAa;YAC1B,MAAM,iPAAA,CAAA,4BAAyB;YAC/B,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO,MAAM,aAAa;YAC1B,MAAM,6MAAA,CAAA,UAAO;YACb,OAAO;YACP,SAAS;QACX;KACD;IAED,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACZ;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;oBAAY,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;mBARb;;;;;;;;;;IAgBlB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;gBAAoB,WAAU;0BAC7B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,CAAC,eAAe,EAAE,KAAK,OAAO,EAAE;8CAC9C,cAAA,8OAAC,KAAK,IAAI;wCAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;wCAAE,eAAY;;;;;;;;;;;;;;;;0CAG/D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8C,KAAK,IAAI;;;;;;sDACrE,8OAAC;4CAAG,WAAU;sDAAqC,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAX7D,KAAK,IAAI;;;;;;;;;;AAoB3B", "debugId": null}}, {"offset": {"line": 1103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/dashboard/recent-activity.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { createSupabaseClient } from '@/lib/supabase'\nimport { format } from 'date-fns'\nimport {\n  CubeIcon,\n  ClipboardDocumentListIcon,\n  CogIcon,\n  BeakerIcon,\n} from '@heroicons/react/24/outline'\n\ninterface Activity {\n  id: string\n  type: 'inventory' | 'recipe' | 'production' | 'quality'\n  title: string\n  description: string\n  timestamp: string\n  user?: string\n}\n\nexport function RecentActivity() {\n  const [activities, setActivities] = useState<Activity[]>([])\n  const [loading, setLoading] = useState(true)\n  const supabase = createSupabaseClient()\n\n  useEffect(() => {\n    async function fetchRecentActivity() {\n      try {\n        // This is a simplified version - in a real app, you'd have a proper activity log table\n        const recentActivities: Activity[] = [\n          {\n            id: '1',\n            type: 'inventory',\n            title: 'Low Stock Alert',\n            description: 'Vanilla Extract is running low (5 units remaining)',\n            timestamp: new Date().toISOString(),\n          },\n          {\n            id: '2',\n            type: 'production',\n            title: 'Batch Completed',\n            description: 'Production batch SFF-20241211-001 completed successfully',\n            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n          },\n          {\n            id: '3',\n            type: 'quality',\n            title: 'Quality Test Passed',\n            description: 'COA generated for batch SFF-20241211-001',\n            timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n          },\n          {\n            id: '4',\n            type: 'recipe',\n            title: 'Recipe Updated',\n            description: 'Strawberry Flavor v2.1 recipe modified',\n            timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n          },\n        ]\n\n        setActivities(recentActivities)\n      } catch (error) {\n        console.error('Error fetching recent activity:', error)\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    fetchRecentActivity()\n  }, [supabase])\n\n  const getActivityIcon = (type: Activity['type']) => {\n    switch (type) {\n      case 'inventory':\n        return CubeIcon\n      case 'recipe':\n        return ClipboardDocumentListIcon\n      case 'production':\n        return CogIcon\n      case 'quality':\n        return BeakerIcon\n      default:\n        return CubeIcon\n    }\n  }\n\n  const getActivityColor = (type: Activity['type']) => {\n    switch (type) {\n      case 'inventory':\n        return 'text-blue-600 bg-blue-100'\n      case 'recipe':\n        return 'text-green-600 bg-green-100'\n      case 'production':\n        return 'text-purple-600 bg-purple-100'\n      case 'quality':\n        return 'text-orange-600 bg-orange-100'\n      default:\n        return 'text-gray-600 bg-gray-100'\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">Recent Activity</h3>\n          <div className=\"space-y-4\">\n            {[...Array(4)].map((_, i) => (\n              <div key={i} className=\"flex items-start space-x-3 animate-pulse\">\n                <div className=\"h-8 w-8 bg-gray-200 rounded-full\"></div>\n                <div className=\"flex-1 space-y-2\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"bg-white shadow rounded-lg\">\n      <div className=\"px-4 py-5 sm:p-6\">\n        <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">Recent Activity</h3>\n        <div className=\"flow-root\">\n          <ul role=\"list\" className=\"-mb-8\">\n            {activities.map((activity, activityIdx) => {\n              const Icon = getActivityIcon(activity.type)\n              const colorClasses = getActivityColor(activity.type)\n              \n              return (\n                <li key={activity.id}>\n                  <div className=\"relative pb-8\">\n                    {activityIdx !== activities.length - 1 ? (\n                      <span\n                        className=\"absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200\"\n                        aria-hidden=\"true\"\n                      />\n                    ) : null}\n                    <div className=\"relative flex space-x-3\">\n                      <div>\n                        <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${colorClasses}`}>\n                          <Icon className=\"h-4 w-4\" aria-hidden=\"true\" />\n                        </span>\n                      </div>\n                      <div className=\"flex min-w-0 flex-1 justify-between space-x-4 pt-1.5\">\n                        <div>\n                          <p className=\"text-sm font-medium text-gray-900\">{activity.title}</p>\n                          <p className=\"text-sm text-gray-500\">{activity.description}</p>\n                        </div>\n                        <div className=\"whitespace-nowrap text-right text-sm text-gray-500\">\n                          <time dateTime={activity.timestamp}>\n                            {format(new Date(activity.timestamp), 'MMM d, h:mm a')}\n                          </time>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </li>\n              )\n            })}\n          </ul>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAqBO,SAAS;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;IAEpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;YACb,IAAI;gBACF,uFAAuF;gBACvF,MAAM,mBAA+B;oBACnC;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,WAAW,IAAI,OAAO,WAAW;oBACnC;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;oBAClE;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;oBAClE;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;oBAClE;iBACD;gBAED,cAAc;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;YACnD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO,+MAAA,CAAA,WAAQ;YACjB,KAAK;gBACH,OAAO,iPAAA,CAAA,4BAAyB;YAClC,KAAK;gBACH,OAAO,6MAAA,CAAA,UAAO;YAChB,KAAK;gBACH,OAAO,mNAAA,CAAA,aAAU;YACnB;gBACE,OAAO,+MAAA,CAAA,WAAQ;QACnB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmD;;;;;;kCACjE,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;gCAAY,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;+BAJT;;;;;;;;;;;;;;;;;;;;;IAYtB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAmD;;;;;;8BACjE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,MAAK;wBAAO,WAAU;kCACvB,WAAW,GAAG,CAAC,CAAC,UAAU;4BACzB,MAAM,OAAO,gBAAgB,SAAS,IAAI;4BAC1C,MAAM,eAAe,iBAAiB,SAAS,IAAI;4BAEnD,qBACE,8OAAC;0CACC,cAAA,8OAAC;oCAAI,WAAU;;wCACZ,gBAAgB,WAAW,MAAM,GAAG,kBACnC,8OAAC;4CACC,WAAU;4CACV,eAAY;;;;;mDAEZ;sDACJ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DACC,cAAA,8OAAC;wDAAK,WAAW,CAAC,wEAAwE,EAAE,cAAc;kEACxG,cAAA,8OAAC;4DAAK,WAAU;4DAAU,eAAY;;;;;;;;;;;;;;;;8DAG1C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAqC,SAAS,KAAK;;;;;;8EAChE,8OAAC;oEAAE,WAAU;8EAAyB,SAAS,WAAW;;;;;;;;;;;;sEAE5D,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,UAAU,SAAS,SAAS;0EAC/B,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,SAAS,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BArBzC,SAAS,EAAE;;;;;wBA6BxB;;;;;;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 1422, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/ui/modal.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\n\ninterface ModalProps {\n  isOpen: boolean\n  onClose: () => void\n  title: string\n  children: React.ReactNode\n  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl'\n}\n\nexport function Modal({ isOpen, onClose, title, children, maxWidth = 'lg' }: ModalProps) {\n  const maxWidthClasses = {\n    sm: 'sm:max-w-sm',\n    md: 'sm:max-w-md',\n    lg: 'sm:max-w-lg',\n    xl: 'sm:max-w-xl',\n    '2xl': 'sm:max-w-2xl'\n  }\n\n  return (\n    <Transition.Root show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={onClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-out duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in duration-200\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 z-10 overflow-y-auto\">\n          <div className=\"flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"ease-out duration-300\"\n              enterFrom=\"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95\"\n              enterTo=\"opacity-100 translate-y-0 sm:scale-100\"\n              leave=\"ease-in duration-200\"\n              leaveFrom=\"opacity-100 translate-y-0 sm:scale-100\"\n              leaveTo=\"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95\"\n            >\n              <Dialog.Panel className={`relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full ${maxWidthClasses[maxWidth]} sm:p-6`}>\n                <div className=\"absolute right-0 top-0 hidden pr-4 pt-4 sm:block\">\n                  <button\n                    type=\"button\"\n                    className=\"rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2\"\n                    onClick={onClose}\n                  >\n                    <span className=\"sr-only\">Close</span>\n                    <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                  </button>\n                </div>\n                <div className=\"sm:flex sm:items-start\">\n                  <div className=\"mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full\">\n                    <Dialog.Title as=\"h3\" className=\"text-lg font-semibold leading-6 text-gray-900 mb-4\">\n                      {title}\n                    </Dialog.Title>\n                    <div className=\"mt-2\">\n                      {children}\n                    </div>\n                  </div>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </div>\n      </Dialog>\n    </Transition.Root>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAcO,SAAS,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAc;IACrF,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;IACT;IAEA,qBACE,8OAAC,uLAAA,CAAA,aAAU,CAAC,IAAI;QAAC,MAAM;QAAQ,IAAI,qMAAA,CAAA,WAAQ;kBACzC,cAAA,8OAAC,+KAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,qMAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,qMAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;gCAAC,WAAW,CAAC,2HAA2H,EAAE,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC;;kDACvL,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS;;8DAET,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;;;;;;kDAG/C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;oDAAC,IAAG;oDAAK,WAAU;8DAC7B;;;;;;8DAEH,8OAAC;oDAAI,WAAU;8DACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvB", "debugId": null}}, {"offset": {"line": 1592, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/dashboard/quick-actions.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { Modal } from '@/components/ui/modal'\nimport {\n  PlusIcon,\n  ClipboardDocumentListIcon,\n  CogIcon,\n  BeakerIcon,\n} from '@heroicons/react/24/outline'\n\ninterface QuickAction {\n  name: string\n  description: string\n  icon: any\n  color: string\n  bgColor: string\n  action: 'navigate' | 'modal'\n  href?: string\n  modalType?: 'inventory' | 'recipe' | 'production' | 'quality'\n}\n\nconst actions: QuickAction[] = [\n  {\n    name: 'Add Inventory Item',\n    description: 'Add new raw materials or ingredients',\n    icon: PlusIcon,\n    color: 'text-blue-600',\n    bgColor: 'bg-blue-50 hover:bg-blue-100',\n    action: 'navigate',\n    href: '/inventory'\n  },\n  {\n    name: 'Create Recipe',\n    description: 'Create a new product recipe',\n    icon: ClipboardDocumentListIcon,\n    color: 'text-green-600',\n    bgColor: 'bg-green-50 hover:bg-green-100',\n    action: 'navigate',\n    href: '/recipes'\n  },\n  {\n    name: 'Start Production',\n    description: 'Begin a new production batch',\n    icon: CogIcon,\n    color: 'text-purple-600',\n    bgColor: 'bg-purple-50 hover:bg-purple-100',\n    action: 'navigate',\n    href: '/production'\n  },\n  {\n    name: 'Quality Test',\n    description: 'Record quality test results',\n    icon: BeakerIcon,\n    color: 'text-orange-600',\n    bgColor: 'bg-orange-50 hover:bg-orange-100',\n    action: 'modal',\n    modalType: 'quality'\n  },\n]\n\nexport function QuickActions() {\n  const router = useRouter()\n  const [showQualityModal, setShowQualityModal] = useState(false)\n  const [qualityTest, setQualityTest] = useState({\n    batch_number: '',\n    test_type: 'microbiological',\n    result: 'pass',\n    notes: ''\n  })\n\n  const handleActionClick = (action: QuickAction) => {\n    if (action.action === 'navigate' && action.href) {\n      router.push(action.href)\n    } else if (action.action === 'modal' && action.modalType === 'quality') {\n      setShowQualityModal(true)\n    }\n  }\n\n  const handleQualityTest = () => {\n    // Mock quality test submission\n    console.log('Quality test recorded:', qualityTest)\n    setShowQualityModal(false)\n    setQualityTest({\n      batch_number: '',\n      test_type: 'microbiological',\n      result: 'pass',\n      notes: ''\n    })\n    // Show success message or redirect\n    alert('Quality test results recorded successfully!')\n  }\n\n  return (\n    <>\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">Quick Actions</h3>\n          <div className=\"space-y-3\">\n            {actions.map((action) => (\n              <button\n                key={action.name}\n                onClick={() => handleActionClick(action)}\n                className={`w-full text-left p-3 rounded-lg border border-gray-200 transition-colors ${action.bgColor} hover:shadow-md`}\n              >\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <action.icon className={`h-5 w-5 ${action.color}`} aria-hidden=\"true\" />\n                  </div>\n                  <div className=\"ml-3 flex-1\">\n                    <p className=\"text-sm font-medium text-gray-900\">{action.name}</p>\n                    <p className=\"text-xs text-gray-500\">{action.description}</p>\n                  </div>\n                </div>\n              </button>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Quality Test Modal */}\n      <Modal\n        isOpen={showQualityModal}\n        onClose={() => setShowQualityModal(false)}\n        title=\"Record Quality Test Results\"\n        maxWidth=\"lg\"\n      >\n        <form onSubmit={(e) => { e.preventDefault(); handleQualityTest(); }} className=\"space-y-4\">\n          <div className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"batch_number\" className=\"block text-sm font-medium text-gray-700\">\n                Batch Number\n              </label>\n              <select\n                id=\"batch_number\"\n                required\n                className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                value={qualityTest.batch_number}\n                onChange={(e) => setQualityTest({ ...qualityTest, batch_number: e.target.value })}\n              >\n                <option value=\"\">Select Batch</option>\n                <option value=\"SFF-20241211-001\">SFF-20241211-001 - Strawberry Vanilla Blend</option>\n                <option value=\"SFF-20241211-002\">SFF-20241211-002 - Classic Vanilla Extract</option>\n                <option value=\"SFF-20241212-001\">SFF-20241212-001 - Citrus Burst Flavor</option>\n              </select>\n            </div>\n            <div>\n              <label htmlFor=\"test_type\" className=\"block text-sm font-medium text-gray-700\">\n                Test Type\n              </label>\n              <select\n                id=\"test_type\"\n                className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                value={qualityTest.test_type}\n                onChange={(e) => setQualityTest({ ...qualityTest, test_type: e.target.value })}\n              >\n                <option value=\"microbiological\">Microbiological Testing</option>\n                <option value=\"chemical\">Chemical Analysis</option>\n                <option value=\"physical\">Physical Properties</option>\n                <option value=\"sensory\">Sensory Evaluation</option>\n                <option value=\"stability\">Stability Testing</option>\n              </select>\n            </div>\n            <div>\n              <label htmlFor=\"result\" className=\"block text-sm font-medium text-gray-700\">\n                Test Result\n              </label>\n              <select\n                id=\"result\"\n                className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                value={qualityTest.result}\n                onChange={(e) => setQualityTest({ ...qualityTest, result: e.target.value })}\n              >\n                <option value=\"pass\">Pass</option>\n                <option value=\"fail\">Fail</option>\n                <option value=\"pending\">Pending</option>\n                <option value=\"retest\">Retest Required</option>\n              </select>\n            </div>\n            <div>\n              <label htmlFor=\"notes\" className=\"block text-sm font-medium text-gray-700\">\n                Notes\n              </label>\n              <textarea\n                id=\"notes\"\n                rows={3}\n                className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                placeholder=\"Enter test observations, measurements, or additional notes...\"\n                value={qualityTest.notes}\n                onChange={(e) => setQualityTest({ ...qualityTest, notes: e.target.value })}\n              />\n            </div>\n          </div>\n          <div className=\"flex justify-end space-x-3 pt-4\">\n            <button\n              type=\"button\"\n              onClick={() => setShowQualityModal(false)}\n              className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700\"\n            >\n              Record Test Results\n            </button>\n          </div>\n        </form>\n      </Modal>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAuBA,MAAM,UAAyB;IAC7B;QACE,MAAM;QACN,aAAa;QACb,MAAM,+MAAA,CAAA,WAAQ;QACd,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;IACR;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,iPAAA,CAAA,4BAAyB;QAC/B,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;IACR;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,6MAAA,CAAA,UAAO;QACb,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;IACR;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,mNAAA,CAAA,aAAU;QAChB,OAAO;QACP,SAAS;QACT,QAAQ;QACR,WAAW;IACb;CACD;AAEM,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,cAAc;QACd,WAAW;QACX,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,OAAO,MAAM,KAAK,cAAc,OAAO,IAAI,EAAE;YAC/C,OAAO,IAAI,CAAC,OAAO,IAAI;QACzB,OAAO,IAAI,OAAO,MAAM,KAAK,WAAW,OAAO,SAAS,KAAK,WAAW;YACtE,oBAAoB;QACtB;IACF;IAEA,MAAM,oBAAoB;QACxB,+BAA+B;QAC/B,QAAQ,GAAG,CAAC,0BAA0B;QACtC,oBAAoB;QACpB,eAAe;YACb,cAAc;YACd,WAAW;YACX,QAAQ;YACR,OAAO;QACT;QACA,mCAAmC;QACnC,MAAM;IACR;IAEA,qBACE;;0BACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmD;;;;;;sCACjE,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;oCAEC,SAAS,IAAM,kBAAkB;oCACjC,WAAW,CAAC,yEAAyE,EAAE,OAAO,OAAO,CAAC,gBAAgB,CAAC;8CAEvH,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,OAAO,IAAI;oDAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,KAAK,EAAE;oDAAE,eAAY;;;;;;;;;;;0DAEjE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAqC,OAAO,IAAI;;;;;;kEAC7D,8OAAC;wDAAE,WAAU;kEAAyB,OAAO,WAAW;;;;;;;;;;;;;;;;;;mCAVvD,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;0BAoB1B,8OAAC,iIAAA,CAAA,QAAK;gBACJ,QAAQ;gBACR,SAAS,IAAM,oBAAoB;gBACnC,OAAM;gBACN,UAAS;0BAET,cAAA,8OAAC;oBAAK,UAAU,CAAC;wBAAQ,EAAE,cAAc;wBAAI;oBAAqB;oBAAG,WAAU;;sCAC7E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAe,WAAU;sDAA0C;;;;;;sDAGlF,8OAAC;4CACC,IAAG;4CACH,QAAQ;4CACR,WAAU;4CACV,OAAO,YAAY,YAAY;4CAC/B,UAAU,CAAC,IAAM,eAAe;oDAAE,GAAG,WAAW;oDAAE,cAAc,EAAE,MAAM,CAAC,KAAK;gDAAC;;8DAE/E,8OAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,8OAAC;oDAAO,OAAM;8DAAmB;;;;;;8DACjC,8OAAC;oDAAO,OAAM;8DAAmB;;;;;;8DACjC,8OAAC;oDAAO,OAAM;8DAAmB;;;;;;;;;;;;;;;;;;8CAGrC,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAY,WAAU;sDAA0C;;;;;;sDAG/E,8OAAC;4CACC,IAAG;4CACH,WAAU;4CACV,OAAO,YAAY,SAAS;4CAC5B,UAAU,CAAC,IAAM,eAAe;oDAAE,GAAG,WAAW;oDAAE,WAAW,EAAE,MAAM,CAAC,KAAK;gDAAC;;8DAE5E,8OAAC;oDAAO,OAAM;8DAAkB;;;;;;8DAChC,8OAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,8OAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,8OAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,8OAAC;oDAAO,OAAM;8DAAY;;;;;;;;;;;;;;;;;;8CAG9B,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAS,WAAU;sDAA0C;;;;;;sDAG5E,8OAAC;4CACC,IAAG;4CACH,WAAU;4CACV,OAAO,YAAY,MAAM;4CACzB,UAAU,CAAC,IAAM,eAAe;oDAAE,GAAG,WAAW;oDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;gDAAC;;8DAEzE,8OAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,8OAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,8OAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;;;;;;;;;;;;;8CAG3B,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,8OAAC;4CACC,IAAG;4CACH,MAAM;4CACN,WAAU;4CACV,aAAY;4CACZ,OAAO,YAAY,KAAK;4CACxB,UAAU,CAAC,IAAM,eAAe;oDAAE,GAAG,WAAW;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC;;;;;;;;;;;;;;;;;;sCAI9E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS,IAAM,oBAAoB;oCACnC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 2068, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/dashboard/inventory-alerts.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { createSupabaseClient } from '@/lib/supabase'\nimport Link from 'next/link'\nimport { ExclamationTriangleIcon } from '@heroicons/react/24/outline'\n\ninterface LowStockItem {\n  id: string\n  name: string\n  sku: string\n  current_stock: number\n  minimum_stock: number\n  unit_of_measure: string\n}\n\nexport function InventoryAlerts() {\n  const [lowStockItems, setLowStockItems] = useState<LowStockItem[]>([])\n  const [loading, setLoading] = useState(true)\n  const supabase = createSupabaseClient()\n\n  useEffect(() => {\n    async function fetchLowStockItems() {\n      try {\n        // Mock low stock items for demo\n        const mockLowStockItems: LowStockItem[] = [\n          {\n            id: '1',\n            name: 'Vanilla Extract',\n            sku: 'VAN-001',\n            current_stock: 5,\n            minimum_stock: 10,\n            unit_of_measure: 'liters'\n          },\n          {\n            id: '2',\n            name: 'Citric Acid',\n            sku: 'CIT-001',\n            current_stock: 8,\n            minimum_stock: 12,\n            unit_of_measure: 'kg'\n          },\n          {\n            id: '3',\n            name: 'Glass Bottles 100ml',\n            sku: 'BOT-100',\n            current_stock: 200,\n            minimum_stock: 500,\n            unit_of_measure: 'pieces'\n          }\n        ]\n\n        // Simulate API delay\n        await new Promise(resolve => setTimeout(resolve, 800))\n\n        setLowStockItems(mockLowStockItems)\n      } catch (error) {\n        console.error('Error fetching low stock items:', error)\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    fetchLowStockItems()\n  }, [])\n\n  if (loading) {\n    return (\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">Inventory Alerts</h3>\n          <div className=\"space-y-3\">\n            {[...Array(3)].map((_, i) => (\n              <div key={i} className=\"flex items-center space-x-3 animate-pulse\">\n                <div className=\"h-5 w-5 bg-gray-200 rounded\"></div>\n                <div className=\"flex-1 space-y-2\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"bg-white shadow rounded-lg\">\n      <div className=\"px-4 py-5 sm:p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg leading-6 font-medium text-gray-900\">Inventory Alerts</h3>\n          <Link\n            href=\"/inventory\"\n            className=\"text-sm font-medium text-blue-600 hover:text-blue-500\"\n          >\n            View all\n          </Link>\n        </div>\n        \n        {lowStockItems.length === 0 ? (\n          <div className=\"text-center py-6\">\n            <div className=\"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100\">\n              <svg className=\"h-6 w-6 text-green-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n              </svg>\n            </div>\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">All good!</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">No low stock alerts at the moment.</p>\n          </div>\n        ) : (\n          <div className=\"space-y-3\">\n            {lowStockItems.map((item) => (\n              <div\n                key={item.id}\n                className=\"flex items-center space-x-3 p-3 bg-red-50 rounded-lg border border-red-200\"\n              >\n                <ExclamationTriangleIcon className=\"h-5 w-5 text-red-600 flex-shrink-0\" />\n                <div className=\"flex-1 min-w-0\">\n                  <p className=\"text-sm font-medium text-gray-900 truncate\">\n                    {item.name}\n                  </p>\n                  <p className=\"text-xs text-gray-500\">\n                    {item.current_stock} {item.unit_of_measure} remaining \n                    (min: {item.minimum_stock})\n                  </p>\n                </div>\n              </div>\n            ))}\n            \n            {lowStockItems.length > 0 && (\n              <div className=\"mt-4\">\n                <Link\n                  href=\"/inventory?filter=low_stock\"\n                  className=\"w-full flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200\"\n                >\n                  View all low stock items\n                </Link>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAgBO,SAAS;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;IAEpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;YACb,IAAI;gBACF,gCAAgC;gBAChC,MAAM,oBAAoC;oBACxC;wBACE,IAAI;wBACJ,MAAM;wBACN,KAAK;wBACL,eAAe;wBACf,eAAe;wBACf,iBAAiB;oBACnB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,KAAK;wBACL,eAAe;wBACf,eAAe;wBACf,iBAAiB;oBACnB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,KAAK;wBACL,eAAe;wBACf,eAAe;wBACf,iBAAiB;oBACnB;iBACD;gBAED,qBAAqB;gBACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,iBAAiB;YACnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;YACnD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmD;;;;;;kCACjE,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;gCAAY,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;+BAJT;;;;;;;;;;;;;;;;;;;;;IAYtB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA8C;;;;;;sCAC5D,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;gBAKF,cAAc,MAAM,KAAK,kBACxB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;gCAAyB,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CAC7E,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;yCAG5C,8OAAC;oBAAI,WAAU;;wBACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC,6OAAA,CAAA,0BAAuB;wCAAC,WAAU;;;;;;kDACnC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DACV,KAAK,IAAI;;;;;;0DAEZ,8OAAC;gDAAE,WAAU;;oDACV,KAAK,aAAa;oDAAC;oDAAE,KAAK,eAAe;oDAAC;oDACpC,KAAK,aAAa;oDAAC;;;;;;;;;;;;;;+BAVzB,KAAK,EAAE;;;;;wBAgBf,cAAc,MAAM,GAAG,mBACtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 2374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/dashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/components/providers'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { DashboardStats } from '@/components/dashboard/dashboard-stats'\nimport { RecentActivity } from '@/components/dashboard/recent-activity'\nimport { QuickActions } from '@/components/dashboard/quick-actions'\nimport { InventoryAlerts } from '@/components/dashboard/inventory-alerts'\n\nexport function Dashboard() {\n  const { user, loading } = useAuth()\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    // Redirect to login if no user\n    if (typeof window !== 'undefined') {\n      window.location.href = '/login'\n    }\n    return null\n  }\n\n  return (\n    <MainLayout>\n      <div className=\"space-y-6\">\n        {/* Welcome Section */}\n        <div className=\"bg-white shadow rounded-lg p-6\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">\n            Welcome back to SFF Production & Quality Management\n          </h1>\n          <p className=\"mt-1 text-sm text-gray-600\">\n            Here's what's happening with your production and quality management today.\n          </p>\n        </div>\n\n        {/* Dashboard Stats */}\n        <DashboardStats />\n\n        {/* Main Content Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          {/* Left Column - 2/3 width */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            <RecentActivity />\n          </div>\n\n          {/* Right Column - 1/3 width */}\n          <div className=\"space-y-6\">\n            <QuickActions />\n            <InventoryAlerts />\n          </div>\n        </div>\n      </div>\n    </MainLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,MAAM;QACT,+BAA+B;QAC/B,uCAAmC;;QAEnC;QACA,OAAO;IACT;IAEA,qBACE,8OAAC,8IAAA,CAAA,aAAU;kBACT,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAGjD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAM5C,8OAAC,qJAAA,CAAA,iBAAc;;;;;8BAGf,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,qJAAA,CAAA,iBAAc;;;;;;;;;;sCAIjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,mJAAA,CAAA,eAAY;;;;;8CACb,8OAAC,sJAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B", "debugId": null}}]}