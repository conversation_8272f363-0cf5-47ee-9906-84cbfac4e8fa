const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🚀 بدء بناء تطبيق SFF Management System للسطح المكتب...\n')

// Check if Node.js and npm are available
try {
  execSync('node --version', { stdio: 'pipe' })
  execSync('npm --version', { stdio: 'pipe' })
  console.log('✅ Node.js و npm متوفران')
} catch (error) {
  console.error('❌ خطأ: Node.js أو npm غير متوفر')
  process.exit(1)
}

// Install dependencies if needed
console.log('\n📦 تحقق من المتطلبات...')
if (!fs.existsSync('node_modules')) {
  console.log('📥 تثبيت المتطلبات...')
  try {
    execSync('npm install', { stdio: 'inherit' })
    console.log('✅ تم تثبيت المتطلبات بنجاح')
  } catch (error) {
    console.error('❌ خطأ في تثبيت المتطلبات')
    process.exit(1)
  }
} else {
  console.log('✅ المتطلبات متوفرة')
}

// Build Next.js application
console.log('\n🔨 بناء تطبيق Next.js...')
try {
  execSync('npm run build', { stdio: 'inherit' })
  console.log('✅ تم بناء تطبيق Next.js بنجاح')
} catch (error) {
  console.error('❌ خطأ في بناء تطبيق Next.js')
  process.exit(1)
}

// Export static files
console.log('\n📤 تصدير الملفات الثابتة...')
try {
  execSync('npm run export', { stdio: 'inherit' })
  console.log('✅ تم تصدير الملفات الثابتة بنجاح')
} catch (error) {
  console.error('❌ خطأ في تصدير الملفات الثابتة')
  process.exit(1)
}

// Build Electron application
console.log('\n⚡ بناء تطبيق Electron...')
try {
  execSync('npm run dist', { stdio: 'inherit' })
  console.log('✅ تم بناء تطبيق Electron بنجاح')
} catch (error) {
  console.error('❌ خطأ في بناء تطبيق Electron')
  process.exit(1)
}

// Check output directory
const distPath = path.join(__dirname, 'dist')
if (fs.existsSync(distPath)) {
  const files = fs.readdirSync(distPath)
  console.log('\n📁 الملفات المبنية:')
  files.forEach(file => {
    const filePath = path.join(distPath, file)
    const stats = fs.statSync(filePath)
    const size = (stats.size / 1024 / 1024).toFixed(2)
    console.log(`   📄 ${file} (${size} MB)`)
  })
}

console.log('\n🎉 تم بناء تطبيق SFF Management System بنجاح!')
console.log('📍 يمكنك العثور على الملفات في مجلد "dist"')
console.log('\n💡 لتشغيل التطبيق:')
console.log('   - Windows: انقر نقراً مزدوجاً على ملف .exe')
console.log('   - macOS: انقر نقراً مزدوجاً على ملف .dmg')
console.log('   - Linux: انقر نقراً مزدوجاً على ملف .AppImage')

console.log('\n📚 للمزيد من المعلومات، راجع ملف DESKTOP-README.md')
