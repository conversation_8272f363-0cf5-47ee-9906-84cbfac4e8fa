'use client'

import { useEffect, useState } from 'react'
import { createSupabaseClient } from '@/lib/supabase'
import {
  CubeIcon,
  ClipboardDocumentListIcon,
  CogIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline'

interface Stats {
  totalItems: number
  lowStockItems: number
  activeRecipes: number
  activeBatches: number
}

export function DashboardStats() {
  const [stats, setStats] = useState<Stats>({
    totalItems: 0,
    lowStockItems: 0,
    activeRecipes: 0,
    activeBatches: 0,
  })
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseClient()

  useEffect(() => {
    async function fetchStats() {
      try {
        // Fetch inventory stats
        const { data: inventoryItems } = await supabase
          .from('inventory_items')
          .select('current_stock, minimum_stock')
          .eq('is_active', true)

        // Fetch recipe stats
        const { data: recipes } = await supabase
          .from('recipes')
          .select('id')
          .eq('is_active', true)

        // Fetch production batch stats
        const { data: batches } = await supabase
          .from('production_batches')
          .select('id')
          .in('status', ['planned', 'in_progress'])

        const totalItems = inventoryItems?.length || 0
        const lowStockItems = inventoryItems?.filter(
          item => item.current_stock <= item.minimum_stock
        ).length || 0

        setStats({
          totalItems,
          lowStockItems,
          activeRecipes: recipes?.length || 0,
          activeBatches: batches?.length || 0,
        })
      } catch (error) {
        console.error('Error fetching stats:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [supabase])

  const statItems = [
    {
      name: 'Total Inventory Items',
      value: stats.totalItems,
      icon: CubeIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      name: 'Low Stock Alerts',
      value: stats.lowStockItems,
      icon: ExclamationTriangleIcon,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
    },
    {
      name: 'Active Recipes',
      value: stats.activeRecipes,
      icon: ClipboardDocumentListIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      name: 'Active Batches',
      value: stats.activeBatches,
      icon: CogIcon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
  ]

  if (loading) {
    return (
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white overflow-hidden shadow rounded-lg animate-pulse">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 bg-gray-200 rounded-md"></div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      {statItems.map((item) => (
        <div key={item.name} className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`p-2 rounded-md ${item.bgColor}`}>
                  <item.icon className={`h-6 w-6 ${item.color}`} aria-hidden="true" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">{item.name}</dt>
                  <dd className="text-lg font-medium text-gray-900">{item.value}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
