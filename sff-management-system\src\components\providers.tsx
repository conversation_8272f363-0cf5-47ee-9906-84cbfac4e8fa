'use client'

import { createContext, useContext, useEffect, useState } from 'react'

interface User {
  id: string
  username: string
  email: string
  role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'
  full_name?: string
  department?: string
}

interface AuthContextType {
  user: User | null
  loading: boolean
  signIn: (username: string, password: string) => Promise<{ success: boolean; error?: string }>
  signOut: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export function Providers({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Check for stored user session
    const checkSession = () => {
      try {
        if (typeof window !== 'undefined') {
          const storedUser = localStorage.getItem('sff_user')
          if (storedUser) {
            const userData = JSON.parse(storedUser)
            setUser(userData)
          }
        }
      } catch (error) {
        if (typeof window !== 'undefined') {
          localStorage.removeItem('sff_user')
        }
      } finally {
        setLoading(false)
      }
    }

    checkSession()
  }, [])

  const signIn = async (username: string, password: string) => {
    // Demo users
    const demoUsers = [
      { username: 'admin', password: 'admin123', role: 'admin', full_name: 'System Administrator', department: 'IT', email: '<EMAIL>' },
      { username: 'quality', password: 'quality123', role: 'quality_manager', full_name: 'Sarah Johnson', department: 'Quality Assurance', email: '<EMAIL>' },
      { username: 'production', password: 'production123', role: 'production_manager', full_name: 'Mike Wilson', department: 'Production', email: '<EMAIL>' },
      { username: 'employee', password: 'employee123', role: 'employee', full_name: 'Emily Davis', department: 'Production', email: '<EMAIL>' }
    ]

    const cleanUsername = username.trim().toLowerCase()
    const cleanPassword = password.trim()

    const demoUser = demoUsers.find(u =>
      u.username.toLowerCase() === cleanUsername && u.password === cleanPassword
    )

    if (!demoUser) {
      return { success: false, error: 'Invalid username or password' }
    }

    const userData = {
      id: `demo_${demoUser.username}`,
      username: demoUser.username,
      email: demoUser.email,
      role: demoUser.role as any,
      full_name: demoUser.full_name,
      department: demoUser.department
    }

    setUser(userData)
    if (typeof window !== 'undefined') {
      localStorage.setItem('sff_user', JSON.stringify(userData))
    }

    return { success: true }
  }

  const signOut = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('sff_user')
      setUser(null)
      window.location.href = '/login'
    }
  }

  const value = {
    user,
    loading,
    signIn,
    signOut,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
