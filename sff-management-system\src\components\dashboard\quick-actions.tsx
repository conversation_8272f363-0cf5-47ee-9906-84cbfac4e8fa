'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Modal } from '@/components/ui/modal'
import {
  PlusIcon,
  ClipboardDocumentListIcon,
  CogIcon,
  BeakerIcon,
} from '@heroicons/react/24/outline'

interface QuickAction {
  name: string
  description: string
  icon: any
  color: string
  bgColor: string
  action: 'navigate' | 'modal'
  href?: string
  modalType?: 'inventory' | 'recipe' | 'production' | 'quality'
}

const actions: QuickAction[] = [
  {
    name: 'Add Inventory Item',
    description: 'Add new raw materials or ingredients',
    icon: PlusIcon,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 hover:bg-blue-100',
    action: 'navigate',
    href: '/inventory'
  },
  {
    name: 'Create Recipe',
    description: 'Create a new product recipe',
    icon: ClipboardDocumentListIcon,
    color: 'text-green-600',
    bgColor: 'bg-green-50 hover:bg-green-100',
    action: 'navigate',
    href: '/recipes'
  },
  {
    name: 'Start Production',
    description: 'Begin a new production batch',
    icon: CogIcon,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50 hover:bg-purple-100',
    action: 'navigate',
    href: '/production'
  },
  {
    name: 'Quality Test',
    description: 'Record quality test results',
    icon: BeakerIcon,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50 hover:bg-orange-100',
    action: 'modal',
    modalType: 'quality'
  },
]

export function QuickActions() {
  const router = useRouter()
  const [showQualityModal, setShowQualityModal] = useState(false)
  const [qualityTest, setQualityTest] = useState({
    batch_number: '',
    test_type: 'microbiological',
    result: 'pass',
    notes: ''
  })

  const handleActionClick = (action: QuickAction) => {
    if (action.action === 'navigate' && action.href) {
      router.push(action.href)
    } else if (action.action === 'modal' && action.modalType === 'quality') {
      setShowQualityModal(true)
    }
  }

  const handleQualityTest = () => {
    // Mock quality test submission
    console.log('Quality test recorded:', qualityTest)
    setShowQualityModal(false)
    setQualityTest({
      batch_number: '',
      test_type: 'microbiological',
      result: 'pass',
      notes: ''
    })
    // Show success message or redirect
    alert('Quality test results recorded successfully!')
  }

  return (
    <>
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
          <div className="space-y-3">
            {actions.map((action) => (
              <button
                key={action.name}
                onClick={() => handleActionClick(action)}
                className={`w-full text-left p-3 rounded-lg border border-gray-200 transition-colors ${action.bgColor} hover:shadow-md`}
              >
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <action.icon className={`h-5 w-5 ${action.color}`} aria-hidden="true" />
                  </div>
                  <div className="ml-3 flex-1">
                    <p className="text-sm font-medium text-gray-900">{action.name}</p>
                    <p className="text-xs text-gray-500">{action.description}</p>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Quality Test Modal */}
      <Modal
        isOpen={showQualityModal}
        onClose={() => setShowQualityModal(false)}
        title="Record Quality Test Results"
        maxWidth="lg"
      >
        <form onSubmit={(e) => { e.preventDefault(); handleQualityTest(); }} className="space-y-4">
          <div className="space-y-4">
            <div>
              <label htmlFor="batch_number" className="block text-sm font-medium text-gray-700">
                Batch Number
              </label>
              <select
                id="batch_number"
                required
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                value={qualityTest.batch_number}
                onChange={(e) => setQualityTest({ ...qualityTest, batch_number: e.target.value })}
              >
                <option value="">Select Batch</option>
                <option value="SFF-20241211-001">SFF-20241211-001 - Strawberry Vanilla Blend</option>
                <option value="SFF-20241211-002">SFF-20241211-002 - Classic Vanilla Extract</option>
                <option value="SFF-20241212-001">SFF-20241212-001 - Citrus Burst Flavor</option>
              </select>
            </div>
            <div>
              <label htmlFor="test_type" className="block text-sm font-medium text-gray-700">
                Test Type
              </label>
              <select
                id="test_type"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                value={qualityTest.test_type}
                onChange={(e) => setQualityTest({ ...qualityTest, test_type: e.target.value })}
              >
                <option value="microbiological">Microbiological Testing</option>
                <option value="chemical">Chemical Analysis</option>
                <option value="physical">Physical Properties</option>
                <option value="sensory">Sensory Evaluation</option>
                <option value="stability">Stability Testing</option>
              </select>
            </div>
            <div>
              <label htmlFor="result" className="block text-sm font-medium text-gray-700">
                Test Result
              </label>
              <select
                id="result"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                value={qualityTest.result}
                onChange={(e) => setQualityTest({ ...qualityTest, result: e.target.value })}
              >
                <option value="pass">Pass</option>
                <option value="fail">Fail</option>
                <option value="pending">Pending</option>
                <option value="retest">Retest Required</option>
              </select>
            </div>
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                Notes
              </label>
              <textarea
                id="notes"
                rows={3}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                placeholder="Enter test observations, measurements, or additional notes..."
                value={qualityTest.notes}
                onChange={(e) => setQualityTest({ ...qualityTest, notes: e.target.value })}
              />
            </div>
          </div>
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={() => setShowQualityModal(false)}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700"
            >
              Record Test Results
            </button>
          </div>
        </form>
      </Modal>
    </>
  )
}
