'use client'

import Link from 'next/link'
import {
  PlusIcon,
  ClipboardDocumentListIcon,
  CogIcon,
  BeakerIcon,
} from '@heroicons/react/24/outline'

const actions = [
  {
    name: 'Add Inventory Item',
    href: '/inventory/new',
    icon: PlusIcon,
    description: 'Add new raw materials or ingredients',
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 hover:bg-blue-100',
  },
  {
    name: 'Create Recipe',
    href: '/recipes/new',
    icon: ClipboardDocumentListIcon,
    description: 'Create a new product recipe',
    color: 'text-green-600',
    bgColor: 'bg-green-50 hover:bg-green-100',
  },
  {
    name: 'Start Production',
    href: '/production/new',
    icon: CogIcon,
    description: 'Begin a new production batch',
    color: 'text-purple-600',
    bgColor: 'bg-purple-50 hover:bg-purple-100',
  },
  {
    name: 'Quality Test',
    href: '/quality/new',
    icon: BeakerIcon,
    description: 'Record quality test results',
    color: 'text-orange-600',
    bgColor: 'bg-orange-50 hover:bg-orange-100',
  },
]

export function QuickActions() {
  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div className="space-y-3">
          {actions.map((action) => (
            <Link
              key={action.name}
              href={action.href}
              className={`block p-3 rounded-lg border border-gray-200 transition-colors ${action.bgColor}`}
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <action.icon className={`h-5 w-5 ${action.color}`} aria-hidden="true" />
                </div>
                <div className="ml-3 flex-1">
                  <p className="text-sm font-medium text-gray-900">{action.name}</p>
                  <p className="text-xs text-gray-500">{action.description}</p>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
}
