{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\nimport {\n  HomeIcon,\n  CubeIcon,\n  BeakerIcon,\n  CogIcon,\n  DocumentTextIcon,\n  UsersIcon,\n  ChartBarIcon,\n  ClipboardDocumentListIcon,\n} from '@heroicons/react/24/outline'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { clsx } from 'clsx'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/', icon: HomeIcon },\n  { name: 'Inventory', href: '/inventory', icon: CubeIcon },\n  { name: 'Recipes', href: '/recipes', icon: ClipboardDocumentListIcon },\n  { name: 'Production', href: '/production', icon: CogIcon },\n  { name: 'Quality', href: '/quality', icon: BeakerIcon },\n  { name: 'Reports', href: '/reports', icon: ChartBarIcon },\n  { name: 'Documents', href: '/documents', icon: DocumentTextIcon },\n  { name: 'Users', href: '/users', icon: UsersIcon },\n]\n\ninterface SidebarProps {\n  open: boolean\n  setOpen: (open: boolean) => void\n}\n\nexport function Sidebar({ open, setOpen }: SidebarProps) {\n  const pathname = usePathname()\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <Transition.Root show={open} as={Fragment}>\n        <Dialog as=\"div\" className=\"relative z-50 lg:hidden\" onClose={setOpen}>\n          <Transition.Child\n            as={Fragment}\n            enter=\"transition-opacity ease-linear duration-300\"\n            enterFrom=\"opacity-0\"\n            enterTo=\"opacity-100\"\n            leave=\"transition-opacity ease-linear duration-300\"\n            leaveFrom=\"opacity-100\"\n            leaveTo=\"opacity-0\"\n          >\n            <div className=\"fixed inset-0 bg-gray-900/80\" />\n          </Transition.Child>\n\n          <div className=\"fixed inset-0 flex\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"transition ease-in-out duration-300 transform\"\n              enterFrom=\"-translate-x-full\"\n              enterTo=\"translate-x-0\"\n              leave=\"transition ease-in-out duration-300 transform\"\n              leaveFrom=\"translate-x-0\"\n              leaveTo=\"-translate-x-full\"\n            >\n              <Dialog.Panel className=\"relative mr-16 flex w-full max-w-xs flex-1\">\n                <Transition.Child\n                  as={Fragment}\n                  enter=\"ease-in-out duration-300\"\n                  enterFrom=\"opacity-0\"\n                  enterTo=\"opacity-100\"\n                  leave=\"ease-in-out duration-300\"\n                  leaveFrom=\"opacity-100\"\n                  leaveTo=\"opacity-0\"\n                >\n                  <div className=\"absolute left-full top-0 flex w-16 justify-center pt-5\">\n                    <button type=\"button\" className=\"-m-2.5 p-2.5\" onClick={() => setOpen(false)}>\n                      <span className=\"sr-only\">Close sidebar</span>\n                      <XMarkIcon className=\"h-6 w-6 text-white\" aria-hidden=\"true\" />\n                    </button>\n                  </div>\n                </Transition.Child>\n                <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4\">\n                  <div className=\"flex h-16 shrink-0 items-center\">\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">SFF</span>\n                    </div>\n                    <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n                  </div>\n                  <nav className=\"flex flex-1 flex-col\">\n                    <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n                      <li>\n                        <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                          {navigation.map((item) => (\n                            <li key={item.name}>\n                              <Link\n                                href={item.href}\n                                className={clsx(\n                                  pathname === item.href\n                                    ? 'bg-green-50 text-green-700'\n                                    : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                                  'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                                )}\n                              >\n                                <item.icon\n                                  className={clsx(\n                                    pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                                    'h-6 w-6 shrink-0'\n                                  )}\n                                  aria-hidden=\"true\"\n                                />\n                                {item.name}\n                              </Link>\n                            </li>\n                          ))}\n                        </ul>\n                      </li>\n                    </ul>\n                  </nav>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </Dialog>\n      </Transition.Root>\n\n      {/* Static sidebar for desktop */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 bg-white px-6 pb-4\">\n          <div className=\"flex h-16 shrink-0 items-center\">\n            <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-sm\">SFF</span>\n            </div>\n            <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n          </div>\n          <nav className=\"flex flex-1 flex-col\">\n            <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n              <li>\n                <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                  {navigation.map((item) => (\n                    <li key={item.name}>\n                      <Link\n                        href={item.href}\n                        className={clsx(\n                          pathname === item.href\n                            ? 'bg-green-50 text-green-700'\n                            : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                          'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                        )}\n                      >\n                        <item.icon\n                          className={clsx(\n                            pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                            'h-6 w-6 shrink-0'\n                          )}\n                          aria-hidden=\"true\"\n                        />\n                        {item.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </li>\n            </ul>\n          </nav>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;;;AAjBA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,kNAAA,CAAA,WAAQ;IAAC;IAC/C;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,oPAAA,CAAA,4BAAyB;IAAC;IACrE;QAAE,MAAM;QAAc,MAAM;QAAe,MAAM,gNAAA,CAAA,UAAO;IAAC;IACzD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,sNAAA,CAAA,aAAU;IAAC;IACtD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,0NAAA,CAAA,eAAY;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kOAAA,CAAA,mBAAgB;IAAC;IAChE;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,oNAAA,CAAA,YAAS;IAAC;CAClD;AAOM,SAAS,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAgB;;IACrD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE;;0BAEE,6LAAC,0LAAA,CAAA,aAAU,CAAC,IAAI;gBAAC,MAAM;gBAAM,IAAI,6JAAA,CAAA,WAAQ;0BACvC,cAAA,6LAAC,kLAAA,CAAA,SAAM;oBAAC,IAAG;oBAAM,WAAU;oBAA0B,SAAS;;sCAC5D,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,6JAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;sCAGjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;gCACf,IAAI,6JAAA,CAAA,WAAQ;gCACZ,OAAM;gCACN,WAAU;gCACV,SAAQ;gCACR,OAAM;gCACN,WAAU;gCACV,SAAQ;0CAER,cAAA,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;oCAAC,WAAU;;sDACtB,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4CACf,IAAI,6JAAA,CAAA,WAAQ;4CACZ,OAAM;4CACN,WAAU;4CACV,SAAQ;4CACR,OAAM;4CACN,WAAU;4CACV,SAAQ;sDAER,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAO,MAAK;oDAAS,WAAU;oDAAe,SAAS,IAAM,QAAQ;;sEACpE,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;4DAAqB,eAAY;;;;;;;;;;;;;;;;;;;;;;sDAI5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,6LAAC;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;8DAE7D,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAG,MAAK;wDAAO,WAAU;kEACxB,cAAA,6LAAC;sEACC,cAAA,6LAAC;gEAAG,MAAK;gEAAO,WAAU;0EACvB,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;kFACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4EACH,MAAM,KAAK,IAAI;4EACf,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;8FAGF,6LAAC,KAAK,IAAI;oFACR,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;oFAEF,eAAY;;;;;;gFAEb,KAAK,IAAI;;;;;;;uEAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiCxC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAE7D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,MAAK;gCAAO,WAAU;0CACxB,cAAA,6LAAC;8CACC,cAAA,6LAAC;wCAAG,MAAK;wCAAO,WAAU;kDACvB,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;sEAGF,6LAAC,KAAK,IAAI;4DACR,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;4DAEF,eAAY;;;;;;wDAEb,KAAK,IAAI;;;;;;;+CAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BtC;GAtIgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Menu, Transition } from '@headlessui/react'\nimport { Bars3Icon, BellIcon } from '@heroicons/react/24/outline'\nimport { useAuth } from '@/components/providers'\nimport { clsx } from 'clsx'\n\ninterface HeaderProps {\n  setSidebarOpen: (open: boolean) => void\n}\n\nexport function Header({ setSidebarOpen }: HeaderProps) {\n  const { user, signOut } = useAuth()\n\n  const userNavigation = [\n    { name: 'Your profile', href: '#' },\n    { name: 'Settings', href: '#' },\n    { name: 'Sign out', href: '#', onClick: signOut },\n  ]\n\n  return (\n    <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n      <button\n        type=\"button\"\n        className=\"-m-2.5 p-2.5 text-gray-700 lg:hidden\"\n        onClick={() => setSidebarOpen(true)}\n      >\n        <span className=\"sr-only\">Open sidebar</span>\n        <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n      </button>\n\n      {/* Separator */}\n      <div className=\"h-6 w-px bg-gray-200 lg:hidden\" aria-hidden=\"true\" />\n\n      <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n        <div className=\"relative flex flex-1\">\n          {/* Search can be added here later */}\n        </div>\n        <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n          <button type=\"button\" className=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\">\n            <span className=\"sr-only\">View notifications</span>\n            <BellIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n\n          {/* Separator */}\n          <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200\" aria-hidden=\"true\" />\n\n          {/* Profile dropdown */}\n          <Menu as=\"div\" className=\"relative\">\n            <Menu.Button className=\"-m-1.5 flex items-center p-1.5\">\n              <span className=\"sr-only\">Open user menu</span>\n              <div className=\"h-8 w-8 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.username?.charAt(0).toUpperCase() || 'U'}\n                </span>\n              </div>\n              <span className=\"hidden lg:flex lg:items-center\">\n                <span className=\"ml-4 text-sm font-semibold leading-6 text-gray-900\" aria-hidden=\"true\">\n                  {user?.username || 'User'}\n                </span>\n                <span className=\"ml-2 text-xs text-gray-500 capitalize\">\n                  ({user?.role?.replace('_', ' ') || 'Role'})\n                </span>\n              </span>\n            </Menu.Button>\n            <Transition\n              as={Fragment}\n              enter=\"transition ease-out duration-100\"\n              enterFrom=\"transform opacity-0 scale-95\"\n              enterTo=\"transform opacity-100 scale-100\"\n              leave=\"transition ease-in duration-75\"\n              leaveFrom=\"transform opacity-100 scale-100\"\n              leaveTo=\"transform opacity-0 scale-95\"\n            >\n              <Menu.Items className=\"absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none\">\n                {userNavigation.map((item) => (\n                  <Menu.Item key={item.name}>\n                    {({ active }) => (\n                      <button\n                        onClick={item.onClick}\n                        className={clsx(\n                          active ? 'bg-gray-50' : '',\n                          'block w-full text-left px-3 py-1 text-sm leading-6 text-gray-900'\n                        )}\n                      >\n                        {item.name}\n                      </button>\n                    )}\n                  </Menu.Item>\n                ))}\n              </Menu.Items>\n            </Transition>\n          </Menu>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;;;AANA;;;;;;AAYO,SAAS,OAAO,EAAE,cAAc,EAAe;;IACpD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAAgB,MAAM;QAAI;QAClC;YAAE,MAAM;YAAY,MAAM;QAAI;QAC9B;YAAE,MAAM;YAAY,MAAM;YAAK,SAAS;QAAQ;KACjD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,eAAe;;kCAE9B,6LAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;wBAAU,eAAY;;;;;;;;;;;;0BAI7C,6LAAC;gBAAI,WAAU;gBAAiC,eAAY;;;;;;0BAE5D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,MAAK;gCAAS,WAAU;;kDAC9B,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;gCAAgD,eAAY;;;;;;0CAG3E,6LAAC,8KAAA,CAAA,OAAI;gCAAC,IAAG;gCAAM,WAAU;;kDACvB,6LAAC,8KAAA,CAAA,OAAI,CAAC,MAAM;wCAAC,WAAU;;0DACrB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb,MAAM,UAAU,OAAO,GAAG,iBAAiB;;;;;;;;;;;0DAGhD,6LAAC;gDAAK,WAAU;;kEACd,6LAAC;wDAAK,WAAU;wDAAqD,eAAY;kEAC9E,MAAM,YAAY;;;;;;kEAErB,6LAAC;wDAAK,WAAU;;4DAAwC;4DACpD,MAAM,MAAM,QAAQ,KAAK,QAAQ;4DAAO;;;;;;;;;;;;;;;;;;;kDAIhD,6LAAC,0LAAA,CAAA,aAAU;wCACT,IAAI,6JAAA,CAAA,WAAQ;wCACZ,OAAM;wCACN,WAAU;wCACV,SAAQ;wCACR,OAAM;wCACN,WAAU;wCACV,SAAQ;kDAER,cAAA,6LAAC,8KAAA,CAAA,OAAI,CAAC,KAAK;4CAAC,WAAU;sDACnB,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;8DACP,CAAC,EAAE,MAAM,EAAE,iBACV,6LAAC;4DACC,SAAS,KAAK,OAAO;4DACrB,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,SAAS,eAAe,IACxB;sEAGD,KAAK,IAAI;;;;;;mDATA,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqB3C;GAtFgB;;QACY,kIAAA,CAAA,UAAO;;;KADnB", "debugId": null}}, {"offset": {"line": 680, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/components/providers'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const { user } = useAuth()\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Sidebar open={sidebarOpen} setOpen={setSidebarOpen} />\n      \n      <div className=\"lg:pl-72\">\n        <Header setSidebarOpen={setSidebarOpen} />\n        \n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAWO,SAAS,WAAW,EAAE,QAAQ,EAAmB;;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAO;gBAAC,MAAM;gBAAa,SAAS;;;;;;0BAErC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,SAAM;wBAAC,gBAAgB;;;;;;kCAExB,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAnBgB;;QAEG,kIAAA,CAAA,UAAO;;;KAFV", "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/ui/modal.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\n\ninterface ModalProps {\n  isOpen: boolean\n  onClose: () => void\n  title: string\n  children: React.ReactNode\n  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl'\n}\n\nexport function Modal({ isOpen, onClose, title, children, maxWidth = 'lg' }: ModalProps) {\n  const maxWidthClasses = {\n    sm: 'sm:max-w-sm',\n    md: 'sm:max-w-md',\n    lg: 'sm:max-w-lg',\n    xl: 'sm:max-w-xl',\n    '2xl': 'sm:max-w-2xl'\n  }\n\n  return (\n    <Transition.Root show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={onClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-out duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in duration-200\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 z-10 overflow-y-auto\">\n          <div className=\"flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"ease-out duration-300\"\n              enterFrom=\"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95\"\n              enterTo=\"opacity-100 translate-y-0 sm:scale-100\"\n              leave=\"ease-in duration-200\"\n              leaveFrom=\"opacity-100 translate-y-0 sm:scale-100\"\n              leaveTo=\"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95\"\n            >\n              <Dialog.Panel className={`relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full ${maxWidthClasses[maxWidth]} sm:p-6`}>\n                <div className=\"absolute right-0 top-0 hidden pr-4 pt-4 sm:block\">\n                  <button\n                    type=\"button\"\n                    className=\"rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2\"\n                    onClick={onClose}\n                  >\n                    <span className=\"sr-only\">Close</span>\n                    <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                  </button>\n                </div>\n                <div className=\"sm:flex sm:items-start\">\n                  <div className=\"mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full\">\n                    <Dialog.Title as=\"h3\" className=\"text-lg font-semibold leading-6 text-gray-900 mb-4\">\n                      {title}\n                    </Dialog.Title>\n                    <div className=\"mt-2\">\n                      {children}\n                    </div>\n                  </div>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </div>\n      </Dialog>\n    </Transition.Root>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAcO,SAAS,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAc;IACrF,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;IACT;IAEA,qBACE,6LAAC,0LAAA,CAAA,aAAU,CAAC,IAAI;QAAC,MAAM;QAAQ,IAAI,6JAAA,CAAA,WAAQ;kBACzC,cAAA,6LAAC,kLAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,6JAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,6JAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;gCAAC,WAAW,CAAC,2HAA2H,EAAE,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC;;kDACvL,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS;;8DAET,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;;;;;;kDAG/C,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;oDAAC,IAAG;oDAAK,WAAU;8DAC7B;;;;;;8DAEH,6LAAC;oDAAI,WAAU;8DACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvB;KA/DgB", "debugId": null}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createClientComponentClient } from '@supabase/auth-helpers-nextjs'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Client component client\nexport const createSupabaseClient = () => createClientComponentClient()\n\n// Admin client (server-side only)\nexport const createSupabaseAdminClient = () => {\n  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n  return createClient(supabaseUrl, serviceRoleKey, {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  })\n}\n\n// Database types\nexport interface Database {\n  public: {\n    Tables: {\n      profiles: {\n        Row: {\n          id: string\n          email: string\n          full_name: string\n          role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department: string | null\n          phone: string | null\n          is_active: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          email: string\n          full_name: string\n          role?: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department?: string | null\n          phone?: string | null\n          is_active?: boolean\n        }\n        Update: {\n          email?: string\n          full_name?: string\n          role?: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department?: string | null\n          phone?: string | null\n          is_active?: boolean\n        }\n      }\n      inventory_categories: {\n        Row: {\n          id: string\n          name: string\n          description: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          description?: string | null\n        }\n        Update: {\n          name?: string\n          description?: string | null\n        }\n      }\n      inventory_items: {\n        Row: {\n          id: string\n          name: string\n          sku: string\n          category_id: string | null\n          description: string | null\n          unit_of_measure: string\n          current_stock: number\n          minimum_stock: number\n          maximum_stock: number | null\n          unit_cost: number\n          supplier_info: any | null\n          storage_conditions: string | null\n          expiry_date: string | null\n          batch_number: string | null\n          is_active: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          sku: string\n          category_id?: string | null\n          description?: string | null\n          unit_of_measure: string\n          current_stock?: number\n          minimum_stock?: number\n          maximum_stock?: number | null\n          unit_cost?: number\n          supplier_info?: any | null\n          storage_conditions?: string | null\n          expiry_date?: string | null\n          batch_number?: string | null\n          is_active?: boolean\n        }\n        Update: {\n          name?: string\n          sku?: string\n          category_id?: string | null\n          description?: string | null\n          unit_of_measure?: string\n          current_stock?: number\n          minimum_stock?: number\n          maximum_stock?: number | null\n          unit_cost?: number\n          supplier_info?: any | null\n          storage_conditions?: string | null\n          expiry_date?: string | null\n          batch_number?: string | null\n          is_active?: boolean\n        }\n      }\n      recipes: {\n        Row: {\n          id: string\n          name: string\n          code: string\n          description: string | null\n          category: string | null\n          version: number\n          batch_size: number\n          unit_of_measure: string\n          estimated_cost: number | null\n          preparation_time: number | null\n          instructions: string | null\n          notes: string | null\n          is_active: boolean\n          created_by: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          code: string\n          description?: string | null\n          category?: string | null\n          version?: number\n          batch_size: number\n          unit_of_measure: string\n          estimated_cost?: number | null\n          preparation_time?: number | null\n          instructions?: string | null\n          notes?: string | null\n          is_active?: boolean\n          created_by?: string | null\n        }\n        Update: {\n          name?: string\n          code?: string\n          description?: string | null\n          category?: string | null\n          version?: number\n          batch_size?: number\n          unit_of_measure?: string\n          estimated_cost?: number | null\n          preparation_time?: number | null\n          instructions?: string | null\n          notes?: string | null\n          is_active?: boolean\n        }\n      }\n      production_batches: {\n        Row: {\n          id: string\n          batch_number: string\n          recipe_id: string\n          batch_type: 'test' | 'production'\n          planned_quantity: number\n          actual_quantity: number | null\n          unit_of_measure: string\n          status: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date: string | null\n          actual_start_date: string | null\n          planned_end_date: string | null\n          actual_end_date: string | null\n          production_cost: number | null\n          yield_percentage: number | null\n          quality_approved: boolean | null\n          notes: string | null\n          created_by: string | null\n          assigned_to: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          batch_number: string\n          recipe_id: string\n          batch_type: 'test' | 'production'\n          planned_quantity: number\n          actual_quantity?: number | null\n          unit_of_measure: string\n          status?: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority?: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date?: string | null\n          actual_start_date?: string | null\n          planned_end_date?: string | null\n          actual_end_date?: string | null\n          production_cost?: number | null\n          yield_percentage?: number | null\n          quality_approved?: boolean | null\n          notes?: string | null\n          created_by?: string | null\n          assigned_to?: string | null\n        }\n        Update: {\n          batch_number?: string\n          recipe_id?: string\n          batch_type?: 'test' | 'production'\n          planned_quantity?: number\n          actual_quantity?: number | null\n          unit_of_measure?: string\n          status?: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority?: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date?: string | null\n          actual_start_date?: string | null\n          planned_end_date?: string | null\n          actual_end_date?: string | null\n          production_cost?: number | null\n          yield_percentage?: number | null\n          quality_approved?: boolean | null\n          notes?: string | null\n          assigned_to?: string | null\n        }\n      }\n    }\n    Views: {\n      [_ in never]: never\n    }\n    Functions: {\n      check_recipe_availability: {\n        Args: {\n          recipe_uuid: string\n          batch_size_multiplier?: number\n        }\n        Returns: {\n          item_id: string\n          item_name: string\n          required_quantity: number\n          available_quantity: number\n          is_sufficient: boolean\n        }[]\n      }\n      calculate_recipe_cost: {\n        Args: {\n          recipe_uuid: string\n          batch_size_multiplier?: number\n        }\n        Returns: number\n      }\n    }\n    Enums: {\n      user_role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAGoB;AAHpB;AACA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,uBAAuB,IAAM,CAAA,GAAA,2KAAA,CAAA,8BAA2B,AAAD;AAG7D,MAAM,4BAA4B;IACvC,MAAM,iBAAiB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB;IAC5D,OAAO,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa,gBAAgB;QAC/C,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 973, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/lib/database.ts"], "sourcesContent": ["import { createSupabaseClient } from './supabase'\n\nconst supabase = createSupabaseClient()\n\n// Inventory Items\nexport interface InventoryItem {\n  id: string\n  name: string\n  sku: string\n  category_id: string\n  current_stock: number\n  minimum_stock: number\n  unit_of_measure: string\n  unit_cost: number\n  supplier?: string\n  description?: string\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface InventoryCategory {\n  id: string\n  name: string\n  description?: string\n  is_active: boolean\n}\n\n// Recipes\nexport interface Recipe {\n  id: string\n  name: string\n  code: string\n  category: string\n  batch_size: number\n  unit_of_measure: string\n  instructions?: string\n  is_active: boolean\n  version: number\n  created_at: string\n  updated_at: string\n}\n\n// Production Batches\nexport interface ProductionBatch {\n  id: string\n  batch_number: string\n  recipe_id: string\n  batch_type: 'test' | 'production'\n  planned_quantity: number\n  actual_quantity?: number\n  unit_of_measure: string\n  status: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n  priority: 'low' | 'normal' | 'high' | 'urgent'\n  planned_start_date?: string\n  planned_end_date?: string\n  actual_start_date?: string\n  actual_end_date?: string\n  assigned_to?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\n// Quality Documents\nexport interface QualityDocument {\n  id: string\n  document_type: 'msds' | 'coa' | 'tds' | 'quality_spec' | 'lab_report'\n  title: string\n  document_number: string\n  version: string\n  item_id?: string\n  batch_id?: string\n  status: 'draft' | 'review' | 'approved' | 'expired'\n  valid_from?: string\n  valid_until?: string\n  file_path?: string\n  created_by: string\n  created_at: string\n  updated_at: string\n}\n\n// Database service functions\nexport class DatabaseService {\n  // Inventory Items\n  static async getInventoryItems(): Promise<InventoryItem[]> {\n    try {\n      const { data, error } = await supabase\n        .from('inventory_items')\n        .select(`\n          *,\n          inventory_categories(name)\n        `)\n        .order('name')\n\n      if (error) {\n        console.error('Error fetching inventory items:', error)\n        // Only return demo data if it's a connection error\n        if (error.message.includes('Failed to fetch') || error.message.includes('network')) {\n          return this.getDemoInventoryItems()\n        }\n        return []\n      }\n\n      // Process the data to match the expected interface\n      const processedItems = (data || []).map(item => {\n        // Calculate status based on stock levels\n        const currentStock = parseFloat(item.current_stock?.toString() || '0')\n        const minimumStock = parseFloat(item.minimum_stock?.toString() || '0')\n\n        let status: 'in_stock' | 'low_stock' | 'out_of_stock' = 'in_stock'\n        if (currentStock === 0) {\n          status = 'out_of_stock'\n        } else if (currentStock <= minimumStock) {\n          status = 'low_stock'\n        }\n\n        return {\n          ...item,\n          current_stock: currentStock,\n          minimum_stock: minimumStock,\n          maximum_stock: parseFloat(item.maximum_stock?.toString() || '0'),\n          unit_cost: parseFloat(item.unit_cost?.toString() || '0'),\n          status,\n          category_name: item.inventory_categories?.name || 'Unknown',\n          is_active: item.is_active !== false // Default to true if null\n        }\n      })\n\n      return processedItems\n    } catch (error) {\n      console.error('Database connection error:', error)\n      return this.getDemoInventoryItems()\n    }\n  }\n\n  static getDemoInventoryItems(): InventoryItem[] {\n    return [\n      {\n        id: 'demo_item_1',\n        name: 'Vanilla Extract Premium',\n        sku: 'VAN-001',\n        category_id: 'demo_cat_1',\n        current_stock: 25.5,\n        minimum_stock: 10.0,\n        unit_of_measure: 'liters',\n        unit_cost: 12.50,\n        supplier: 'Premium Ingredients Co.',\n        description: 'High-quality vanilla extract for premium flavoring applications',\n        is_active: true,\n        created_at: '2024-01-15T08:00:00Z',\n        updated_at: '2024-01-15T08:00:00Z'\n      },\n      {\n        id: 'demo_item_2',\n        name: 'Strawberry Concentrate',\n        sku: 'STR-002',\n        category_id: 'demo_cat_1',\n        current_stock: 8.2,\n        minimum_stock: 15.0,\n        unit_of_measure: 'liters',\n        unit_cost: 18.75,\n        supplier: 'Fruit Essences Ltd.',\n        description: 'Natural strawberry concentrate for beverage and dessert applications',\n        is_active: true,\n        created_at: '2024-01-20T10:30:00Z',\n        updated_at: '2024-01-20T10:30:00Z'\n      },\n      {\n        id: 'demo_item_3',\n        name: 'Citric Acid Food Grade',\n        sku: 'CIT-003',\n        category_id: 'demo_cat_2',\n        current_stock: 45.0,\n        minimum_stock: 20.0,\n        unit_of_measure: 'kg',\n        unit_cost: 3.25,\n        supplier: 'Chemical Solutions Inc.',\n        description: 'Food-grade citric acid for pH adjustment and preservation',\n        is_active: true,\n        created_at: '2024-02-01T14:15:00Z',\n        updated_at: '2024-02-01T14:15:00Z'\n      },\n      {\n        id: 'demo_item_4',\n        name: 'Glass Bottles 500ml',\n        sku: 'BTL-004',\n        category_id: 'demo_cat_3',\n        current_stock: 0,\n        minimum_stock: 100,\n        unit_of_measure: 'pieces',\n        unit_cost: 0.85,\n        supplier: 'Packaging Solutions Ltd.',\n        description: 'Clear glass bottles with screw caps for beverage packaging',\n        is_active: true,\n        created_at: '2024-02-10T09:45:00Z',\n        updated_at: '2024-02-10T09:45:00Z'\n      },\n      {\n        id: 'demo_item_5',\n        name: 'Natural Lemon Oil',\n        sku: 'LEM-005',\n        category_id: 'demo_cat_1',\n        current_stock: 3.8,\n        minimum_stock: 5.0,\n        unit_of_measure: 'liters',\n        unit_cost: 45.00,\n        supplier: 'Essential Oils Direct',\n        description: 'Cold-pressed natural lemon oil for citrus flavoring',\n        is_active: true,\n        created_at: '2024-02-15T11:20:00Z',\n        updated_at: '2024-02-15T11:20:00Z'\n      }\n    ]\n  }\n\n  static async createInventoryItem(item: Omit<InventoryItem, 'id' | 'created_at' | 'updated_at'>): Promise<InventoryItem | null> {\n    try {\n      // Map the item data to match the database schema\n      const dbItem = {\n        name: item.name,\n        sku: item.sku,\n        category_id: item.category_id,\n        description: item.description || null,\n        unit_of_measure: item.unit_of_measure,\n        current_stock: item.current_stock || 0,\n        minimum_stock: item.minimum_stock || 0,\n        maximum_stock: item.maximum_stock || null,\n        unit_cost: item.unit_cost || 0,\n        supplier_info: item.supplier_name ? {\n          name: item.supplier_name,\n          code: item.supplier_code,\n          batch_number: item.batch_number\n        } : null,\n        storage_conditions: item.requires_refrigeration ? 'Refrigerated' : null,\n        expiry_date: item.expiry_date || null,\n        batch_number: item.batch_number || null,\n        is_active: true\n      }\n\n      const { data, error } = await supabase\n        .from('inventory_items')\n        .insert([dbItem])\n        .select(`\n          *,\n          inventory_categories(name)\n        `)\n        .single()\n\n      if (error) {\n        console.error('Error creating inventory item:', error)\n        // Only use demo fallback for connection errors\n        if (error.message.includes('Failed to fetch') || error.message.includes('network')) {\n          return this.createDemoInventoryItem(item)\n        }\n        return null\n      }\n\n      // Process the returned data to match the expected interface\n      if (data) {\n        const processedItem = {\n          ...data,\n          current_stock: parseFloat(data.current_stock?.toString() || '0'),\n          minimum_stock: parseFloat(data.minimum_stock?.toString() || '0'),\n          maximum_stock: parseFloat(data.maximum_stock?.toString() || '0'),\n          unit_cost: parseFloat(data.unit_cost?.toString() || '0'),\n          category_name: data.inventory_categories?.name || 'Unknown',\n          status: 'in_stock' as const\n        }\n        return processedItem\n      }\n\n      return data\n    } catch (error) {\n      console.error('Database connection error:', error)\n      return this.createDemoInventoryItem(item)\n    }\n  }\n\n  static createDemoInventoryItem(item: Omit<InventoryItem, 'id' | 'created_at' | 'updated_at'>): InventoryItem {\n    return {\n      ...item,\n      id: `demo_item_${Date.now()}`,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }\n  }\n\n  static async updateInventoryItem(id: string, updates: Partial<InventoryItem>): Promise<InventoryItem | null> {\n    const { data, error } = await supabase\n      .from('inventory_items')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error updating inventory item:', error)\n      return null\n    }\n\n    return data\n  }\n\n  // Inventory Categories\n  static async getInventoryCategories(): Promise<InventoryCategory[]> {\n    try {\n      const { data, error } = await supabase\n        .from('inventory_categories')\n        .select('*')\n        .order('name')\n\n      if (error) {\n        console.error('Error fetching inventory categories:', error)\n        // Only return demo data if it's a connection error\n        if (error.message.includes('Failed to fetch') || error.message.includes('network')) {\n          return this.getDemoInventoryCategories()\n        }\n        return []\n      }\n\n      // Return actual data from database (even if empty)\n      // Add is_active: true to match interface since DB doesn't have this column\n      const categoriesWithActive = (data || []).map(cat => ({\n        ...cat,\n        is_active: true\n      }))\n\n      return categoriesWithActive\n    } catch (error) {\n      console.error('Database connection error:', error)\n      return this.getDemoInventoryCategories()\n    }\n  }\n\n  static getDemoInventoryCategories(): InventoryCategory[] {\n    return [\n      {\n        id: 'demo_cat_1',\n        name: 'Flavoring Agents',\n        description: 'Natural and artificial flavoring compounds and extracts',\n        is_active: true\n      },\n      {\n        id: 'demo_cat_2',\n        name: 'Preservatives & Additives',\n        description: 'Food-grade preservatives, stabilizers, and additives',\n        is_active: true\n      },\n      {\n        id: 'demo_cat_3',\n        name: 'Packaging Materials',\n        description: 'Bottles, caps, labels, and packaging supplies',\n        is_active: true\n      },\n      {\n        id: 'demo_cat_4',\n        name: 'Raw Materials',\n        description: 'Base ingredients and raw materials for production',\n        is_active: true\n      },\n      {\n        id: 'demo_cat_5',\n        name: 'Quality Control',\n        description: 'Testing materials and quality control supplies',\n        is_active: true\n      }\n    ]\n  }\n\n  // Recipes\n  static async getRecipes(): Promise<Recipe[]> {\n    const { data, error } = await supabase\n      .from('recipes')\n      .select('*')\n      .eq('is_active', true)\n      .order('name')\n\n    if (error) {\n      console.error('Error fetching recipes:', error)\n      return []\n    }\n\n    return data || []\n  }\n\n  static async createRecipe(recipe: Omit<Recipe, 'id' | 'created_at' | 'updated_at'>): Promise<Recipe | null> {\n    const { data, error } = await supabase\n      .from('recipes')\n      .insert([recipe])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error creating recipe:', error)\n      return null\n    }\n\n    return data\n  }\n\n  // Production Batches\n  static async getProductionBatches(): Promise<ProductionBatch[]> {\n    const { data, error } = await supabase\n      .from('production_batches')\n      .select(`\n        *,\n        recipes(name, code)\n      `)\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      console.error('Error fetching production batches:', error)\n      return []\n    }\n\n    return data || []\n  }\n\n  static async createProductionBatch(batch: Omit<ProductionBatch, 'id' | 'created_at' | 'updated_at'>): Promise<ProductionBatch | null> {\n    const { data, error } = await supabase\n      .from('production_batches')\n      .insert([batch])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error creating production batch:', error)\n      return null\n    }\n\n    return data\n  }\n\n  // Quality Documents\n  static async getQualityDocuments(): Promise<QualityDocument[]> {\n    const { data, error } = await supabase\n      .from('quality_documents')\n      .select('*')\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      console.error('Error fetching quality documents:', error)\n      return []\n    }\n\n    return data || []\n  }\n\n  static async createQualityDocument(doc: Omit<QualityDocument, 'id' | 'created_at' | 'updated_at'>): Promise<QualityDocument | null> {\n    try {\n      const { data, error } = await supabase\n        .from('quality_documents')\n        .insert([doc])\n        .select()\n        .single()\n\n      if (error) {\n        console.error('Error creating quality document:', error)\n        // Only use demo fallback for connection errors\n        if (error.message.includes('Failed to fetch') || error.message.includes('network')) {\n          return {\n            ...doc,\n            id: `demo_doc_${Date.now()}`,\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n          }\n        }\n        return null\n      }\n\n      return data\n    } catch (error) {\n      console.error('Database connection error:', error)\n      return {\n        ...doc,\n        id: `demo_doc_${Date.now()}`,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }\n    }\n  }\n\n  // User Management\n  static async getUsers(): Promise<any[]> {\n    try {\n      const { data, error } = await supabase\n        .from('profiles')\n        .select('*')\n        .order('created_at', { ascending: false })\n\n      if (error) {\n        // Silently handle database errors and return demo users\n        // This is expected when the database is empty or has constraints\n        return this.getDemoUsers()\n      }\n\n      // If no users in database, return demo users\n      if (!data || data.length === 0) {\n        return this.getDemoUsers()\n      }\n\n      return data\n    } catch (error) {\n      // Silently handle connection errors and return demo users\n      return this.getDemoUsers()\n    }\n  }\n\n  static getDemoUsers(): any[] {\n    return [\n      {\n        id: 'demo_admin',\n        username: 'admin',\n        full_name: 'System Administrator',\n        email: '<EMAIL>',\n        role: 'admin',\n        department: 'IT',\n        phone: '+****************',\n        is_active: true,\n        created_at: '2024-01-15T08:00:00Z',\n        updated_at: '2024-01-15T08:00:00Z'\n      },\n      {\n        id: 'demo_quality',\n        username: 'quality',\n        full_name: 'Sarah Johnson',\n        email: '<EMAIL>',\n        role: 'quality_manager',\n        department: 'Quality Assurance',\n        phone: '+****************',\n        is_active: true,\n        created_at: '2024-02-01T10:00:00Z',\n        updated_at: '2024-02-01T10:00:00Z'\n      },\n      {\n        id: 'demo_production',\n        username: 'production',\n        full_name: 'Mike Wilson',\n        email: '<EMAIL>',\n        role: 'production_manager',\n        department: 'Production',\n        phone: '+****************',\n        is_active: true,\n        created_at: '2024-02-15T14:30:00Z',\n        updated_at: '2024-02-15T14:30:00Z'\n      },\n      {\n        id: 'demo_employee',\n        username: 'employee',\n        full_name: 'Emily Davis',\n        email: '<EMAIL>',\n        role: 'employee',\n        department: 'Production',\n        phone: '+****************',\n        is_active: true,\n        created_at: '2024-03-01T09:00:00Z',\n        updated_at: '2024-03-01T09:00:00Z'\n      }\n    ]\n  }\n\n  static async createUser(userData: {\n    username: string\n    email: string\n    password_hash: string\n    role: string\n    full_name: string\n    department: string\n    phone?: string\n    is_active: boolean\n  }): Promise<any | null> {\n    try {\n      // For demo purposes, since we can't create auth users directly,\n      // we'll simulate user creation and return a demo user object\n      const newUser = {\n        id: `demo_${Date.now()}`,\n        username: userData.username,\n        email: userData.email,\n        full_name: userData.full_name,\n        role: userData.role,\n        department: userData.department,\n        phone: userData.phone || null,\n        is_active: userData.is_active,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }\n\n      // In a real implementation, this would create the user in the database\n      // For now, we'll just return the user object for demo purposes\n      return newUser\n\n      // Commented out actual database insertion due to auth constraints\n      /*\n      const { data, error } = await supabase\n        .from('profiles')\n        .insert([{\n          ...userData,\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        }])\n        .select()\n        .single()\n\n      if (error) {\n        console.error('Error creating user:', error)\n        return null\n      }\n\n      return data\n      */\n    } catch (error) {\n      return null\n    }\n  }\n\n  static async updateUser(id: string, updates: any): Promise<any | null> {\n    const { data, error } = await supabase\n      .from('profiles')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error updating user:', error)\n      return null\n    }\n\n    return data\n  }\n\n  static async toggleUserStatus(id: string, isActive: boolean): Promise<boolean> {\n    try {\n      // For demo users (those with demo_ prefix), just return success\n      if (id.startsWith('demo_')) {\n        return true\n      }\n\n      const { error } = await supabase\n        .from('profiles')\n        .update({ is_active: isActive, updated_at: new Date().toISOString() })\n        .eq('id', id)\n\n      if (error) {\n        return false\n      }\n\n      return true\n    } catch (error) {\n      return false\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;AAiF7B,MAAM;IACX,kBAAkB;IAClB,aAAa,oBAA8C;QACzD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,mBACL,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,KAAK,CAAC;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,mDAAmD;gBACnD,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,sBAAsB,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;oBAClF,OAAO,IAAI,CAAC,qBAAqB;gBACnC;gBACA,OAAO,EAAE;YACX;YAEA,mDAAmD;YACnD,MAAM,iBAAiB,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAA;gBACtC,yCAAyC;gBACzC,MAAM,eAAe,WAAW,KAAK,aAAa,EAAE,cAAc;gBAClE,MAAM,eAAe,WAAW,KAAK,aAAa,EAAE,cAAc;gBAElE,IAAI,SAAoD;gBACxD,IAAI,iBAAiB,GAAG;oBACtB,SAAS;gBACX,OAAO,IAAI,gBAAgB,cAAc;oBACvC,SAAS;gBACX;gBAEA,OAAO;oBACL,GAAG,IAAI;oBACP,eAAe;oBACf,eAAe;oBACf,eAAe,WAAW,KAAK,aAAa,EAAE,cAAc;oBAC5D,WAAW,WAAW,KAAK,SAAS,EAAE,cAAc;oBACpD;oBACA,eAAe,KAAK,oBAAoB,EAAE,QAAQ;oBAClD,WAAW,KAAK,SAAS,KAAK,MAAM,0BAA0B;gBAChE;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,IAAI,CAAC,qBAAqB;QACnC;IACF;IAEA,OAAO,wBAAyC;QAC9C,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;SACD;IACH;IAEA,aAAa,oBAAoB,IAA6D,EAAiC;QAC7H,IAAI;YACF,iDAAiD;YACjD,MAAM,SAAS;gBACb,MAAM,KAAK,IAAI;gBACf,KAAK,KAAK,GAAG;gBACb,aAAa,KAAK,WAAW;gBAC7B,aAAa,KAAK,WAAW,IAAI;gBACjC,iBAAiB,KAAK,eAAe;gBACrC,eAAe,KAAK,aAAa,IAAI;gBACrC,eAAe,KAAK,aAAa,IAAI;gBACrC,eAAe,KAAK,aAAa,IAAI;gBACrC,WAAW,KAAK,SAAS,IAAI;gBAC7B,eAAe,KAAK,aAAa,GAAG;oBAClC,MAAM,KAAK,aAAa;oBACxB,MAAM,KAAK,aAAa;oBACxB,cAAc,KAAK,YAAY;gBACjC,IAAI;gBACJ,oBAAoB,KAAK,sBAAsB,GAAG,iBAAiB;gBACnE,aAAa,KAAK,WAAW,IAAI;gBACjC,cAAc,KAAK,YAAY,IAAI;gBACnC,WAAW;YACb;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,mBACL,MAAM,CAAC;gBAAC;aAAO,EACf,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,+CAA+C;gBAC/C,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,sBAAsB,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;oBAClF,OAAO,IAAI,CAAC,uBAAuB,CAAC;gBACtC;gBACA,OAAO;YACT;YAEA,4DAA4D;YAC5D,IAAI,MAAM;gBACR,MAAM,gBAAgB;oBACpB,GAAG,IAAI;oBACP,eAAe,WAAW,KAAK,aAAa,EAAE,cAAc;oBAC5D,eAAe,WAAW,KAAK,aAAa,EAAE,cAAc;oBAC5D,eAAe,WAAW,KAAK,aAAa,EAAE,cAAc;oBAC5D,WAAW,WAAW,KAAK,SAAS,EAAE,cAAc;oBACpD,eAAe,KAAK,oBAAoB,EAAE,QAAQ;oBAClD,QAAQ;gBACV;gBACA,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,IAAI,CAAC,uBAAuB,CAAC;QACtC;IACF;IAEA,OAAO,wBAAwB,IAA6D,EAAiB;QAC3G,OAAO;YACL,GAAG,IAAI;YACP,IAAI,CAAC,UAAU,EAAE,KAAK,GAAG,IAAI;YAC7B,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;IACF;IAEA,aAAa,oBAAoB,EAAU,EAAE,OAA+B,EAAiC;QAC3G,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,mBACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,uBAAuB;IACvB,aAAa,yBAAuD;QAClE,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,wBACL,MAAM,CAAC,KACP,KAAK,CAAC;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,mDAAmD;gBACnD,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,sBAAsB,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;oBAClF,OAAO,IAAI,CAAC,0BAA0B;gBACxC;gBACA,OAAO,EAAE;YACX;YAEA,mDAAmD;YACnD,2EAA2E;YAC3E,MAAM,uBAAuB,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAA,MAAO,CAAC;oBACpD,GAAG,GAAG;oBACN,WAAW;gBACb,CAAC;YAED,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,IAAI,CAAC,0BAA0B;QACxC;IACF;IAEA,OAAO,6BAAkD;QACvD,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;SACD;IACH;IAEA,UAAU;IACV,aAAa,aAAgC;QAC3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,EAAE;QACX;QAEA,OAAO,QAAQ,EAAE;IACnB;IAEA,aAAa,aAAa,MAAwD,EAA0B;QAC1G,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,WACL,MAAM,CAAC;YAAC;SAAO,EACf,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,qBAAqB;IACrB,aAAa,uBAAmD;QAC9D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,sBACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO,EAAE;QACX;QAEA,OAAO,QAAQ,EAAE;IACnB;IAEA,aAAa,sBAAsB,KAAgE,EAAmC;QACpI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,sBACL,MAAM,CAAC;YAAC;SAAM,EACd,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,oBAAoB;IACpB,aAAa,sBAAkD;QAC7D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO,EAAE;QACX;QAEA,OAAO,QAAQ,EAAE;IACnB;IAEA,aAAa,sBAAsB,GAA8D,EAAmC;QAClI,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC;gBAAC;aAAI,EACZ,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,+CAA+C;gBAC/C,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,sBAAsB,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;oBAClF,OAAO;wBACL,GAAG,GAAG;wBACN,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;wBAC5B,YAAY,IAAI,OAAO,WAAW;wBAClC,YAAY,IAAI,OAAO,WAAW;oBACpC;gBACF;gBACA,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;gBACL,GAAG,GAAG;gBACN,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;gBAC5B,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;QACF;IACF;IAEA,kBAAkB;IAClB,aAAa,WAA2B;QACtC,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,wDAAwD;gBACxD,iEAAiE;gBACjE,OAAO,IAAI,CAAC,YAAY;YAC1B;YAEA,6CAA6C;YAC7C,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;gBAC9B,OAAO,IAAI,CAAC,YAAY;YAC1B;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,0DAA0D;YAC1D,OAAO,IAAI,CAAC,YAAY;QAC1B;IACF;IAEA,OAAO,eAAsB;QAC3B,OAAO;YACL;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;SACD;IACH;IAEA,aAAa,WAAW,QASvB,EAAuB;QACtB,IAAI;YACF,gEAAgE;YAChE,6DAA6D;YAC7D,MAAM,UAAU;gBACd,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;gBACxB,UAAU,SAAS,QAAQ;gBAC3B,OAAO,SAAS,KAAK;gBACrB,WAAW,SAAS,SAAS;gBAC7B,MAAM,SAAS,IAAI;gBACnB,YAAY,SAAS,UAAU;gBAC/B,OAAO,SAAS,KAAK,IAAI;gBACzB,WAAW,SAAS,SAAS;gBAC7B,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,uEAAuE;YACvE,+DAA+D;YAC/D,OAAO;QAEP,kEAAkE;QAClE;;;;;;;;;;;;;;;;;MAiBA,GACF,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,aAAa,WAAW,EAAU,EAAE,OAAY,EAAuB;QACrE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,aAAa,iBAAiB,EAAU,EAAE,QAAiB,EAAoB;QAC7E,IAAI;YACF,gEAAgE;YAChE,IAAI,GAAG,UAAU,CAAC,UAAU;gBAC1B,OAAO;YACT;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,YACL,MAAM,CAAC;gBAAE,WAAW;gBAAU,YAAY,IAAI,OAAO,WAAW;YAAG,GACnE,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO;gBACT,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 1471, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/app/inventory/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { useAuth } from '@/components/providers'\nimport { Modal } from '@/components/ui/modal'\nimport { DatabaseService, InventoryCategory } from '@/lib/database'\nimport {\n  PlusIcon,\n  MagnifyingGlassIcon,\n  ExclamationTriangleIcon,\n  CheckCircleIcon,\n  XMarkIcon,\n  EyeIcon,\n  DocumentDuplicateIcon,\n  CurrencyDollarIcon,\n  ArchiveBoxIcon,\n  ClipboardDocumentListIcon\n} from '@heroicons/react/24/outline'\n\ninterface InventoryItem {\n  id: string\n  name: string\n  sku: string\n  category_id: string\n  category_name?: string\n  current_stock: number\n  minimum_stock: number\n  unit_of_measure: string\n  unit_cost: number\n  description?: string\n  is_active: boolean\n  status?: 'in_stock' | 'low_stock' | 'out_of_stock'\n}\n\nexport default function InventoryPage() {\n  const { user } = useAuth()\n  const [items, setItems] = useState<InventoryItem[]>([])\n  const [categories, setCategories] = useState<InventoryCategory[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [showAddModal, setShowAddModal] = useState(false)\n  const [currentStep, setCurrentStep] = useState(1)\n  const [isCreating, setIsCreating] = useState(false)\n  const [validationErrors, setValidationErrors] = useState<string[]>([])\n\n  // Enhanced new item state with more professional fields\n  const [newItem, setNewItem] = useState({\n    name: '',\n    sku: '',\n    category_id: '',\n    current_stock: 0,\n    minimum_stock: 0,\n    maximum_stock: 0,\n    reorder_point: 0,\n    unit_of_measure: 'liters',\n    unit_cost: 0,\n    supplier_name: '',\n    supplier_code: '',\n    batch_number: '',\n    expiry_date: '',\n    location: '',\n    description: '',\n    notes: '',\n    is_hazardous: false,\n    requires_refrigeration: false\n  })\n\n  // Filter and sort states\n  const [selectedCategory, setSelectedCategory] = useState('all')\n  const [selectedStatus, setSelectedStatus] = useState('all')\n  const [sortBy, setSortBy] = useState('name')\n  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')\n\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setLoading(true)\n\n        // Load inventory items and categories from database\n        const [inventoryItems, inventoryCategories] = await Promise.all([\n          DatabaseService.getInventoryItems(),\n          DatabaseService.getInventoryCategories()\n        ])\n\n        // Process inventory items to add status and category name\n        const processedItems = inventoryItems.map(item => ({\n          ...item,\n          current_stock: parseFloat(item.current_stock.toString()),\n          minimum_stock: parseFloat(item.minimum_stock.toString()),\n          unit_cost: parseFloat(item.unit_cost.toString()),\n          status: parseFloat(item.current_stock.toString()) <= parseFloat(item.minimum_stock.toString())\n            ? 'low_stock' as const\n            : parseFloat(item.current_stock.toString()) === 0\n            ? 'out_of_stock' as const\n            : 'in_stock' as const,\n          category_name: inventoryCategories.find(cat => cat.id === item.category_id)?.name || 'Unknown'\n        }))\n\n        setItems(processedItems)\n        setCategories(inventoryCategories)\n\n        // Set default category for new items\n        if (inventoryCategories.length > 0 && !newItem.category_id) {\n          setNewItem(prev => ({ ...prev, category_id: inventoryCategories[0].id }))\n        }\n      } catch (error) {\n        console.error('Error loading inventory data:', error)\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    loadData()\n  }, [])\n\n  // Enhanced filtering and sorting\n  const filteredItems = items\n    .filter(item => {\n      // Search filter\n      const matchesSearch = searchTerm === '' ||\n        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        item.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        (item.category_name && item.category_name.toLowerCase().includes(searchTerm.toLowerCase())) ||\n        (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()))\n\n      // Category filter\n      const matchesCategory = selectedCategory === 'all' || item.category_id === selectedCategory\n\n      // Status filter\n      const matchesStatus = selectedStatus === 'all' || item.status === selectedStatus\n\n      return matchesSearch && matchesCategory && matchesStatus\n    })\n    .sort((a, b) => {\n      let aValue: any = a[sortBy as keyof InventoryItem]\n      let bValue: any = b[sortBy as keyof InventoryItem]\n\n      // Handle different data types\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase()\n        bValue = bValue.toLowerCase()\n      }\n\n      if (sortDirection === 'asc') {\n        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0\n      } else {\n        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0\n      }\n    })\n\n  // Calculate inventory statistics\n  const inventoryStats = {\n    totalItems: items.length,\n    totalValue: items.reduce((sum, item) => sum + (item.current_stock * item.unit_cost), 0),\n    lowStockItems: items.filter(item => item.status === 'low_stock').length,\n    outOfStockItems: items.filter(item => item.status === 'out_of_stock').length,\n    inStockItems: items.filter(item => item.status === 'in_stock').length\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'in_stock':\n        return 'bg-green-100 text-green-800'\n      case 'low_stock':\n        return 'bg-yellow-100 text-yellow-800'\n      case 'out_of_stock':\n        return 'bg-red-100 text-red-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'in_stock':\n        return 'In Stock'\n      case 'low_stock':\n        return 'Low Stock'\n      case 'out_of_stock':\n        return 'Out of Stock'\n      default:\n        return 'Unknown'\n    }\n  }\n\n  // Professional validation function\n  const validateItem = () => {\n    const errors: string[] = []\n\n    // Required field validation\n    if (!newItem.name.trim()) errors.push('Item name is required')\n    if (!newItem.sku.trim()) errors.push('SKU is required')\n    if (!newItem.category_id) errors.push('Category selection is required')\n    if (!newItem.unit_of_measure) errors.push('Unit of measure is required')\n\n    // Business logic validation\n    if (newItem.name.length < 2) errors.push('Item name must be at least 2 characters')\n    if (newItem.sku.length < 3) errors.push('SKU must be at least 3 characters')\n    if (newItem.current_stock < 0) errors.push('Current stock cannot be negative')\n    if (newItem.minimum_stock < 0) errors.push('Minimum stock cannot be negative')\n    if (newItem.maximum_stock > 0 && newItem.maximum_stock < newItem.minimum_stock) {\n      errors.push('Maximum stock must be greater than minimum stock')\n    }\n    if (newItem.reorder_point < 0) errors.push('Reorder point cannot be negative')\n    if (newItem.unit_cost < 0) errors.push('Unit cost cannot be negative')\n\n    // SKU uniqueness check\n    if (items.some(item => item.sku.toLowerCase() === newItem.sku.toLowerCase())) {\n      errors.push('SKU already exists')\n    }\n\n    // Date validation\n    if (newItem.expiry_date && new Date(newItem.expiry_date) <= new Date()) {\n      errors.push('Expiry date must be in the future')\n    }\n\n    // Supplier validation\n    if (newItem.supplier_name && newItem.supplier_name.length < 2) {\n      errors.push('Supplier name must be at least 2 characters')\n    }\n\n    setValidationErrors(errors)\n    return errors.length === 0\n  }\n\n  // Generate SKU automatically\n  const generateSKU = () => {\n    const category = categories.find(cat => cat.id === newItem.category_id)\n    const categoryCode = category ? category.name.substring(0, 3).toUpperCase() : 'GEN'\n    const timestamp = Date.now().toString().slice(-6)\n    const randomNum = Math.floor(Math.random() * 100).toString().padStart(2, '0')\n    return `${categoryCode}-${timestamp}-${randomNum}`\n  }\n\n  // Auto-generate SKU when category changes\n  useEffect(() => {\n    if (newItem.category_id && !newItem.sku) {\n      setNewItem(prev => ({ ...prev, sku: generateSKU() }))\n    }\n  }, [newItem.category_id])\n\n  // Professional item creation with comprehensive data\n  const handleAddItem = async () => {\n    if (!validateItem()) return\n\n    try {\n      setIsCreating(true)\n\n      const newInventoryItem = {\n        name: newItem.name.trim(),\n        sku: newItem.sku.toUpperCase().trim(),\n        category_id: newItem.category_id,\n        current_stock: newItem.current_stock,\n        minimum_stock: newItem.minimum_stock,\n        maximum_stock: newItem.maximum_stock || null,\n        reorder_point: newItem.reorder_point || newItem.minimum_stock,\n        unit_of_measure: newItem.unit_of_measure,\n        unit_cost: newItem.unit_cost,\n        supplier_name: newItem.supplier_name || null,\n        supplier_code: newItem.supplier_code || null,\n        batch_number: newItem.batch_number || null,\n        expiry_date: newItem.expiry_date || null,\n        location: newItem.location || null,\n        description: newItem.description || null,\n        notes: newItem.notes || null,\n        is_hazardous: newItem.is_hazardous,\n        requires_refrigeration: newItem.requires_refrigeration,\n        is_active: true\n      }\n\n      const createdItem = await DatabaseService.createInventoryItem(newInventoryItem)\n\n      if (createdItem) {\n        // Add the new item to the local state with processed data\n        const processedItem = {\n          ...createdItem,\n          current_stock: parseFloat(createdItem.current_stock.toString()),\n          minimum_stock: parseFloat(createdItem.minimum_stock.toString()),\n          unit_cost: parseFloat(createdItem.unit_cost.toString()),\n          status: parseFloat(createdItem.current_stock.toString()) <= parseFloat(createdItem.minimum_stock.toString())\n            ? 'low_stock' as const\n            : parseFloat(createdItem.current_stock.toString()) === 0\n            ? 'out_of_stock' as const\n            : 'in_stock' as const,\n          category_name: categories.find(cat => cat.id === createdItem.category_id)?.name || 'Unknown'\n        }\n\n        setItems([...items, processedItem])\n        resetForm()\n        setShowAddModal(false)\n\n        alert(`✅ Inventory item \"${newItem.name}\" created successfully!\\n\\n📋 Item Details:\\n• SKU: ${newItem.sku}\\n• Category: ${categories.find(cat => cat.id === newItem.category_id)?.name}\\n• Current Stock: ${newItem.current_stock} ${newItem.unit_of_measure}\\n• Unit Cost: $${newItem.unit_cost.toFixed(2)}\\n• Total Value: $${(newItem.current_stock * newItem.unit_cost).toFixed(2)}`)\n      } else {\n        alert('❌ Failed to create inventory item. Please try again.')\n      }\n    } catch (error) {\n      alert('❌ Error creating inventory item. Please check the form and try again.')\n    } finally {\n      setIsCreating(false)\n    }\n  }\n\n  // Reset form function\n  const resetForm = () => {\n    setCurrentStep(1)\n    setNewItem({\n      name: '',\n      sku: '',\n      category_id: categories.length > 0 ? categories[0].id : '',\n      current_stock: 0,\n      minimum_stock: 0,\n      maximum_stock: 0,\n      reorder_point: 0,\n      unit_of_measure: 'liters',\n      unit_cost: 0,\n      supplier_name: '',\n      supplier_code: '',\n      batch_number: '',\n      expiry_date: '',\n      location: '',\n      description: '',\n      notes: '',\n      is_hazardous: false,\n      requires_refrigeration: false\n    })\n    setValidationErrors([])\n  }\n\n  // Navigation functions\n  const nextStep = () => {\n    if (currentStep < 3) setCurrentStep(currentStep + 1)\n  }\n\n  const prevStep = () => {\n    if (currentStep > 1) setCurrentStep(currentStep - 1)\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return (\n    <MainLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"sm:flex sm:items-center sm:justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Professional Inventory Management</h1>\n            <p className=\"mt-2 text-sm text-gray-700\">\n              Comprehensive inventory tracking with real-time database integration and advanced analytics\n            </p>\n          </div>\n          <div className=\"mt-4 sm:mt-0\">\n            <button\n              type=\"button\"\n              onClick={() => { resetForm(); setShowAddModal(true); }}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n            >\n              <PlusIcon className=\"-ml-1 mr-2 h-5 w-5\" aria-hidden=\"true\" />\n              Add Item\n            </button>\n          </div>\n        </div>\n\n        {/* Enhanced Statistics Cards */}\n        <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-5\">\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <ArchiveBoxIcon className=\"h-6 w-6 text-blue-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Items</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">{inventoryStats.totalItems}</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <CurrencyDollarIcon className=\"h-6 w-6 text-green-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Value</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">${inventoryStats.totalValue.toFixed(0)}</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <CheckCircleIcon className=\"h-6 w-6 text-green-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">In Stock</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">{inventoryStats.inStockItems}</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <ExclamationTriangleIcon className=\"h-6 w-6 text-yellow-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Low Stock</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">{inventoryStats.lowStockItems}</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <XMarkIcon className=\"h-6 w-6 text-red-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Out of Stock</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">{inventoryStats.outOfStockItems}</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Enhanced Search and Filters */}\n        <div className=\"bg-white shadow rounded-lg p-6\">\n          <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4\">\n            {/* Search */}\n            <div className=\"lg:col-span-2\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Search Items</label>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\n                </div>\n                <input\n                  type=\"text\"\n                  className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-green-500 focus:border-green-500\"\n                  placeholder=\"Search by name, SKU, category, or description...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                />\n              </div>\n            </div>\n\n            {/* Category Filter */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Category</label>\n              <select\n                className=\"block w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-green-500\"\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n              >\n                <option value=\"all\">All Categories</option>\n                {categories.map(category => (\n                  <option key={category.id} value={category.id}>{category.name}</option>\n                ))}\n              </select>\n            </div>\n\n            {/* Status Filter */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Status</label>\n              <select\n                className=\"block w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-green-500\"\n                value={selectedStatus}\n                onChange={(e) => setSelectedStatus(e.target.value)}\n              >\n                <option value=\"all\">All Status</option>\n                <option value=\"in_stock\">In Stock</option>\n                <option value=\"low_stock\">Low Stock</option>\n                <option value=\"out_of_stock\">Out of Stock</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Sort Options */}\n          <div className=\"mt-4 flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-2\">\n              <label className=\"text-sm font-medium text-gray-700\">Sort by:</label>\n              <select\n                className=\"border border-gray-300 rounded-md py-1 px-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-green-500\"\n                value={`${sortBy}-${sortDirection}`}\n                onChange={(e) => {\n                  const [field, direction] = e.target.value.split('-')\n                  setSortBy(field)\n                  setSortDirection(direction as 'asc' | 'desc')\n                }}\n              >\n                <option value=\"name-asc\">Name (A-Z)</option>\n                <option value=\"name-desc\">Name (Z-A)</option>\n                <option value=\"sku-asc\">SKU (A-Z)</option>\n                <option value=\"sku-desc\">SKU (Z-A)</option>\n                <option value=\"current_stock-desc\">Stock (High-Low)</option>\n                <option value=\"current_stock-asc\">Stock (Low-High)</option>\n                <option value=\"unit_cost-desc\">Cost (High-Low)</option>\n                <option value=\"unit_cost-asc\">Cost (Low-High)</option>\n              </select>\n            </div>\n            <div className=\"text-sm text-gray-500\">\n              Showing {filteredItems.length} of {items.length} items\n            </div>\n          </div>\n        </div>\n\n        {/* Inventory Table */}\n        <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n              Inventory Items ({filteredItems.length})\n            </h3>\n            \n            {loading ? (\n              <div className=\"animate-pulse\">\n                {[...Array(5)].map((_, i) => (\n                  <div key={i} className=\"flex items-center space-x-4 py-4\">\n                    <div className=\"h-4 bg-gray-200 rounded w-1/4\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full divide-y divide-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Item\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Category\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Stock Level\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Unit Cost\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"relative px-6 py-3\">\n                        <span className=\"sr-only\">Actions</span>\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {filteredItems.map((item) => (\n                      <tr key={item.id} className=\"hover:bg-gray-50\">\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div>\n                            <div className=\"text-sm font-medium text-gray-900\">{item.name}</div>\n                            <div className=\"text-sm text-gray-500\">{item.sku}</div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {item.category_name}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"text-sm text-gray-900\">\n                            {item.current_stock} {item.unit_of_measure}\n                          </div>\n                          <div className=\"text-sm text-gray-500\">\n                            Min: {item.minimum_stock} {item.unit_of_measure}\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          ${item.unit_cost.toFixed(2)}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(item.status || 'in_stock')}`}>\n                            {item.status === 'low_stock' && (\n                              <ExclamationTriangleIcon className=\"w-3 h-3 mr-1\" />\n                            )}\n                            {getStatusText(item.status || 'in_stock')}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                          <button className=\"text-green-600 hover:text-green-900\">\n                            Edit\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Professional Multi-Step Add Item Modal */}\n        <Modal\n          isOpen={showAddModal}\n          onClose={() => { resetForm(); setShowAddModal(false); }}\n          title={`Add New Inventory Item - Step ${currentStep} of 3`}\n          maxWidth=\"3xl\"\n        >\n          <div className=\"space-y-6\">\n            {/* Progress Steps */}\n            <div className=\"flex items-center justify-between\">\n              {[1, 2, 3].map((step) => (\n                <div key={step} className=\"flex items-center\">\n                  <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${\n                    step <= currentStep\n                      ? 'bg-green-600 border-green-600 text-white'\n                      : 'border-gray-300 text-gray-500'\n                  }`}>\n                    {step < currentStep ? (\n                      <CheckCircleIcon className=\"w-5 h-5\" />\n                    ) : (\n                      <span className=\"text-sm font-medium\">{step}</span>\n                    )}\n                  </div>\n                  <div className=\"ml-2 text-sm font-medium text-gray-900\">\n                    {step === 1 && 'Basic Information'}\n                    {step === 2 && 'Stock & Pricing'}\n                    {step === 3 && 'Additional Details'}\n                  </div>\n                  {step < 3 && (\n                    <div className={`ml-4 w-16 h-0.5 ${\n                      step < currentStep ? 'bg-green-600' : 'bg-gray-300'\n                    }`} />\n                  )}\n                </div>\n              ))}\n            </div>\n\n            {/* Validation Errors */}\n            {validationErrors.length > 0 && (\n              <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n                <div className=\"flex\">\n                  <ExclamationTriangleIcon className=\"h-5 w-5 text-red-400\" />\n                  <div className=\"ml-3\">\n                    <h3 className=\"text-sm font-medium text-red-800\">Please fix the following errors:</h3>\n                    <ul className=\"mt-2 text-sm text-red-700 list-disc list-inside\">\n                      {validationErrors.map((error, index) => (\n                        <li key={index}>{error}</li>\n                      ))}\n                    </ul>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Step Content */}\n            <div className=\"min-h-96\">\n              {currentStep === 1 && (\n                <div className=\"space-y-4\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">Basic Item Information</h3>\n                  <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Item Name *</label>\n                      <input\n                        type=\"text\"\n                        required\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newItem.name}\n                        onChange={(e) => setNewItem({ ...newItem, name: e.target.value })}\n                        placeholder=\"e.g., Vanilla Extract Premium\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">SKU *</label>\n                      <div className=\"mt-1 flex rounded-md shadow-sm\">\n                        <input\n                          type=\"text\"\n                          required\n                          className=\"flex-1 block w-full rounded-l-md border-gray-300 focus:border-green-500 focus:ring-green-500\"\n                          value={newItem.sku}\n                          onChange={(e) => setNewItem({ ...newItem, sku: e.target.value })}\n                          placeholder=\"e.g., VAN-001\"\n                        />\n                        <button\n                          type=\"button\"\n                          onClick={() => setNewItem({ ...newItem, sku: generateSKU() })}\n                          className=\"inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 text-sm hover:bg-gray-100\"\n                        >\n                          <DocumentDuplicateIcon className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n                      <p className=\"mt-1 text-xs text-gray-500\">Click the icon to auto-generate</p>\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Category *</label>\n                      <select\n                        required\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newItem.category_id}\n                        onChange={(e) => setNewItem({ ...newItem, category_id: e.target.value })}\n                      >\n                        <option value=\"\">Select a category...</option>\n                        {categories.map(category => (\n                          <option key={category.id} value={category.id}>{category.name}</option>\n                        ))}\n                      </select>\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Unit of Measure *</label>\n                      <select\n                        required\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newItem.unit_of_measure}\n                        onChange={(e) => setNewItem({ ...newItem, unit_of_measure: e.target.value })}\n                      >\n                        <option value=\"liters\">Liters</option>\n                        <option value=\"kg\">Kilograms</option>\n                        <option value=\"grams\">Grams</option>\n                        <option value=\"pieces\">Pieces</option>\n                        <option value=\"bottles\">Bottles</option>\n                        <option value=\"boxes\">Boxes</option>\n                        <option value=\"ml\">Milliliters</option>\n                        <option value=\"gallons\">Gallons</option>\n                      </select>\n                    </div>\n                    <div className=\"sm:col-span-2\">\n                      <label className=\"block text-sm font-medium text-gray-700\">Description</label>\n                      <textarea\n                        rows={3}\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newItem.description}\n                        onChange={(e) => setNewItem({ ...newItem, description: e.target.value })}\n                        placeholder=\"Detailed description of the item, its uses, and specifications...\"\n                      />\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {currentStep === 2 && (\n                <div className=\"space-y-4\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">Stock Levels & Pricing</h3>\n                  <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Current Stock *</label>\n                      <input\n                        type=\"number\"\n                        min=\"0\"\n                        step=\"0.001\"\n                        required\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newItem.current_stock}\n                        onChange={(e) => setNewItem({ ...newItem, current_stock: parseFloat(e.target.value) || 0 })}\n                        placeholder=\"0.000\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Minimum Stock Level *</label>\n                      <input\n                        type=\"number\"\n                        min=\"0\"\n                        step=\"0.001\"\n                        required\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newItem.minimum_stock}\n                        onChange={(e) => setNewItem({ ...newItem, minimum_stock: parseFloat(e.target.value) || 0 })}\n                        placeholder=\"0.000\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Maximum Stock Level</label>\n                      <input\n                        type=\"number\"\n                        min=\"0\"\n                        step=\"0.001\"\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newItem.maximum_stock}\n                        onChange={(e) => setNewItem({ ...newItem, maximum_stock: parseFloat(e.target.value) || 0 })}\n                        placeholder=\"0.000\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Reorder Point</label>\n                      <input\n                        type=\"number\"\n                        min=\"0\"\n                        step=\"0.001\"\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newItem.reorder_point}\n                        onChange={(e) => setNewItem({ ...newItem, reorder_point: parseFloat(e.target.value) || 0 })}\n                        placeholder=\"0.000\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Unit Cost ($) *</label>\n                      <input\n                        type=\"number\"\n                        min=\"0\"\n                        step=\"0.01\"\n                        required\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newItem.unit_cost}\n                        onChange={(e) => setNewItem({ ...newItem, unit_cost: parseFloat(e.target.value) || 0 })}\n                        placeholder=\"0.00\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Total Value</label>\n                      <div className=\"mt-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm text-gray-900\">\n                        ${(newItem.current_stock * newItem.unit_cost).toFixed(2)}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Stock Level Indicators */}\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\n                    <h4 className=\"text-sm font-medium text-gray-900 mb-2\">Stock Level Analysis</h4>\n                    <div className=\"grid grid-cols-1 gap-2 sm:grid-cols-3 text-xs\">\n                      <div className={`p-2 rounded ${newItem.current_stock > newItem.minimum_stock ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>\n                        <span className=\"font-medium\">Status:</span> {newItem.current_stock > newItem.minimum_stock ? 'Adequate Stock' : 'Below Minimum'}\n                      </div>\n                      <div className=\"p-2 bg-blue-100 text-blue-800 rounded\">\n                        <span className=\"font-medium\">Days Supply:</span> {newItem.minimum_stock > 0 ? Math.floor(newItem.current_stock / newItem.minimum_stock * 30) : 'N/A'} days\n                      </div>\n                      <div className=\"p-2 bg-purple-100 text-purple-800 rounded\">\n                        <span className=\"font-medium\">Turnover:</span> {newItem.current_stock > 0 ? 'Active' : 'No Stock'}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {currentStep === 3 && (\n                <div className=\"space-y-4\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">Additional Details & Supplier Information</h3>\n                  <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Supplier Name</label>\n                      <input\n                        type=\"text\"\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newItem.supplier_name}\n                        onChange={(e) => setNewItem({ ...newItem, supplier_name: e.target.value })}\n                        placeholder=\"e.g., Premium Ingredients Co.\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Supplier Code</label>\n                      <input\n                        type=\"text\"\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newItem.supplier_code}\n                        onChange={(e) => setNewItem({ ...newItem, supplier_code: e.target.value })}\n                        placeholder=\"e.g., SUP-001\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Batch Number</label>\n                      <input\n                        type=\"text\"\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newItem.batch_number}\n                        onChange={(e) => setNewItem({ ...newItem, batch_number: e.target.value })}\n                        placeholder=\"e.g., BATCH-2024-001\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Expiry Date</label>\n                      <input\n                        type=\"date\"\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newItem.expiry_date}\n                        onChange={(e) => setNewItem({ ...newItem, expiry_date: e.target.value })}\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Storage Location</label>\n                      <input\n                        type=\"text\"\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newItem.location}\n                        onChange={(e) => setNewItem({ ...newItem, location: e.target.value })}\n                        placeholder=\"e.g., Warehouse A, Shelf 3\"\n                      />\n                    </div>\n                    <div className=\"sm:col-span-2\">\n                      <label className=\"block text-sm font-medium text-gray-700\">Additional Notes</label>\n                      <textarea\n                        rows={3}\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newItem.notes}\n                        onChange={(e) => setNewItem({ ...newItem, notes: e.target.value })}\n                        placeholder=\"Special handling instructions, quality notes, etc...\"\n                      />\n                    </div>\n                  </div>\n\n                  {/* Special Properties */}\n                  <div className=\"space-y-3\">\n                    <h4 className=\"text-sm font-medium text-gray-900\">Special Properties</h4>\n                    <div className=\"space-y-2\">\n                      <label className=\"flex items-center\">\n                        <input\n                          type=\"checkbox\"\n                          className=\"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                          checked={newItem.is_hazardous}\n                          onChange={(e) => setNewItem({ ...newItem, is_hazardous: e.target.checked })}\n                        />\n                        <span className=\"ml-2 text-sm text-gray-700\">Hazardous Material</span>\n                      </label>\n                      <label className=\"flex items-center\">\n                        <input\n                          type=\"checkbox\"\n                          className=\"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                          checked={newItem.requires_refrigeration}\n                          onChange={(e) => setNewItem({ ...newItem, requires_refrigeration: e.target.checked })}\n                        />\n                        <span className=\"ml-2 text-sm text-gray-700\">Requires Refrigeration</span>\n                      </label>\n                    </div>\n                  </div>\n\n                  {/* Summary */}\n                  <div className=\"bg-green-50 p-4 rounded-lg\">\n                    <h4 className=\"text-sm font-medium text-green-800 mb-2\">Item Summary</h4>\n                    <div className=\"grid grid-cols-2 gap-4 text-xs text-green-700\">\n                      <div><span className=\"font-medium\">Name:</span> {newItem.name || 'Not specified'}</div>\n                      <div><span className=\"font-medium\">SKU:</span> {newItem.sku || 'Not specified'}</div>\n                      <div><span className=\"font-medium\">Category:</span> {categories.find(cat => cat.id === newItem.category_id)?.name || 'Not selected'}</div>\n                      <div><span className=\"font-medium\">Stock:</span> {newItem.current_stock} {newItem.unit_of_measure}</div>\n                      <div><span className=\"font-medium\">Unit Cost:</span> ${newItem.unit_cost.toFixed(2)}</div>\n                      <div><span className=\"font-medium\">Total Value:</span> ${(newItem.current_stock * newItem.unit_cost).toFixed(2)}</div>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Navigation Buttons */}\n            <div className=\"flex justify-between pt-6 border-t border-gray-200\">\n              <button\n                type=\"button\"\n                onClick={currentStep === 1 ? () => { resetForm(); setShowAddModal(false); } : prevStep}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n                disabled={isCreating}\n              >\n                {currentStep === 1 ? 'Cancel' : 'Previous'}\n              </button>\n\n              <div className=\"flex space-x-3\">\n                {currentStep < 3 ? (\n                  <button\n                    type=\"button\"\n                    onClick={nextStep}\n                    disabled={currentStep === 1 && (!newItem.name || !newItem.sku || !newItem.category_id)}\n                    className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400\"\n                  >\n                    Next Step\n                  </button>\n                ) : (\n                  <button\n                    type=\"button\"\n                    onClick={handleAddItem}\n                    disabled={isCreating}\n                    className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400\"\n                  >\n                    {isCreating ? (\n                      <>\n                        <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\n                          <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                          <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                        </svg>\n                        Creating...\n                      </>\n                    ) : (\n                      <>\n                        <CheckCircleIcon className=\"-ml-1 mr-2 h-4 w-4\" />\n                        Create Item\n                      </>\n                    )}\n                  </button>\n                )}\n              </div>\n            </div>\n          </div>\n        </Modal>\n      </div>\n    </MainLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAmCe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IACpE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAErE,wDAAwD;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,MAAM;QACN,KAAK;QACL,aAAa;QACb,eAAe;QACf,eAAe;QACf,eAAe;QACf,eAAe;QACf,iBAAiB;QACjB,WAAW;QACX,eAAe;QACf,eAAe;QACf,cAAc;QACd,aAAa;QACb,UAAU;QACV,aAAa;QACb,OAAO;QACP,cAAc;QACd,wBAAwB;IAC1B;IAEA,yBAAyB;IACzB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAEnE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;oDAAW;oBACf,IAAI;wBACF,WAAW;wBAEX,oDAAoD;wBACpD,MAAM,CAAC,gBAAgB,oBAAoB,GAAG,MAAM,QAAQ,GAAG,CAAC;4BAC9D,yHAAA,CAAA,kBAAe,CAAC,iBAAiB;4BACjC,yHAAA,CAAA,kBAAe,CAAC,sBAAsB;yBACvC;wBAED,0DAA0D;wBAC1D,MAAM,iBAAiB,eAAe,GAAG;+EAAC,CAAA,OAAQ,CAAC;oCACjD,GAAG,IAAI;oCACP,eAAe,WAAW,KAAK,aAAa,CAAC,QAAQ;oCACrD,eAAe,WAAW,KAAK,aAAa,CAAC,QAAQ;oCACrD,WAAW,WAAW,KAAK,SAAS,CAAC,QAAQ;oCAC7C,QAAQ,WAAW,KAAK,aAAa,CAAC,QAAQ,OAAO,WAAW,KAAK,aAAa,CAAC,QAAQ,MACvF,cACA,WAAW,KAAK,aAAa,CAAC,QAAQ,QAAQ,IAC9C,iBACA;oCACJ,eAAe,oBAAoB,IAAI;2FAAC,CAAA,MAAO,IAAI,EAAE,KAAK,KAAK,WAAW;2FAAG,QAAQ;gCACvF,CAAC;;wBAED,SAAS;wBACT,cAAc;wBAEd,qCAAqC;wBACrC,IAAI,oBAAoB,MAAM,GAAG,KAAK,CAAC,QAAQ,WAAW,EAAE;4BAC1D;oEAAW,CAAA,OAAQ,CAAC;wCAAE,GAAG,IAAI;wCAAE,aAAa,mBAAmB,CAAC,EAAE,CAAC,EAAE;oCAAC,CAAC;;wBACzE;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,iCAAiC;oBACjD,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;kCAAG,EAAE;IAEL,iCAAiC;IACjC,MAAM,gBAAgB,MACnB,MAAM,CAAC,CAAA;QACN,gBAAgB;QAChB,MAAM,gBAAgB,eAAe,MACnC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,KAAK,GAAG,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACrD,KAAK,aAAa,IAAI,KAAK,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACtF,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAErF,kBAAkB;QAClB,MAAM,kBAAkB,qBAAqB,SAAS,KAAK,WAAW,KAAK;QAE3E,gBAAgB;QAChB,MAAM,gBAAgB,mBAAmB,SAAS,KAAK,MAAM,KAAK;QAElE,OAAO,iBAAiB,mBAAmB;IAC7C,GACC,IAAI,CAAC,CAAC,GAAG;QACR,IAAI,SAAc,CAAC,CAAC,OAA8B;QAClD,IAAI,SAAc,CAAC,CAAC,OAA8B;QAElD,8BAA8B;QAC9B,IAAI,OAAO,WAAW,UAAU;YAC9B,SAAS,OAAO,WAAW;YAC3B,SAAS,OAAO,WAAW;QAC7B;QAEA,IAAI,kBAAkB,OAAO;YAC3B,OAAO,SAAS,SAAS,CAAC,IAAI,SAAS,SAAS,IAAI;QACtD,OAAO;YACL,OAAO,SAAS,SAAS,CAAC,IAAI,SAAS,SAAS,IAAI;QACtD;IACF;IAEF,iCAAiC;IACjC,MAAM,iBAAiB;QACrB,YAAY,MAAM,MAAM;QACxB,YAAY,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,aAAa,GAAG,KAAK,SAAS,EAAG;QACrF,eAAe,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,aAAa,MAAM;QACvE,iBAAiB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,gBAAgB,MAAM;QAC5E,cAAc,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,YAAY,MAAM;IACvE;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,mCAAmC;IACnC,MAAM,eAAe;QACnB,MAAM,SAAmB,EAAE;QAE3B,4BAA4B;QAC5B,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC;QACtC,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC;QACrC,IAAI,CAAC,QAAQ,WAAW,EAAE,OAAO,IAAI,CAAC;QACtC,IAAI,CAAC,QAAQ,eAAe,EAAE,OAAO,IAAI,CAAC;QAE1C,4BAA4B;QAC5B,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,GAAG,OAAO,IAAI,CAAC;QACzC,IAAI,QAAQ,GAAG,CAAC,MAAM,GAAG,GAAG,OAAO,IAAI,CAAC;QACxC,IAAI,QAAQ,aAAa,GAAG,GAAG,OAAO,IAAI,CAAC;QAC3C,IAAI,QAAQ,aAAa,GAAG,GAAG,OAAO,IAAI,CAAC;QAC3C,IAAI,QAAQ,aAAa,GAAG,KAAK,QAAQ,aAAa,GAAG,QAAQ,aAAa,EAAE;YAC9E,OAAO,IAAI,CAAC;QACd;QACA,IAAI,QAAQ,aAAa,GAAG,GAAG,OAAO,IAAI,CAAC;QAC3C,IAAI,QAAQ,SAAS,GAAG,GAAG,OAAO,IAAI,CAAC;QAEvC,uBAAuB;QACvB,IAAI,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,GAAG,CAAC,WAAW,OAAO,QAAQ,GAAG,CAAC,WAAW,KAAK;YAC5E,OAAO,IAAI,CAAC;QACd;QAEA,kBAAkB;QAClB,IAAI,QAAQ,WAAW,IAAI,IAAI,KAAK,QAAQ,WAAW,KAAK,IAAI,QAAQ;YACtE,OAAO,IAAI,CAAC;QACd;QAEA,sBAAsB;QACtB,IAAI,QAAQ,aAAa,IAAI,QAAQ,aAAa,CAAC,MAAM,GAAG,GAAG;YAC7D,OAAO,IAAI,CAAC;QACd;QAEA,oBAAoB;QACpB,OAAO,OAAO,MAAM,KAAK;IAC3B;IAEA,6BAA6B;IAC7B,MAAM,cAAc;QAClB,MAAM,WAAW,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,QAAQ,WAAW;QACtE,MAAM,eAAe,WAAW,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,WAAW,KAAK;QAC9E,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;QAC/C,MAAM,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG;QACzE,OAAO,GAAG,aAAa,CAAC,EAAE,UAAU,CAAC,EAAE,WAAW;IACpD;IAEA,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,QAAQ,WAAW,IAAI,CAAC,QAAQ,GAAG,EAAE;gBACvC;+CAAW,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,KAAK;wBAAc,CAAC;;YACrD;QACF;kCAAG;QAAC,QAAQ,WAAW;KAAC;IAExB,qDAAqD;IACrD,MAAM,gBAAgB;QACpB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,cAAc;YAEd,MAAM,mBAAmB;gBACvB,MAAM,QAAQ,IAAI,CAAC,IAAI;gBACvB,KAAK,QAAQ,GAAG,CAAC,WAAW,GAAG,IAAI;gBACnC,aAAa,QAAQ,WAAW;gBAChC,eAAe,QAAQ,aAAa;gBACpC,eAAe,QAAQ,aAAa;gBACpC,eAAe,QAAQ,aAAa,IAAI;gBACxC,eAAe,QAAQ,aAAa,IAAI,QAAQ,aAAa;gBAC7D,iBAAiB,QAAQ,eAAe;gBACxC,WAAW,QAAQ,SAAS;gBAC5B,eAAe,QAAQ,aAAa,IAAI;gBACxC,eAAe,QAAQ,aAAa,IAAI;gBACxC,cAAc,QAAQ,YAAY,IAAI;gBACtC,aAAa,QAAQ,WAAW,IAAI;gBACpC,UAAU,QAAQ,QAAQ,IAAI;gBAC9B,aAAa,QAAQ,WAAW,IAAI;gBACpC,OAAO,QAAQ,KAAK,IAAI;gBACxB,cAAc,QAAQ,YAAY;gBAClC,wBAAwB,QAAQ,sBAAsB;gBACtD,WAAW;YACb;YAEA,MAAM,cAAc,MAAM,yHAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC;YAE9D,IAAI,aAAa;gBACf,0DAA0D;gBAC1D,MAAM,gBAAgB;oBACpB,GAAG,WAAW;oBACd,eAAe,WAAW,YAAY,aAAa,CAAC,QAAQ;oBAC5D,eAAe,WAAW,YAAY,aAAa,CAAC,QAAQ;oBAC5D,WAAW,WAAW,YAAY,SAAS,CAAC,QAAQ;oBACpD,QAAQ,WAAW,YAAY,aAAa,CAAC,QAAQ,OAAO,WAAW,YAAY,aAAa,CAAC,QAAQ,MACrG,cACA,WAAW,YAAY,aAAa,CAAC,QAAQ,QAAQ,IACrD,iBACA;oBACJ,eAAe,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,YAAY,WAAW,GAAG,QAAQ;gBACrF;gBAEA,SAAS;uBAAI;oBAAO;iBAAc;gBAClC;gBACA,gBAAgB;gBAEhB,MAAM,CAAC,kBAAkB,EAAE,QAAQ,IAAI,CAAC,oDAAoD,EAAE,QAAQ,GAAG,CAAC,cAAc,EAAE,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,QAAQ,WAAW,GAAG,KAAK,mBAAmB,EAAE,QAAQ,aAAa,CAAC,CAAC,EAAE,QAAQ,eAAe,CAAC,gBAAgB,EAAE,QAAQ,SAAS,CAAC,OAAO,CAAC,GAAG,kBAAkB,EAAE,CAAC,QAAQ,aAAa,GAAG,QAAQ,SAAS,EAAE,OAAO,CAAC,IAAI;YAC1X,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR,SAAU;YACR,cAAc;QAChB;IACF;IAEA,sBAAsB;IACtB,MAAM,YAAY;QAChB,eAAe;QACf,WAAW;YACT,MAAM;YACN,KAAK;YACL,aAAa,WAAW,MAAM,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG;YACxD,eAAe;YACf,eAAe;YACf,eAAe;YACf,eAAe;YACf,iBAAiB;YACjB,WAAW;YACX,eAAe;YACf,eAAe;YACf,cAAc;YACd,aAAa;YACb,UAAU;YACV,aAAa;YACb,OAAO;YACP,cAAc;YACd,wBAAwB;QAC1B;QACA,oBAAoB,EAAE;IACxB;IAEA,uBAAuB;IACvB,MAAM,WAAW;QACf,IAAI,cAAc,GAAG,eAAe,cAAc;IACpD;IAEA,MAAM,WAAW;QACf,IAAI,cAAc,GAAG,eAAe,cAAc;IACpD;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC,iJAAA,CAAA,aAAU;kBACT,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,SAAS;oCAAQ;oCAAa,gBAAgB;gCAAO;gCACrD,WAAU;;kDAEV,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAqB,eAAY;;;;;;oCAAS;;;;;;;;;;;;;;;;;;8BAOpE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,8NAAA,CAAA,iBAAc;gDAAC,WAAU;gDAAwB,eAAY;;;;;;;;;;;sDAEhE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAAqC,eAAe,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOtF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,sOAAA,CAAA,qBAAkB;gDAAC,WAAU;gDAAyB,eAAY;;;;;;;;;;;sDAErE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;;4DAAoC;4DAAE,eAAe,UAAU,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhG,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gOAAA,CAAA,kBAAe;gDAAC,WAAU;gDAAyB,eAAY;;;;;;;;;;;sDAElE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAAqC,eAAe,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOxF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gPAAA,CAAA,0BAAuB;gDAAC,WAAU;gDAA0B,eAAY;;;;;;;;;;;sDAE3E,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAAqC,eAAe,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOzF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAuB,eAAY;;;;;;;;;;;sDAE1D,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAAqC,eAAe,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS7F,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,wOAAA,CAAA,sBAAmB;wDAAC,WAAU;wDAAwB,eAAY;;;;;;;;;;;8DAErE,6LAAC;oDACC,MAAK;oDACL,WAAU;oDACV,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;8CAMnD,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,6LAAC;4CACC,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;;8DAEnD,6LAAC;oDAAO,OAAM;8DAAM;;;;;;gDACnB,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;wDAAyB,OAAO,SAAS,EAAE;kEAAG,SAAS,IAAI;uDAA/C,SAAS,EAAE;;;;;;;;;;;;;;;;;8CAM9B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,6LAAC;4CACC,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;;8DAEjD,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,6LAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,6LAAC;oDAAO,OAAM;8DAAe;;;;;;;;;;;;;;;;;;;;;;;;sCAMnC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAAoC;;;;;;sDACrD,6LAAC;4CACC,WAAU;4CACV,OAAO,GAAG,OAAO,CAAC,EAAE,eAAe;4CACnC,UAAU,CAAC;gDACT,MAAM,CAAC,OAAO,UAAU,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;gDAChD,UAAU;gDACV,iBAAiB;4CACnB;;8DAEA,6LAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,6LAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,6LAAC;oDAAO,OAAM;8DAAqB;;;;;;8DACnC,6LAAC;oDAAO,OAAM;8DAAoB;;;;;;8DAClC,6LAAC;oDAAO,OAAM;8DAAiB;;;;;;8DAC/B,6LAAC;oDAAO,OAAM;8DAAgB;;;;;;;;;;;;;;;;;;8CAGlC,6LAAC;oCAAI,WAAU;;wCAAwB;wCAC5B,cAAc,MAAM;wCAAC;wCAAK,MAAM,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;8BAMtD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAmD;oCAC7C,cAAc,MAAM;oCAAC;;;;;;;4BAGxC,wBACC,6LAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wCAAY,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;uCALP;;;;;;;;;qDAUd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;sDAIhC,6LAAC;4CAAM,WAAU;sDACd,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;oDAAiB,WAAU;;sEAC1B,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;kFAAqC,KAAK,IAAI;;;;;;kFAC7D,6LAAC;wEAAI,WAAU;kFAAyB,KAAK,GAAG;;;;;;;;;;;;;;;;;sEAGpD,6LAAC;4DAAG,WAAU;sEACX,KAAK,aAAa;;;;;;sEAErB,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAI,WAAU;;wEACZ,KAAK,aAAa;wEAAC;wEAAE,KAAK,eAAe;;;;;;;8EAE5C,6LAAC;oEAAI,WAAU;;wEAAwB;wEAC/B,KAAK,aAAa;wEAAC;wEAAE,KAAK,eAAe;;;;;;;;;;;;;sEAGnD,6LAAC;4DAAG,WAAU;;gEAAoD;gEAC9D,KAAK,SAAS,CAAC,OAAO,CAAC;;;;;;;sEAE3B,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,CAAC,wEAAwE,EAAE,eAAe,KAAK,MAAM,IAAI,aAAa;;oEACpI,KAAK,MAAM,KAAK,6BACf,6LAAC,gPAAA,CAAA,0BAAuB;wEAAC,WAAU;;;;;;oEAEpC,cAAc,KAAK,MAAM,IAAI;;;;;;;;;;;;sEAGlC,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAO,WAAU;0EAAsC;;;;;;;;;;;;mDA9BnD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BA4C9B,6LAAC,oIAAA,CAAA,QAAK;oBACJ,QAAQ;oBACR,SAAS;wBAAQ;wBAAa,gBAAgB;oBAAQ;oBACtD,OAAO,CAAC,8BAA8B,EAAE,YAAY,KAAK,CAAC;oBAC1D,UAAS;8BAET,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAG;oCAAG;iCAAE,CAAC,GAAG,CAAC,CAAC,qBACd,6LAAC;wCAAe,WAAU;;0DACxB,6LAAC;gDAAI,WAAW,CAAC,+DAA+D,EAC9E,QAAQ,cACJ,6CACA,iCACJ;0DACC,OAAO,4BACN,6LAAC,gOAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;yEAE3B,6LAAC;oDAAK,WAAU;8DAAuB;;;;;;;;;;;0DAG3C,6LAAC;gDAAI,WAAU;;oDACZ,SAAS,KAAK;oDACd,SAAS,KAAK;oDACd,SAAS,KAAK;;;;;;;4CAEhB,OAAO,mBACN,6LAAC;gDAAI,WAAW,CAAC,gBAAgB,EAC/B,OAAO,cAAc,iBAAiB,eACtC;;;;;;;uCApBI;;;;;;;;;;4BA2Bb,iBAAiB,MAAM,GAAG,mBACzB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gPAAA,CAAA,0BAAuB;4CAAC,WAAU;;;;;;sDACnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,6LAAC;oDAAG,WAAU;8DACX,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,6LAAC;sEAAgB;2DAAR;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASrB,6LAAC;gCAAI,WAAU;;oCACZ,gBAAgB,mBACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,MAAK;gEACL,QAAQ;gEACR,WAAU;gEACV,OAAO,QAAQ,IAAI;gEACnB,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oEAAC;gEAC/D,aAAY;;;;;;;;;;;;kEAGhB,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,MAAK;wEACL,QAAQ;wEACR,WAAU;wEACV,OAAO,QAAQ,GAAG;wEAClB,UAAU,CAAC,IAAM,WAAW;gFAAE,GAAG,OAAO;gFAAE,KAAK,EAAE,MAAM,CAAC,KAAK;4EAAC;wEAC9D,aAAY;;;;;;kFAEd,6LAAC;wEACC,MAAK;wEACL,SAAS,IAAM,WAAW;gFAAE,GAAG,OAAO;gFAAE,KAAK;4EAAc;wEAC3D,WAAU;kFAEV,cAAA,6LAAC,4OAAA,CAAA,wBAAqB;4EAAC,WAAU;;;;;;;;;;;;;;;;;0EAGrC,6LAAC;gEAAE,WAAU;0EAA6B;;;;;;;;;;;;kEAE5C,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,QAAQ;gEACR,WAAU;gEACV,OAAO,QAAQ,WAAW;gEAC1B,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oEAAC;;kFAEtE,6LAAC;wEAAO,OAAM;kFAAG;;;;;;oEAChB,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;4EAAyB,OAAO,SAAS,EAAE;sFAAG,SAAS,IAAI;2EAA/C,SAAS,EAAE;;;;;;;;;;;;;;;;;kEAI9B,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,QAAQ;gEACR,WAAU;gEACV,OAAO,QAAQ,eAAe;gEAC9B,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;oEAAC;;kFAE1E,6LAAC;wEAAO,OAAM;kFAAS;;;;;;kFACvB,6LAAC;wEAAO,OAAM;kFAAK;;;;;;kFACnB,6LAAC;wEAAO,OAAM;kFAAQ;;;;;;kFACtB,6LAAC;wEAAO,OAAM;kFAAS;;;;;;kFACvB,6LAAC;wEAAO,OAAM;kFAAU;;;;;;kFACxB,6LAAC;wEAAO,OAAM;kFAAQ;;;;;;kFACtB,6LAAC;wEAAO,OAAM;kFAAK;;;;;;kFACnB,6LAAC;wEAAO,OAAM;kFAAU;;;;;;;;;;;;;;;;;;kEAG5B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,MAAM;gEACN,WAAU;gEACV,OAAO,QAAQ,WAAW;gEAC1B,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACtE,aAAY;;;;;;;;;;;;;;;;;;;;;;;;oCAOrB,gBAAgB,mBACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,MAAK;gEACL,QAAQ;gEACR,WAAU;gEACV,OAAO,QAAQ,aAAa;gEAC5B,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oEAAE;gEACzF,aAAY;;;;;;;;;;;;kEAGhB,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,MAAK;gEACL,QAAQ;gEACR,WAAU;gEACV,OAAO,QAAQ,aAAa;gEAC5B,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oEAAE;gEACzF,aAAY;;;;;;;;;;;;kEAGhB,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,MAAK;gEACL,WAAU;gEACV,OAAO,QAAQ,aAAa;gEAC5B,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oEAAE;gEACzF,aAAY;;;;;;;;;;;;kEAGhB,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,MAAK;gEACL,WAAU;gEACV,OAAO,QAAQ,aAAa;gEAC5B,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oEAAE;gEACzF,aAAY;;;;;;;;;;;;kEAGhB,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,MAAK;gEACL,QAAQ;gEACR,WAAU;gEACV,OAAO,QAAQ,SAAS;gEACxB,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,WAAW,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oEAAE;gEACrF,aAAY;;;;;;;;;;;;kEAGhB,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEAAI,WAAU;;oEAAoF;oEAC/F,CAAC,QAAQ,aAAa,GAAG,QAAQ,SAAS,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;;0DAM5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAW,CAAC,YAAY,EAAE,QAAQ,aAAa,GAAG,QAAQ,aAAa,GAAG,gCAAgC,2BAA2B;;kFACxI,6LAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAc;oEAAE,QAAQ,aAAa,GAAG,QAAQ,aAAa,GAAG,mBAAmB;;;;;;;0EAEnH,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAmB;oEAAE,QAAQ,aAAa,GAAG,IAAI,KAAK,KAAK,CAAC,QAAQ,aAAa,GAAG,QAAQ,aAAa,GAAG,MAAM;oEAAM;;;;;;;0EAExJ,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAgB;oEAAE,QAAQ,aAAa,GAAG,IAAI,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;oCAOhG,gBAAgB,mBACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,MAAK;gEACL,WAAU;gEACV,OAAO,QAAQ,aAAa;gEAC5B,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,eAAe,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACxE,aAAY;;;;;;;;;;;;kEAGhB,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,MAAK;gEACL,WAAU;gEACV,OAAO,QAAQ,aAAa;gEAC5B,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,eAAe,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACxE,aAAY;;;;;;;;;;;;kEAGhB,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,MAAK;gEACL,WAAU;gEACV,OAAO,QAAQ,YAAY;gEAC3B,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,cAAc,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACvE,aAAY;;;;;;;;;;;;kEAGhB,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,MAAK;gEACL,WAAU;gEACV,OAAO,QAAQ,WAAW;gEAC1B,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oEAAC;;;;;;;;;;;;kEAG1E,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,MAAK;gEACL,WAAU;gEACV,OAAO,QAAQ,QAAQ;gEACvB,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACnE,aAAY;;;;;;;;;;;;kEAGhB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,MAAM;gEACN,WAAU;gEACV,OAAO,QAAQ,KAAK;gEACpB,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAC;gEAChE,aAAY;;;;;;;;;;;;;;;;;;0DAMlB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAM,WAAU;;kFACf,6LAAC;wEACC,MAAK;wEACL,WAAU;wEACV,SAAS,QAAQ,YAAY;wEAC7B,UAAU,CAAC,IAAM,WAAW;gFAAE,GAAG,OAAO;gFAAE,cAAc,EAAE,MAAM,CAAC,OAAO;4EAAC;;;;;;kFAE3E,6LAAC;wEAAK,WAAU;kFAA6B;;;;;;;;;;;;0EAE/C,6LAAC;gEAAM,WAAU;;kFACf,6LAAC;wEACC,MAAK;wEACL,WAAU;wEACV,SAAS,QAAQ,sBAAsB;wEACvC,UAAU,CAAC,IAAM,WAAW;gFAAE,GAAG,OAAO;gFAAE,wBAAwB,EAAE,MAAM,CAAC,OAAO;4EAAC;;;;;;kFAErF,6LAAC;wEAAK,WAAU;kFAA6B;;;;;;;;;;;;;;;;;;;;;;;;0DAMnD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA0C;;;;;;kEACxD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFAAI,6LAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAY;oEAAE,QAAQ,IAAI,IAAI;;;;;;;0EACjE,6LAAC;;kFAAI,6LAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAW;oEAAE,QAAQ,GAAG,IAAI;;;;;;;0EAC/D,6LAAC;;kFAAI,6LAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAgB;oEAAE,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,QAAQ,WAAW,GAAG,QAAQ;;;;;;;0EACrH,6LAAC;;kFAAI,6LAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAa;oEAAE,QAAQ,aAAa;oEAAC;oEAAE,QAAQ,eAAe;;;;;;;0EACjG,6LAAC;;kFAAI,6LAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAiB;oEAAG,QAAQ,SAAS,CAAC,OAAO,CAAC;;;;;;;0EACjF,6LAAC;;kFAAI,6LAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAmB;oEAAG,CAAC,QAAQ,aAAa,GAAG,QAAQ,SAAS,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQvH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS,gBAAgB,IAAI;4CAAQ;4CAAa,gBAAgB;wCAAQ,IAAI;wCAC9E,WAAU;wCACV,UAAU;kDAET,gBAAgB,IAAI,WAAW;;;;;;kDAGlC,6LAAC;wCAAI,WAAU;kDACZ,cAAc,kBACb,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,gBAAgB,KAAK,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,WAAW;4CACrF,WAAU;sDACX;;;;;iEAID,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU;4CACV,WAAU;sDAET,2BACC;;kEACE,6LAAC;wDAAI,WAAU;wDAA6C,MAAK;wDAAO,SAAQ;;0EAC9E,6LAAC;gEAAO,WAAU;gEAAa,IAAG;gEAAK,IAAG;gEAAK,GAAE;gEAAK,QAAO;gEAAe,aAAY;;;;;;0EACxF,6LAAC;gEAAK,WAAU;gEAAa,MAAK;gEAAe,GAAE;;;;;;;;;;;;oDAC/C;;6EAIR;;kEACE,6LAAC,gOAAA,CAAA,kBAAe;wDAAC,WAAU;;;;;;oDAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa1E;GA18BwB;;QACL,kIAAA,CAAA,UAAO;;;KADF", "debugId": null}}]}