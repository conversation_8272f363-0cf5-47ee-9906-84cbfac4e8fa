{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\nimport {\n  HomeIcon,\n  CubeIcon,\n  BeakerIcon,\n  CogIcon,\n  DocumentTextIcon,\n  UsersIcon,\n  ChartBarIcon,\n  ClipboardDocumentListIcon,\n} from '@heroicons/react/24/outline'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { clsx } from 'clsx'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/', icon: HomeIcon },\n  { name: 'Inventory', href: '/inventory', icon: CubeIcon },\n  { name: 'Recipes', href: '/recipes', icon: ClipboardDocumentListIcon },\n  { name: 'Production', href: '/production', icon: CogIcon },\n  { name: 'Quality', href: '/quality', icon: BeakerIcon },\n  { name: 'Reports', href: '/reports', icon: ChartBarIcon },\n  { name: 'Documents', href: '/documents', icon: DocumentTextIcon },\n  { name: 'Users', href: '/users', icon: UsersIcon },\n]\n\ninterface SidebarProps {\n  open: boolean\n  setOpen: (open: boolean) => void\n}\n\nexport function Sidebar({ open, setOpen }: SidebarProps) {\n  const pathname = usePathname()\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <Transition.Root show={open} as={Fragment}>\n        <Dialog as=\"div\" className=\"relative z-50 lg:hidden\" onClose={setOpen}>\n          <Transition.Child\n            as={Fragment}\n            enter=\"transition-opacity ease-linear duration-300\"\n            enterFrom=\"opacity-0\"\n            enterTo=\"opacity-100\"\n            leave=\"transition-opacity ease-linear duration-300\"\n            leaveFrom=\"opacity-100\"\n            leaveTo=\"opacity-0\"\n          >\n            <div className=\"fixed inset-0 bg-gray-900/80\" />\n          </Transition.Child>\n\n          <div className=\"fixed inset-0 flex\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"transition ease-in-out duration-300 transform\"\n              enterFrom=\"-translate-x-full\"\n              enterTo=\"translate-x-0\"\n              leave=\"transition ease-in-out duration-300 transform\"\n              leaveFrom=\"translate-x-0\"\n              leaveTo=\"-translate-x-full\"\n            >\n              <Dialog.Panel className=\"relative mr-16 flex w-full max-w-xs flex-1\">\n                <Transition.Child\n                  as={Fragment}\n                  enter=\"ease-in-out duration-300\"\n                  enterFrom=\"opacity-0\"\n                  enterTo=\"opacity-100\"\n                  leave=\"ease-in-out duration-300\"\n                  leaveFrom=\"opacity-100\"\n                  leaveTo=\"opacity-0\"\n                >\n                  <div className=\"absolute left-full top-0 flex w-16 justify-center pt-5\">\n                    <button type=\"button\" className=\"-m-2.5 p-2.5\" onClick={() => setOpen(false)}>\n                      <span className=\"sr-only\">Close sidebar</span>\n                      <XMarkIcon className=\"h-6 w-6 text-white\" aria-hidden=\"true\" />\n                    </button>\n                  </div>\n                </Transition.Child>\n                <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4\">\n                  <div className=\"flex h-16 shrink-0 items-center\">\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">SFF</span>\n                    </div>\n                    <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n                  </div>\n                  <nav className=\"flex flex-1 flex-col\">\n                    <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n                      <li>\n                        <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                          {navigation.map((item) => (\n                            <li key={item.name}>\n                              <Link\n                                href={item.href}\n                                className={clsx(\n                                  pathname === item.href\n                                    ? 'bg-green-50 text-green-700'\n                                    : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                                  'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                                )}\n                              >\n                                <item.icon\n                                  className={clsx(\n                                    pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                                    'h-6 w-6 shrink-0'\n                                  )}\n                                  aria-hidden=\"true\"\n                                />\n                                {item.name}\n                              </Link>\n                            </li>\n                          ))}\n                        </ul>\n                      </li>\n                    </ul>\n                  </nav>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </Dialog>\n      </Transition.Root>\n\n      {/* Static sidebar for desktop */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 bg-white px-6 pb-4\">\n          <div className=\"flex h-16 shrink-0 items-center\">\n            <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-sm\">SFF</span>\n            </div>\n            <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n          </div>\n          <nav className=\"flex flex-1 flex-col\">\n            <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n              <li>\n                <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                  {navigation.map((item) => (\n                    <li key={item.name}>\n                      <Link\n                        href={item.href}\n                        className={clsx(\n                          pathname === item.href\n                            ? 'bg-green-50 text-green-700'\n                            : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                          'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                        )}\n                      >\n                        <item.icon\n                          className={clsx(\n                            pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                            'h-6 w-6 shrink-0'\n                          )}\n                          aria-hidden=\"true\"\n                        />\n                        {item.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </li>\n            </ul>\n          </nav>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AAjBA;;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,+MAAA,CAAA,WAAQ;IAAC;IAC/C;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,+MAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,iPAAA,CAAA,4BAAyB;IAAC;IACrE;QAAE,MAAM;QAAc,MAAM;QAAe,MAAM,6MAAA,CAAA,UAAO;IAAC;IACzD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,mNAAA,CAAA,aAAU;IAAC;IACtD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,uNAAA,CAAA,eAAY;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,+NAAA,CAAA,mBAAgB;IAAC;IAChE;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,iNAAA,CAAA,YAAS;IAAC;CAClD;AAOM,SAAS,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAgB;IACrD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE;;0BAEE,8OAAC,uLAAA,CAAA,aAAU,CAAC,IAAI;gBAAC,MAAM;gBAAM,IAAI,qMAAA,CAAA,WAAQ;0BACvC,cAAA,8OAAC,+KAAA,CAAA,SAAM;oBAAC,IAAG;oBAAM,WAAU;oBAA0B,SAAS;;sCAC5D,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,qMAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;gCACf,IAAI,qMAAA,CAAA,WAAQ;gCACZ,OAAM;gCACN,WAAU;gCACV,SAAQ;gCACR,OAAM;gCACN,WAAU;gCACV,SAAQ;0CAER,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;oCAAC,WAAU;;sDACtB,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;4CACf,IAAI,qMAAA,CAAA,WAAQ;4CACZ,OAAM;4CACN,WAAU;4CACV,SAAQ;4CACR,OAAM;4CACN,WAAU;4CACV,SAAQ;sDAER,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAO,MAAK;oDAAS,WAAU;oDAAe,SAAS,IAAM,QAAQ;;sEACpE,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC,iNAAA,CAAA,YAAS;4DAAC,WAAU;4DAAqB,eAAY;;;;;;;;;;;;;;;;;;;;;;sDAI5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,8OAAC;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;8DAE7D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAG,MAAK;wDAAO,WAAU;kEACxB,cAAA,8OAAC;sEACC,cAAA,8OAAC;gEAAG,MAAK;gEAAO,WAAU;0EACvB,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;kFACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4EACH,MAAM,KAAK,IAAI;4EACf,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;8FAGF,8OAAC,KAAK,IAAI;oFACR,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;oFAEF,eAAY;;;;;;gFAEb,KAAK,IAAI;;;;;;;uEAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiCxC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAE7D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,MAAK;gCAAO,WAAU;0CACxB,cAAA,8OAAC;8CACC,cAAA,8OAAC;wCAAG,MAAK;wCAAO,WAAU;kDACvB,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;sEAGF,8OAAC,KAAK,IAAI;4DACR,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;4DAEF,eAAY;;;;;;wDAEb,KAAK,IAAI;;;;;;;+CAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BtC", "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Menu, Transition } from '@headlessui/react'\nimport { Bars3Icon, BellIcon } from '@heroicons/react/24/outline'\nimport { useAuth } from '@/components/providers'\nimport { clsx } from 'clsx'\n\ninterface HeaderProps {\n  setSidebarOpen: (open: boolean) => void\n}\n\nexport function Header({ setSidebarOpen }: HeaderProps) {\n  const { user, signOut } = useAuth()\n\n  const userNavigation = [\n    { name: 'Your profile', href: '#' },\n    { name: 'Settings', href: '#' },\n    { name: 'Sign out', href: '#', onClick: signOut },\n  ]\n\n  return (\n    <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n      <button\n        type=\"button\"\n        className=\"-m-2.5 p-2.5 text-gray-700 lg:hidden\"\n        onClick={() => setSidebarOpen(true)}\n      >\n        <span className=\"sr-only\">Open sidebar</span>\n        <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n      </button>\n\n      {/* Separator */}\n      <div className=\"h-6 w-px bg-gray-200 lg:hidden\" aria-hidden=\"true\" />\n\n      <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n        <div className=\"relative flex flex-1\">\n          {/* Search can be added here later */}\n        </div>\n        <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n          <button type=\"button\" className=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\">\n            <span className=\"sr-only\">View notifications</span>\n            <BellIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n\n          {/* Separator */}\n          <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200\" aria-hidden=\"true\" />\n\n          {/* Profile dropdown */}\n          <Menu as=\"div\" className=\"relative\">\n            <Menu.Button className=\"-m-1.5 flex items-center p-1.5\">\n              <span className=\"sr-only\">Open user menu</span>\n              <div className=\"h-8 w-8 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.username?.charAt(0).toUpperCase() || 'U'}\n                </span>\n              </div>\n              <span className=\"hidden lg:flex lg:items-center\">\n                <span className=\"ml-4 text-sm font-semibold leading-6 text-gray-900\" aria-hidden=\"true\">\n                  {user?.username || 'User'}\n                </span>\n                <span className=\"ml-2 text-xs text-gray-500 capitalize\">\n                  ({user?.role?.replace('_', ' ') || 'Role'})\n                </span>\n              </span>\n            </Menu.Button>\n            <Transition\n              as={Fragment}\n              enter=\"transition ease-out duration-100\"\n              enterFrom=\"transform opacity-0 scale-95\"\n              enterTo=\"transform opacity-100 scale-100\"\n              leave=\"transition ease-in duration-75\"\n              leaveFrom=\"transform opacity-100 scale-100\"\n              leaveTo=\"transform opacity-0 scale-95\"\n            >\n              <Menu.Items className=\"absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none\">\n                {userNavigation.map((item) => (\n                  <Menu.Item key={item.name}>\n                    {({ active }) => (\n                      <button\n                        onClick={item.onClick}\n                        className={clsx(\n                          active ? 'bg-gray-50' : '',\n                          'block w-full text-left px-3 py-1 text-sm leading-6 text-gray-900'\n                        )}\n                      >\n                        {item.name}\n                      </button>\n                    )}\n                  </Menu.Item>\n                ))}\n              </Menu.Items>\n            </Transition>\n          </Menu>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;AANA;;;;;;;AAYO,SAAS,OAAO,EAAE,cAAc,EAAe;IACpD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAAgB,MAAM;QAAI;QAClC;YAAE,MAAM;YAAY,MAAM;QAAI;QAC9B;YAAE,MAAM;YAAY,MAAM;YAAK,SAAS;QAAQ;KACjD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,eAAe;;kCAE9B,8OAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,8OAAC,iNAAA,CAAA,YAAS;wBAAC,WAAU;wBAAU,eAAY;;;;;;;;;;;;0BAI7C,8OAAC;gBAAI,WAAU;gBAAiC,eAAY;;;;;;0BAE5D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,MAAK;gCAAS,WAAU;;kDAC9B,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC,+MAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;0CAI5C,8OAAC;gCAAI,WAAU;gCAAgD,eAAY;;;;;;0CAG3E,8OAAC,2KAAA,CAAA,OAAI;gCAAC,IAAG;gCAAM,WAAU;;kDACvB,8OAAC,2KAAA,CAAA,OAAI,CAAC,MAAM;wCAAC,WAAU;;0DACrB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,MAAM,UAAU,OAAO,GAAG,iBAAiB;;;;;;;;;;;0DAGhD,8OAAC;gDAAK,WAAU;;kEACd,8OAAC;wDAAK,WAAU;wDAAqD,eAAY;kEAC9E,MAAM,YAAY;;;;;;kEAErB,8OAAC;wDAAK,WAAU;;4DAAwC;4DACpD,MAAM,MAAM,QAAQ,KAAK,QAAQ;4DAAO;;;;;;;;;;;;;;;;;;;kDAIhD,8OAAC,uLAAA,CAAA,aAAU;wCACT,IAAI,qMAAA,CAAA,WAAQ;wCACZ,OAAM;wCACN,WAAU;wCACV,SAAQ;wCACR,OAAM;wCACN,WAAU;wCACV,SAAQ;kDAER,cAAA,8OAAC,2KAAA,CAAA,OAAI,CAAC,KAAK;4CAAC,WAAU;sDACnB,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,2KAAA,CAAA,OAAI,CAAC,IAAI;8DACP,CAAC,EAAE,MAAM,EAAE,iBACV,8OAAC;4DACC,SAAS,KAAK,OAAO;4DACrB,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,SAAS,eAAe,IACxB;sEAGD,KAAK,IAAI;;;;;;mDATA,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqB3C", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/components/providers'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const { user } = useAuth()\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Sidebar open={sidebarOpen} setOpen={setSidebarOpen} />\n      \n      <div className=\"lg:pl-72\">\n        <Header setSidebarOpen={setSidebarOpen} />\n        \n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAWO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAa,SAAS;;;;;;0BAErC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,SAAM;wBAAC,gBAAgB;;;;;;kCAExB,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/ui/modal.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\n\ninterface ModalProps {\n  isOpen: boolean\n  onClose: () => void\n  title: string\n  children: React.ReactNode\n  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl'\n}\n\nexport function Modal({ isOpen, onClose, title, children, maxWidth = 'lg' }: ModalProps) {\n  const maxWidthClasses = {\n    sm: 'sm:max-w-sm',\n    md: 'sm:max-w-md',\n    lg: 'sm:max-w-lg',\n    xl: 'sm:max-w-xl',\n    '2xl': 'sm:max-w-2xl'\n  }\n\n  return (\n    <Transition.Root show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={onClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-out duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in duration-200\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 z-10 overflow-y-auto\">\n          <div className=\"flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"ease-out duration-300\"\n              enterFrom=\"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95\"\n              enterTo=\"opacity-100 translate-y-0 sm:scale-100\"\n              leave=\"ease-in duration-200\"\n              leaveFrom=\"opacity-100 translate-y-0 sm:scale-100\"\n              leaveTo=\"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95\"\n            >\n              <Dialog.Panel className={`relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full ${maxWidthClasses[maxWidth]} sm:p-6`}>\n                <div className=\"absolute right-0 top-0 hidden pr-4 pt-4 sm:block\">\n                  <button\n                    type=\"button\"\n                    className=\"rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2\"\n                    onClick={onClose}\n                  >\n                    <span className=\"sr-only\">Close</span>\n                    <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                  </button>\n                </div>\n                <div className=\"sm:flex sm:items-start\">\n                  <div className=\"mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full\">\n                    <Dialog.Title as=\"h3\" className=\"text-lg font-semibold leading-6 text-gray-900 mb-4\">\n                      {title}\n                    </Dialog.Title>\n                    <div className=\"mt-2\">\n                      {children}\n                    </div>\n                  </div>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </div>\n      </Dialog>\n    </Transition.Root>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAcO,SAAS,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAc;IACrF,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;IACT;IAEA,qBACE,8OAAC,uLAAA,CAAA,aAAU,CAAC,IAAI;QAAC,MAAM;QAAQ,IAAI,qMAAA,CAAA,WAAQ;kBACzC,cAAA,8OAAC,+KAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,qMAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,qMAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;gCAAC,WAAW,CAAC,2HAA2H,EAAE,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC;;kDACvL,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS;;8DAET,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;;;;;;kDAG/C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;oDAAC,IAAG;oDAAK,WAAU;8DAC7B;;;;;;8DAEH,8OAAC;oDAAI,WAAU;8DACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvB", "debugId": null}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createClientComponentClient } from '@supabase/auth-helpers-nextjs'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Client component client\nexport const createSupabaseClient = () => createClientComponentClient()\n\n// Admin client (server-side only)\nexport const createSupabaseAdminClient = () => {\n  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n  return createClient(supabaseUrl, serviceRoleKey, {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  })\n}\n\n// Database types\nexport interface Database {\n  public: {\n    Tables: {\n      profiles: {\n        Row: {\n          id: string\n          email: string\n          full_name: string\n          role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department: string | null\n          phone: string | null\n          is_active: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          email: string\n          full_name: string\n          role?: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department?: string | null\n          phone?: string | null\n          is_active?: boolean\n        }\n        Update: {\n          email?: string\n          full_name?: string\n          role?: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department?: string | null\n          phone?: string | null\n          is_active?: boolean\n        }\n      }\n      inventory_categories: {\n        Row: {\n          id: string\n          name: string\n          description: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          description?: string | null\n        }\n        Update: {\n          name?: string\n          description?: string | null\n        }\n      }\n      inventory_items: {\n        Row: {\n          id: string\n          name: string\n          sku: string\n          category_id: string | null\n          description: string | null\n          unit_of_measure: string\n          current_stock: number\n          minimum_stock: number\n          maximum_stock: number | null\n          unit_cost: number\n          supplier_info: any | null\n          storage_conditions: string | null\n          expiry_date: string | null\n          batch_number: string | null\n          is_active: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          sku: string\n          category_id?: string | null\n          description?: string | null\n          unit_of_measure: string\n          current_stock?: number\n          minimum_stock?: number\n          maximum_stock?: number | null\n          unit_cost?: number\n          supplier_info?: any | null\n          storage_conditions?: string | null\n          expiry_date?: string | null\n          batch_number?: string | null\n          is_active?: boolean\n        }\n        Update: {\n          name?: string\n          sku?: string\n          category_id?: string | null\n          description?: string | null\n          unit_of_measure?: string\n          current_stock?: number\n          minimum_stock?: number\n          maximum_stock?: number | null\n          unit_cost?: number\n          supplier_info?: any | null\n          storage_conditions?: string | null\n          expiry_date?: string | null\n          batch_number?: string | null\n          is_active?: boolean\n        }\n      }\n      recipes: {\n        Row: {\n          id: string\n          name: string\n          code: string\n          description: string | null\n          category: string | null\n          version: number\n          batch_size: number\n          unit_of_measure: string\n          estimated_cost: number | null\n          preparation_time: number | null\n          instructions: string | null\n          notes: string | null\n          is_active: boolean\n          created_by: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          code: string\n          description?: string | null\n          category?: string | null\n          version?: number\n          batch_size: number\n          unit_of_measure: string\n          estimated_cost?: number | null\n          preparation_time?: number | null\n          instructions?: string | null\n          notes?: string | null\n          is_active?: boolean\n          created_by?: string | null\n        }\n        Update: {\n          name?: string\n          code?: string\n          description?: string | null\n          category?: string | null\n          version?: number\n          batch_size?: number\n          unit_of_measure?: string\n          estimated_cost?: number | null\n          preparation_time?: number | null\n          instructions?: string | null\n          notes?: string | null\n          is_active?: boolean\n        }\n      }\n      production_batches: {\n        Row: {\n          id: string\n          batch_number: string\n          recipe_id: string\n          batch_type: 'test' | 'production'\n          planned_quantity: number\n          actual_quantity: number | null\n          unit_of_measure: string\n          status: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date: string | null\n          actual_start_date: string | null\n          planned_end_date: string | null\n          actual_end_date: string | null\n          production_cost: number | null\n          yield_percentage: number | null\n          quality_approved: boolean | null\n          notes: string | null\n          created_by: string | null\n          assigned_to: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          batch_number: string\n          recipe_id: string\n          batch_type: 'test' | 'production'\n          planned_quantity: number\n          actual_quantity?: number | null\n          unit_of_measure: string\n          status?: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority?: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date?: string | null\n          actual_start_date?: string | null\n          planned_end_date?: string | null\n          actual_end_date?: string | null\n          production_cost?: number | null\n          yield_percentage?: number | null\n          quality_approved?: boolean | null\n          notes?: string | null\n          created_by?: string | null\n          assigned_to?: string | null\n        }\n        Update: {\n          batch_number?: string\n          recipe_id?: string\n          batch_type?: 'test' | 'production'\n          planned_quantity?: number\n          actual_quantity?: number | null\n          unit_of_measure?: string\n          status?: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority?: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date?: string | null\n          actual_start_date?: string | null\n          planned_end_date?: string | null\n          actual_end_date?: string | null\n          production_cost?: number | null\n          yield_percentage?: number | null\n          quality_approved?: boolean | null\n          notes?: string | null\n          assigned_to?: string | null\n        }\n      }\n    }\n    Views: {\n      [_ in never]: never\n    }\n    Functions: {\n      check_recipe_availability: {\n        Args: {\n          recipe_uuid: string\n          batch_size_multiplier?: number\n        }\n        Returns: {\n          item_id: string\n          item_name: string\n          required_quantity: number\n          available_quantity: number\n          is_sufficient: boolean\n        }[]\n      }\n      calculate_recipe_cost: {\n        Args: {\n          recipe_uuid: string\n          batch_size_multiplier?: number\n        }\n        Returns: number\n      }\n    }\n    Enums: {\n      user_role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,uBAAuB,IAAM,CAAA,GAAA,wKAAA,CAAA,8BAA2B,AAAD;AAG7D,MAAM,4BAA4B;IACvC,MAAM,iBAAiB,QAAQ,GAAG,CAAC,yBAAyB;IAC5D,OAAO,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,gBAAgB;QAC/C,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 1036, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/lib/database.ts"], "sourcesContent": ["import { createSupabaseClient } from './supabase'\n\nconst supabase = createSupabaseClient()\n\n// Inventory Items\nexport interface InventoryItem {\n  id: string\n  name: string\n  sku: string\n  category_id: string\n  current_stock: number\n  minimum_stock: number\n  unit_of_measure: string\n  unit_cost: number\n  supplier?: string\n  description?: string\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface InventoryCategory {\n  id: string\n  name: string\n  description?: string\n  is_active: boolean\n}\n\n// Recipes\nexport interface Recipe {\n  id: string\n  name: string\n  code: string\n  category: string\n  batch_size: number\n  unit_of_measure: string\n  instructions?: string\n  is_active: boolean\n  version: number\n  created_at: string\n  updated_at: string\n}\n\n// Production Batches\nexport interface ProductionBatch {\n  id: string\n  batch_number: string\n  recipe_id: string\n  batch_type: 'test' | 'production'\n  planned_quantity: number\n  actual_quantity?: number\n  unit_of_measure: string\n  status: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n  priority: 'low' | 'normal' | 'high' | 'urgent'\n  planned_start_date?: string\n  planned_end_date?: string\n  actual_start_date?: string\n  actual_end_date?: string\n  assigned_to?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\n// Quality Documents\nexport interface QualityDocument {\n  id: string\n  document_type: 'msds' | 'coa' | 'tds' | 'quality_spec' | 'lab_report'\n  title: string\n  document_number: string\n  version: string\n  item_id?: string\n  batch_id?: string\n  status: 'draft' | 'review' | 'approved' | 'expired'\n  valid_from?: string\n  valid_until?: string\n  file_path?: string\n  created_by: string\n  created_at: string\n  updated_at: string\n}\n\n// Database service functions\nexport class DatabaseService {\n  // Inventory Items\n  static async getInventoryItems(): Promise<InventoryItem[]> {\n    try {\n      const { data, error } = await supabase\n        .from('inventory_items')\n        .select(`\n          *,\n          inventory_categories(name)\n        `)\n        .eq('is_active', true)\n        .order('name')\n\n      if (error) {\n        console.error('Error fetching inventory items:', error)\n        // Only return demo data if it's a connection error\n        if (error.message.includes('Failed to fetch') || error.message.includes('network')) {\n          return this.getDemoInventoryItems()\n        }\n        return []\n      }\n\n      // Return actual data from database (even if empty)\n      return data || []\n    } catch (error) {\n      console.error('Database connection error:', error)\n      return this.getDemoInventoryItems()\n    }\n  }\n\n  static getDemoInventoryItems(): InventoryItem[] {\n    return [\n      {\n        id: 'demo_item_1',\n        name: 'Vanilla Extract Premium',\n        sku: 'VAN-001',\n        category_id: 'demo_cat_1',\n        current_stock: 25.5,\n        minimum_stock: 10.0,\n        unit_of_measure: 'liters',\n        unit_cost: 12.50,\n        supplier: 'Premium Ingredients Co.',\n        description: 'High-quality vanilla extract for premium flavoring applications',\n        is_active: true,\n        created_at: '2024-01-15T08:00:00Z',\n        updated_at: '2024-01-15T08:00:00Z'\n      },\n      {\n        id: 'demo_item_2',\n        name: 'Strawberry Concentrate',\n        sku: 'STR-002',\n        category_id: 'demo_cat_1',\n        current_stock: 8.2,\n        minimum_stock: 15.0,\n        unit_of_measure: 'liters',\n        unit_cost: 18.75,\n        supplier: 'Fruit Essences Ltd.',\n        description: 'Natural strawberry concentrate for beverage and dessert applications',\n        is_active: true,\n        created_at: '2024-01-20T10:30:00Z',\n        updated_at: '2024-01-20T10:30:00Z'\n      },\n      {\n        id: 'demo_item_3',\n        name: 'Citric Acid Food Grade',\n        sku: 'CIT-003',\n        category_id: 'demo_cat_2',\n        current_stock: 45.0,\n        minimum_stock: 20.0,\n        unit_of_measure: 'kg',\n        unit_cost: 3.25,\n        supplier: 'Chemical Solutions Inc.',\n        description: 'Food-grade citric acid for pH adjustment and preservation',\n        is_active: true,\n        created_at: '2024-02-01T14:15:00Z',\n        updated_at: '2024-02-01T14:15:00Z'\n      },\n      {\n        id: 'demo_item_4',\n        name: 'Glass Bottles 500ml',\n        sku: 'BTL-004',\n        category_id: 'demo_cat_3',\n        current_stock: 0,\n        minimum_stock: 100,\n        unit_of_measure: 'pieces',\n        unit_cost: 0.85,\n        supplier: 'Packaging Solutions Ltd.',\n        description: 'Clear glass bottles with screw caps for beverage packaging',\n        is_active: true,\n        created_at: '2024-02-10T09:45:00Z',\n        updated_at: '2024-02-10T09:45:00Z'\n      },\n      {\n        id: 'demo_item_5',\n        name: 'Natural Lemon Oil',\n        sku: 'LEM-005',\n        category_id: 'demo_cat_1',\n        current_stock: 3.8,\n        minimum_stock: 5.0,\n        unit_of_measure: 'liters',\n        unit_cost: 45.00,\n        supplier: 'Essential Oils Direct',\n        description: 'Cold-pressed natural lemon oil for citrus flavoring',\n        is_active: true,\n        created_at: '2024-02-15T11:20:00Z',\n        updated_at: '2024-02-15T11:20:00Z'\n      }\n    ]\n  }\n\n  static async createInventoryItem(item: Omit<InventoryItem, 'id' | 'created_at' | 'updated_at'>): Promise<InventoryItem | null> {\n    try {\n      const { data, error } = await supabase\n        .from('inventory_items')\n        .insert([item])\n        .select()\n        .single()\n\n      if (error) {\n        console.error('Error creating inventory item:', error)\n        // Only use demo fallback for connection errors\n        if (error.message.includes('Failed to fetch') || error.message.includes('network')) {\n          return this.createDemoInventoryItem(item)\n        }\n        return null\n      }\n\n      return data\n    } catch (error) {\n      console.error('Database connection error:', error)\n      return this.createDemoInventoryItem(item)\n    }\n  }\n\n  static createDemoInventoryItem(item: Omit<InventoryItem, 'id' | 'created_at' | 'updated_at'>): InventoryItem {\n    return {\n      ...item,\n      id: `demo_item_${Date.now()}`,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }\n  }\n\n  static async updateInventoryItem(id: string, updates: Partial<InventoryItem>): Promise<InventoryItem | null> {\n    const { data, error } = await supabase\n      .from('inventory_items')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error updating inventory item:', error)\n      return null\n    }\n\n    return data\n  }\n\n  // Inventory Categories\n  static async getInventoryCategories(): Promise<InventoryCategory[]> {\n    try {\n      const { data, error } = await supabase\n        .from('inventory_categories')\n        .select('*')\n        .order('name')\n\n      if (error) {\n        console.error('Error fetching inventory categories:', error)\n        // Only return demo data if it's a connection error\n        if (error.message.includes('Failed to fetch') || error.message.includes('network')) {\n          return this.getDemoInventoryCategories()\n        }\n        return []\n      }\n\n      // Return actual data from database (even if empty)\n      return data || []\n    } catch (error) {\n      console.error('Database connection error:', error)\n      return this.getDemoInventoryCategories()\n    }\n  }\n\n  static getDemoInventoryCategories(): InventoryCategory[] {\n    return [\n      {\n        id: 'demo_cat_1',\n        name: 'Flavoring Agents',\n        description: 'Natural and artificial flavoring compounds and extracts',\n        is_active: true\n      },\n      {\n        id: 'demo_cat_2',\n        name: 'Preservatives & Additives',\n        description: 'Food-grade preservatives, stabilizers, and additives',\n        is_active: true\n      },\n      {\n        id: 'demo_cat_3',\n        name: 'Packaging Materials',\n        description: 'Bottles, caps, labels, and packaging supplies',\n        is_active: true\n      },\n      {\n        id: 'demo_cat_4',\n        name: 'Raw Materials',\n        description: 'Base ingredients and raw materials for production',\n        is_active: true\n      },\n      {\n        id: 'demo_cat_5',\n        name: 'Quality Control',\n        description: 'Testing materials and quality control supplies',\n        is_active: true\n      }\n    ]\n  }\n\n  // Recipes\n  static async getRecipes(): Promise<Recipe[]> {\n    const { data, error } = await supabase\n      .from('recipes')\n      .select('*')\n      .eq('is_active', true)\n      .order('name')\n\n    if (error) {\n      console.error('Error fetching recipes:', error)\n      return []\n    }\n\n    return data || []\n  }\n\n  static async createRecipe(recipe: Omit<Recipe, 'id' | 'created_at' | 'updated_at'>): Promise<Recipe | null> {\n    const { data, error } = await supabase\n      .from('recipes')\n      .insert([recipe])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error creating recipe:', error)\n      return null\n    }\n\n    return data\n  }\n\n  // Production Batches\n  static async getProductionBatches(): Promise<ProductionBatch[]> {\n    const { data, error } = await supabase\n      .from('production_batches')\n      .select(`\n        *,\n        recipes(name, code)\n      `)\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      console.error('Error fetching production batches:', error)\n      return []\n    }\n\n    return data || []\n  }\n\n  static async createProductionBatch(batch: Omit<ProductionBatch, 'id' | 'created_at' | 'updated_at'>): Promise<ProductionBatch | null> {\n    const { data, error } = await supabase\n      .from('production_batches')\n      .insert([batch])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error creating production batch:', error)\n      return null\n    }\n\n    return data\n  }\n\n  // Quality Documents\n  static async getQualityDocuments(): Promise<QualityDocument[]> {\n    const { data, error } = await supabase\n      .from('quality_documents')\n      .select('*')\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      console.error('Error fetching quality documents:', error)\n      return []\n    }\n\n    return data || []\n  }\n\n  static async createQualityDocument(doc: Omit<QualityDocument, 'id' | 'created_at' | 'updated_at'>): Promise<QualityDocument | null> {\n    try {\n      const { data, error } = await supabase\n        .from('quality_documents')\n        .insert([doc])\n        .select()\n        .single()\n\n      if (error) {\n        console.error('Error creating quality document:', error)\n        // Only use demo fallback for connection errors\n        if (error.message.includes('Failed to fetch') || error.message.includes('network')) {\n          return {\n            ...doc,\n            id: `demo_doc_${Date.now()}`,\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n          }\n        }\n        return null\n      }\n\n      return data\n    } catch (error) {\n      console.error('Database connection error:', error)\n      return {\n        ...doc,\n        id: `demo_doc_${Date.now()}`,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }\n    }\n  }\n\n  // User Management\n  static async getUsers(): Promise<any[]> {\n    try {\n      const { data, error } = await supabase\n        .from('profiles')\n        .select('*')\n        .order('created_at', { ascending: false })\n\n      if (error) {\n        // Silently handle database errors and return demo users\n        // This is expected when the database is empty or has constraints\n        return this.getDemoUsers()\n      }\n\n      // If no users in database, return demo users\n      if (!data || data.length === 0) {\n        return this.getDemoUsers()\n      }\n\n      return data\n    } catch (error) {\n      // Silently handle connection errors and return demo users\n      return this.getDemoUsers()\n    }\n  }\n\n  static getDemoUsers(): any[] {\n    return [\n      {\n        id: 'demo_admin',\n        username: 'admin',\n        full_name: 'System Administrator',\n        email: '<EMAIL>',\n        role: 'admin',\n        department: 'IT',\n        phone: '+****************',\n        is_active: true,\n        created_at: '2024-01-15T08:00:00Z',\n        updated_at: '2024-01-15T08:00:00Z'\n      },\n      {\n        id: 'demo_quality',\n        username: 'quality',\n        full_name: 'Sarah Johnson',\n        email: '<EMAIL>',\n        role: 'quality_manager',\n        department: 'Quality Assurance',\n        phone: '+****************',\n        is_active: true,\n        created_at: '2024-02-01T10:00:00Z',\n        updated_at: '2024-02-01T10:00:00Z'\n      },\n      {\n        id: 'demo_production',\n        username: 'production',\n        full_name: 'Mike Wilson',\n        email: '<EMAIL>',\n        role: 'production_manager',\n        department: 'Production',\n        phone: '+****************',\n        is_active: true,\n        created_at: '2024-02-15T14:30:00Z',\n        updated_at: '2024-02-15T14:30:00Z'\n      },\n      {\n        id: 'demo_employee',\n        username: 'employee',\n        full_name: 'Emily Davis',\n        email: '<EMAIL>',\n        role: 'employee',\n        department: 'Production',\n        phone: '+****************',\n        is_active: true,\n        created_at: '2024-03-01T09:00:00Z',\n        updated_at: '2024-03-01T09:00:00Z'\n      }\n    ]\n  }\n\n  static async createUser(userData: {\n    username: string\n    email: string\n    password_hash: string\n    role: string\n    full_name: string\n    department: string\n    phone?: string\n    is_active: boolean\n  }): Promise<any | null> {\n    try {\n      // For demo purposes, since we can't create auth users directly,\n      // we'll simulate user creation and return a demo user object\n      const newUser = {\n        id: `demo_${Date.now()}`,\n        username: userData.username,\n        email: userData.email,\n        full_name: userData.full_name,\n        role: userData.role,\n        department: userData.department,\n        phone: userData.phone || null,\n        is_active: userData.is_active,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }\n\n      // In a real implementation, this would create the user in the database\n      // For now, we'll just return the user object for demo purposes\n      return newUser\n\n      // Commented out actual database insertion due to auth constraints\n      /*\n      const { data, error } = await supabase\n        .from('profiles')\n        .insert([{\n          ...userData,\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        }])\n        .select()\n        .single()\n\n      if (error) {\n        console.error('Error creating user:', error)\n        return null\n      }\n\n      return data\n      */\n    } catch (error) {\n      return null\n    }\n  }\n\n  static async updateUser(id: string, updates: any): Promise<any | null> {\n    const { data, error } = await supabase\n      .from('profiles')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error updating user:', error)\n      return null\n    }\n\n    return data\n  }\n\n  static async toggleUserStatus(id: string, isActive: boolean): Promise<boolean> {\n    try {\n      // For demo users (those with demo_ prefix), just return success\n      if (id.startsWith('demo_')) {\n        return true\n      }\n\n      const { error } = await supabase\n        .from('profiles')\n        .update({ is_active: isActive, updated_at: new Date().toISOString() })\n        .eq('id', id)\n\n      if (error) {\n        return false\n      }\n\n      return true\n    } catch (error) {\n      return false\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;AAiF7B,MAAM;IACX,kBAAkB;IAClB,aAAa,oBAA8C;QACzD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,mBACL,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,mDAAmD;gBACnD,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,sBAAsB,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;oBAClF,OAAO,IAAI,CAAC,qBAAqB;gBACnC;gBACA,OAAO,EAAE;YACX;YAEA,mDAAmD;YACnD,OAAO,QAAQ,EAAE;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,IAAI,CAAC,qBAAqB;QACnC;IACF;IAEA,OAAO,wBAAyC;QAC9C,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;SACD;IACH;IAEA,aAAa,oBAAoB,IAA6D,EAAiC;QAC7H,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,mBACL,MAAM,CAAC;gBAAC;aAAK,EACb,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,+CAA+C;gBAC/C,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,sBAAsB,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;oBAClF,OAAO,IAAI,CAAC,uBAAuB,CAAC;gBACtC;gBACA,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,IAAI,CAAC,uBAAuB,CAAC;QACtC;IACF;IAEA,OAAO,wBAAwB,IAA6D,EAAiB;QAC3G,OAAO;YACL,GAAG,IAAI;YACP,IAAI,CAAC,UAAU,EAAE,KAAK,GAAG,IAAI;YAC7B,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;IACF;IAEA,aAAa,oBAAoB,EAAU,EAAE,OAA+B,EAAiC;QAC3G,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,mBACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,uBAAuB;IACvB,aAAa,yBAAuD;QAClE,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,wBACL,MAAM,CAAC,KACP,KAAK,CAAC;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,mDAAmD;gBACnD,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,sBAAsB,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;oBAClF,OAAO,IAAI,CAAC,0BAA0B;gBACxC;gBACA,OAAO,EAAE;YACX;YAEA,mDAAmD;YACnD,OAAO,QAAQ,EAAE;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,IAAI,CAAC,0BAA0B;QACxC;IACF;IAEA,OAAO,6BAAkD;QACvD,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;SACD;IACH;IAEA,UAAU;IACV,aAAa,aAAgC;QAC3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,EAAE;QACX;QAEA,OAAO,QAAQ,EAAE;IACnB;IAEA,aAAa,aAAa,MAAwD,EAA0B;QAC1G,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,WACL,MAAM,CAAC;YAAC;SAAO,EACf,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,qBAAqB;IACrB,aAAa,uBAAmD;QAC9D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,sBACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO,EAAE;QACX;QAEA,OAAO,QAAQ,EAAE;IACnB;IAEA,aAAa,sBAAsB,KAAgE,EAAmC;QACpI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,sBACL,MAAM,CAAC;YAAC;SAAM,EACd,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,oBAAoB;IACpB,aAAa,sBAAkD;QAC7D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO,EAAE;QACX;QAEA,OAAO,QAAQ,EAAE;IACnB;IAEA,aAAa,sBAAsB,GAA8D,EAAmC;QAClI,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC;gBAAC;aAAI,EACZ,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,+CAA+C;gBAC/C,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,sBAAsB,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;oBAClF,OAAO;wBACL,GAAG,GAAG;wBACN,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;wBAC5B,YAAY,IAAI,OAAO,WAAW;wBAClC,YAAY,IAAI,OAAO,WAAW;oBACpC;gBACF;gBACA,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;gBACL,GAAG,GAAG;gBACN,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;gBAC5B,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;QACF;IACF;IAEA,kBAAkB;IAClB,aAAa,WAA2B;QACtC,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,wDAAwD;gBACxD,iEAAiE;gBACjE,OAAO,IAAI,CAAC,YAAY;YAC1B;YAEA,6CAA6C;YAC7C,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;gBAC9B,OAAO,IAAI,CAAC,YAAY;YAC1B;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,0DAA0D;YAC1D,OAAO,IAAI,CAAC,YAAY;QAC1B;IACF;IAEA,OAAO,eAAsB;QAC3B,OAAO;YACL;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;SACD;IACH;IAEA,aAAa,WAAW,QASvB,EAAuB;QACtB,IAAI;YACF,gEAAgE;YAChE,6DAA6D;YAC7D,MAAM,UAAU;gBACd,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;gBACxB,UAAU,SAAS,QAAQ;gBAC3B,OAAO,SAAS,KAAK;gBACrB,WAAW,SAAS,SAAS;gBAC7B,MAAM,SAAS,IAAI;gBACnB,YAAY,SAAS,UAAU;gBAC/B,OAAO,SAAS,KAAK,IAAI;gBACzB,WAAW,SAAS,SAAS;gBAC7B,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,uEAAuE;YACvE,+DAA+D;YAC/D,OAAO;QAEP,kEAAkE;QAClE;;;;;;;;;;;;;;;;;MAiBA,GACF,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,aAAa,WAAW,EAAU,EAAE,OAAY,EAAuB;QACrE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,aAAa,iBAAiB,EAAU,EAAE,QAAiB,EAAoB;QAC7E,IAAI;YACF,gEAAgE;YAChE,IAAI,GAAG,UAAU,CAAC,UAAU;gBAC1B,OAAO;YACT;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,YACL,MAAM,CAAC;gBAAE,WAAW;gBAAU,YAAY,IAAI,OAAO,WAAW;YAAG,GACnE,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO;gBACT,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 1468, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/app/recipes/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { useAuth } from '@/components/providers'\nimport { Modal } from '@/components/ui/modal'\nimport { DatabaseService } from '@/lib/database'\nimport {\n  PlusIcon,\n  ClipboardDocumentListIcon,\n  CurrencyDollarIcon,\n  TrashIcon,\n  CalendarIcon,\n  ClockIcon,\n  CheckCircleIcon,\n  ExclamationTriangleIcon\n} from '@heroicons/react/24/outline'\n\ninterface Recipe {\n  id: string\n  name: string\n  code: string\n  category: string\n  batch_size: number\n  unit_of_measure: string\n  estimated_cost: number\n  ingredients_count: number\n  version: number\n  is_active: boolean\n  created_at: string\n}\n\ninterface InventoryItem {\n  id: string\n  name: string\n  sku: string\n  current_stock: number\n  minimum_stock: number\n  unit_of_measure: string\n  unit_cost: number\n  category_name?: string\n}\n\ninterface RecipeIngredient {\n  id: string\n  inventory_item_id: string\n  inventory_item_name: string\n  quantity: number\n  unit_of_measure: string\n  unit_cost: number\n  total_cost: number\n  percentage: number\n  notes?: string\n}\n\ninterface ProductionSchedule {\n  planned_date: string\n  planned_time: string\n  estimated_duration: number // in hours\n  priority: 'low' | 'normal' | 'high' | 'urgent'\n  assigned_to: string\n  equipment_required: string[]\n  special_instructions: string\n}\n\nexport default function RecipesPage() {\n  const { user } = useAuth()\n  const [recipes, setRecipes] = useState<Recipe[]>([])\n  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showAddModal, setShowAddModal] = useState(false)\n  const [currentStep, setCurrentStep] = useState(1)\n\n  // Recipe basic information\n  const [newRecipe, setNewRecipe] = useState({\n    name: '',\n    code: '',\n    category: 'Fruit Flavors',\n    batch_size: 10,\n    unit_of_measure: 'liters',\n    instructions: '',\n    description: '',\n    shelf_life: 12, // months\n    storage_conditions: 'Cool, dry place'\n  })\n\n  // Recipe ingredients\n  const [ingredients, setIngredients] = useState<RecipeIngredient[]>([])\n  const [selectedInventoryItem, setSelectedInventoryItem] = useState('')\n  const [ingredientQuantity, setIngredientQuantity] = useState(0)\n  const [ingredientNotes, setIngredientNotes] = useState('')\n\n  // Production schedule\n  const [schedule, setSchedule] = useState<ProductionSchedule>({\n    planned_date: '',\n    planned_time: '09:00',\n    estimated_duration: 4,\n    priority: 'normal',\n    assigned_to: '',\n    equipment_required: [],\n    special_instructions: ''\n  })\n\n  // Validation and cost calculation\n  const [totalCost, setTotalCost] = useState(0)\n  const [availabilityCheck, setAvailabilityCheck] = useState<{[key: string]: boolean}>({})\n  const [validationErrors, setValidationErrors] = useState<string[]>([])\n\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setLoading(true)\n\n        // Load recipes and inventory items from database\n        const [recipeData, inventoryData] = await Promise.all([\n          DatabaseService.getRecipes(),\n          DatabaseService.getInventoryItems()\n        ])\n\n        // Process inventory items for easier use\n        const processedInventory = inventoryData.map(item => ({\n          ...item,\n          current_stock: parseFloat(item.current_stock.toString()),\n          unit_cost: parseFloat(item.unit_cost.toString())\n        }))\n\n        setRecipes(recipeData)\n        setInventoryItems(processedInventory)\n      } catch (error) {\n        console.error('Error loading data:', error)\n        // Fallback to mock data if database fails\n        const mockRecipes: Recipe[] = [\n          {\n            id: '1',\n            name: 'Strawberry Vanilla Blend',\n            code: 'SVB-001',\n            category: 'Fruit Flavors',\n            batch_size: 10.0,\n            unit_of_measure: 'liters',\n            estimated_cost: 185.50,\n            ingredients_count: 3,\n            version: 1,\n            is_active: true,\n            created_at: '2024-12-10T10:00:00Z'\n          }\n        ]\n        setRecipes(mockRecipes)\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    loadData()\n  }, [])\n\n  // Calculate total cost and update availability\n  useEffect(() => {\n    const cost = ingredients.reduce((sum, ing) => sum + ing.total_cost, 0)\n    setTotalCost(cost)\n\n    // Check ingredient availability\n    const availability: {[key: string]: boolean} = {}\n    ingredients.forEach(ing => {\n      const item = inventoryItems.find(inv => inv.id === ing.inventory_item_id)\n      availability[ing.id] = item ? item.current_stock >= ing.quantity : false\n    })\n    setAvailabilityCheck(availability)\n  }, [ingredients, inventoryItems])\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    })\n  }\n\n  const addIngredient = () => {\n    if (!selectedInventoryItem || ingredientQuantity <= 0) return\n\n    const inventoryItem = inventoryItems.find(item => item.id === selectedInventoryItem)\n    if (!inventoryItem) return\n\n    const totalCost = ingredientQuantity * inventoryItem.unit_cost\n    const newIngredient: RecipeIngredient = {\n      id: Date.now().toString(),\n      inventory_item_id: inventoryItem.id,\n      inventory_item_name: inventoryItem.name,\n      quantity: ingredientQuantity,\n      unit_of_measure: inventoryItem.unit_of_measure,\n      unit_cost: inventoryItem.unit_cost,\n      total_cost: totalCost,\n      percentage: 0, // Will be calculated after all ingredients are added\n      notes: ingredientNotes\n    }\n\n    setIngredients([...ingredients, newIngredient])\n    setSelectedInventoryItem('')\n    setIngredientQuantity(0)\n    setIngredientNotes('')\n  }\n\n  const removeIngredient = (id: string) => {\n    setIngredients(ingredients.filter(ing => ing.id !== id))\n  }\n\n  const validateRecipe = () => {\n    const errors: string[] = []\n\n    if (!newRecipe.name.trim()) errors.push('Recipe name is required')\n    if (!newRecipe.code.trim()) errors.push('Recipe code is required')\n    if (ingredients.length === 0) errors.push('At least one ingredient is required')\n    if (newRecipe.batch_size <= 0) errors.push('Batch size must be greater than 0')\n\n    // Check ingredient availability\n    const unavailableIngredients = ingredients.filter(ing => !availabilityCheck[ing.id])\n    if (unavailableIngredients.length > 0) {\n      errors.push(`Insufficient stock for: ${unavailableIngredients.map(ing => ing.inventory_item_name).join(', ')}`)\n    }\n\n    setValidationErrors(errors)\n    return errors.length === 0\n  }\n\n  const handleCreateRecipe = async () => {\n    if (!validateRecipe()) return\n\n    try {\n      // Calculate percentages\n      const totalQuantity = ingredients.reduce((sum, ing) => sum + ing.quantity, 0)\n      const ingredientsWithPercentages = ingredients.map(ing => ({\n        ...ing,\n        percentage: (ing.quantity / totalQuantity) * 100\n      }))\n\n      const recipeData = {\n        name: newRecipe.name,\n        code: newRecipe.code,\n        category: newRecipe.category,\n        batch_size: newRecipe.batch_size,\n        unit_of_measure: newRecipe.unit_of_measure,\n        instructions: newRecipe.instructions,\n        is_active: true,\n        version: 1\n      }\n\n      // In a real implementation, you would save to database here\n      const recipe: Recipe = {\n        id: (recipes.length + 1).toString(),\n        ...recipeData,\n        estimated_cost: totalCost,\n        ingredients_count: ingredients.length,\n        created_at: new Date().toISOString()\n      }\n\n      setRecipes([...recipes, recipe])\n      resetForm()\n      setShowAddModal(false)\n\n      alert(`Recipe \"${newRecipe.name}\" created successfully!\\nTotal Cost: $${totalCost.toFixed(2)}\\nIngredients: ${ingredients.length}`)\n    } catch (error) {\n      console.error('Error creating recipe:', error)\n      alert('Error creating recipe. Please try again.')\n    }\n  }\n\n  const resetForm = () => {\n    setCurrentStep(1)\n    setNewRecipe({\n      name: '',\n      code: '',\n      category: 'Fruit Flavors',\n      batch_size: 10,\n      unit_of_measure: 'liters',\n      instructions: '',\n      description: '',\n      shelf_life: 12,\n      storage_conditions: 'Cool, dry place'\n    })\n    setIngredients([])\n    setSchedule({\n      planned_date: '',\n      planned_time: '09:00',\n      estimated_duration: 4,\n      priority: 'normal',\n      assigned_to: '',\n      equipment_required: [],\n      special_instructions: ''\n    })\n    setValidationErrors([])\n  }\n\n  const nextStep = () => {\n    if (currentStep < 4) setCurrentStep(currentStep + 1)\n  }\n\n  const prevStep = () => {\n    if (currentStep > 1) setCurrentStep(currentStep - 1)\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return (\n    <MainLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"sm:flex sm:items-center sm:justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Recipe Management</h1>\n            <p className=\"mt-2 text-sm text-gray-700\">\n              Create and manage product formulations and recipes\n            </p>\n          </div>\n          <div className=\"mt-4 sm:mt-0\">\n            <button\n              type=\"button\"\n              onClick={() => setShowAddModal(true)}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n            >\n              <PlusIcon className=\"-ml-1 mr-2 h-5 w-5\" aria-hidden=\"true\" />\n              New Recipe\n            </button>\n          </div>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-3\">\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <ClipboardDocumentListIcon className=\"h-6 w-6 text-green-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Active Recipes</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {recipes.filter(r => r.is_active).length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <CurrencyDollarIcon className=\"h-6 w-6 text-green-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Avg. Cost per Batch</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      ${recipes.length > 0 ? (recipes.reduce((sum, r) => sum + r.estimated_cost, 0) / recipes.length).toFixed(2) : '0.00'}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"h-6 w-6 bg-green-100 rounded-full flex items-center justify-center\">\n                    <span className=\"text-xs font-medium text-green-600\">V</span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Versions</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {recipes.reduce((sum, r) => sum + r.version, 0)}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Recipes Table */}\n        <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n              All Recipes ({recipes.length})\n            </h3>\n            \n            {loading ? (\n              <div className=\"animate-pulse\">\n                {[...Array(4)].map((_, i) => (\n                  <div key={i} className=\"flex items-center space-x-4 py-4\">\n                    <div className=\"h-4 bg-gray-200 rounded w-1/4\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full divide-y divide-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Recipe\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Category\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Batch Size\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Cost\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Created\n                      </th>\n                      <th className=\"relative px-6 py-3\">\n                        <span className=\"sr-only\">Actions</span>\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {recipes.map((recipe) => (\n                      <tr key={recipe.id} className=\"hover:bg-gray-50\">\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div>\n                            <div className=\"text-sm font-medium text-gray-900\">{recipe.name}</div>\n                            <div className=\"text-sm text-gray-500\">\n                              {recipe.code} • v{recipe.version} • {recipe.ingredients_count} ingredients\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {recipe.category}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {recipe.batch_size} {recipe.unit_of_measure}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          ${recipe.estimated_cost.toFixed(2)}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                            recipe.is_active \n                              ? 'bg-green-100 text-green-800' \n                              : 'bg-gray-100 text-gray-800'\n                          }`}>\n                            {recipe.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                          {formatDate(recipe.created_at)}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                          <button className=\"text-green-600 hover:text-green-900 mr-3\">\n                            View\n                          </button>\n                          <button className=\"text-green-600 hover:text-green-900\">\n                            Edit\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Professional Recipe Creation Modal */}\n        <Modal\n          isOpen={showAddModal}\n          onClose={() => { resetForm(); setShowAddModal(false); }}\n          title={`Create New Recipe - Step ${currentStep} of 4`}\n          maxWidth=\"2xl\"\n        >\n          <div className=\"space-y-6\">\n            {/* Progress Steps */}\n            <div className=\"flex items-center justify-between\">\n              {[1, 2, 3, 4].map((step) => (\n                <div key={step} className=\"flex items-center\">\n                  <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${\n                    step <= currentStep\n                      ? 'bg-green-600 border-green-600 text-white'\n                      : 'border-gray-300 text-gray-500'\n                  }`}>\n                    {step < currentStep ? (\n                      <CheckCircleIcon className=\"w-5 h-5\" />\n                    ) : (\n                      <span className=\"text-sm font-medium\">{step}</span>\n                    )}\n                  </div>\n                  <div className=\"ml-2 text-sm font-medium text-gray-900\">\n                    {step === 1 && 'Basic Info'}\n                    {step === 2 && 'Ingredients'}\n                    {step === 3 && 'Schedule'}\n                    {step === 4 && 'Review'}\n                  </div>\n                  {step < 4 && (\n                    <div className={`ml-4 w-16 h-0.5 ${\n                      step < currentStep ? 'bg-green-600' : 'bg-gray-300'\n                    }`} />\n                  )}\n                </div>\n              ))}\n            </div>\n\n            {/* Validation Errors */}\n            {validationErrors.length > 0 && (\n              <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n                <div className=\"flex\">\n                  <ExclamationTriangleIcon className=\"h-5 w-5 text-red-400\" />\n                  <div className=\"ml-3\">\n                    <h3 className=\"text-sm font-medium text-red-800\">Please fix the following errors:</h3>\n                    <ul className=\"mt-2 text-sm text-red-700 list-disc list-inside\">\n                      {validationErrors.map((error, index) => (\n                        <li key={index}>{error}</li>\n                      ))}\n                    </ul>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Step Content */}\n            <div className=\"min-h-96\">\n              {currentStep === 1 && (\n                <div className=\"space-y-4\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">Basic Recipe Information</h3>\n                  <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Recipe Name *</label>\n                      <input\n                        type=\"text\"\n                        required\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newRecipe.name}\n                        onChange={(e) => setNewRecipe({ ...newRecipe, name: e.target.value })}\n                        placeholder=\"e.g., Premium Vanilla Extract\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Recipe Code *</label>\n                      <input\n                        type=\"text\"\n                        required\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newRecipe.code}\n                        onChange={(e) => setNewRecipe({ ...newRecipe, code: e.target.value.toUpperCase() })}\n                        placeholder=\"e.g., PVE-001\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Category</label>\n                      <select\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newRecipe.category}\n                        onChange={(e) => setNewRecipe({ ...newRecipe, category: e.target.value })}\n                      >\n                        <option value=\"Fruit Flavors\">Fruit Flavors</option>\n                        <option value=\"Vanilla Products\">Vanilla Products</option>\n                        <option value=\"Citrus Flavors\">Citrus Flavors</option>\n                        <option value=\"Dessert Flavors\">Dessert Flavors</option>\n                        <option value=\"Spice Flavors\">Spice Flavors</option>\n                        <option value=\"Herbal Extracts\">Herbal Extracts</option>\n                      </select>\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Unit of Measure</label>\n                      <select\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newRecipe.unit_of_measure}\n                        onChange={(e) => setNewRecipe({ ...newRecipe, unit_of_measure: e.target.value })}\n                      >\n                        <option value=\"liters\">Liters</option>\n                        <option value=\"kg\">Kilograms</option>\n                        <option value=\"gallons\">Gallons</option>\n                        <option value=\"pounds\">Pounds</option>\n                      </select>\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Standard Batch Size *</label>\n                      <input\n                        type=\"number\"\n                        min=\"0.1\"\n                        step=\"0.1\"\n                        required\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newRecipe.batch_size}\n                        onChange={(e) => setNewRecipe({ ...newRecipe, batch_size: parseFloat(e.target.value) || 0 })}\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Shelf Life (months)</label>\n                      <input\n                        type=\"number\"\n                        min=\"1\"\n                        max=\"60\"\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newRecipe.shelf_life}\n                        onChange={(e) => setNewRecipe({ ...newRecipe, shelf_life: parseInt(e.target.value) || 12 })}\n                      />\n                    </div>\n                    <div className=\"sm:col-span-2\">\n                      <label className=\"block text-sm font-medium text-gray-700\">Description</label>\n                      <textarea\n                        rows={2}\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newRecipe.description}\n                        onChange={(e) => setNewRecipe({ ...newRecipe, description: e.target.value })}\n                        placeholder=\"Brief description of the product...\"\n                      />\n                    </div>\n                    <div className=\"sm:col-span-2\">\n                      <label className=\"block text-sm font-medium text-gray-700\">Storage Conditions</label>\n                      <input\n                        type=\"text\"\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newRecipe.storage_conditions}\n                        onChange={(e) => setNewRecipe({ ...newRecipe, storage_conditions: e.target.value })}\n                        placeholder=\"e.g., Cool, dry place away from direct sunlight\"\n                      />\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {currentStep === 2 && (\n                <div className=\"space-y-4\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">Recipe Ingredients</h3>\n\n                  {/* Add Ingredient Form */}\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\n                    <h4 className=\"text-md font-medium text-gray-900 mb-3\">Add Ingredient</h4>\n                    <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-4\">\n                      <div className=\"sm:col-span-2\">\n                        <label className=\"block text-sm font-medium text-gray-700\">Inventory Item</label>\n                        <select\n                          className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                          value={selectedInventoryItem}\n                          onChange={(e) => setSelectedInventoryItem(e.target.value)}\n                        >\n                          <option value=\"\">Select an ingredient...</option>\n                          {inventoryItems.map(item => (\n                            <option key={item.id} value={item.id}>\n                              {item.name} ({item.current_stock} {item.unit_of_measure} available)\n                            </option>\n                          ))}\n                        </select>\n                      </div>\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700\">Quantity</label>\n                        <input\n                          type=\"number\"\n                          min=\"0.001\"\n                          step=\"0.001\"\n                          className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                          value={ingredientQuantity}\n                          onChange={(e) => setIngredientQuantity(parseFloat(e.target.value) || 0)}\n                        />\n                      </div>\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700\">Action</label>\n                        <button\n                          type=\"button\"\n                          onClick={addIngredient}\n                          disabled={!selectedInventoryItem || ingredientQuantity <= 0}\n                          className=\"mt-1 w-full px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400\"\n                        >\n                          Add\n                        </button>\n                      </div>\n                    </div>\n                    <div className=\"mt-3\">\n                      <label className=\"block text-sm font-medium text-gray-700\">Notes (optional)</label>\n                      <input\n                        type=\"text\"\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={ingredientNotes}\n                        onChange={(e) => setIngredientNotes(e.target.value)}\n                        placeholder=\"Special handling instructions...\"\n                      />\n                    </div>\n                  </div>\n\n                  {/* Ingredients List */}\n                  <div>\n                    <h4 className=\"text-md font-medium text-gray-900 mb-3\">Recipe Ingredients ({ingredients.length})</h4>\n                    {ingredients.length === 0 ? (\n                      <div className=\"text-center py-8 text-gray-500\">\n                        No ingredients added yet. Add ingredients from your inventory above.\n                      </div>\n                    ) : (\n                      <div className=\"space-y-2\">\n                        {ingredients.map((ingredient) => (\n                          <div key={ingredient.id} className=\"flex items-center justify-between p-3 bg-white border rounded-lg\">\n                            <div className=\"flex-1\">\n                              <div className=\"flex items-center space-x-4\">\n                                <div className=\"flex-1\">\n                                  <div className=\"font-medium text-gray-900\">{ingredient.inventory_item_name}</div>\n                                  <div className=\"text-sm text-gray-500\">\n                                    {ingredient.quantity} {ingredient.unit_of_measure} × ${ingredient.unit_cost.toFixed(2)} = ${ingredient.total_cost.toFixed(2)}\n                                  </div>\n                                  {ingredient.notes && (\n                                    <div className=\"text-xs text-gray-400 mt-1\">{ingredient.notes}</div>\n                                  )}\n                                </div>\n                                <div className=\"text-right\">\n                                  <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                                    availabilityCheck[ingredient.id]\n                                      ? 'bg-green-100 text-green-800'\n                                      : 'bg-red-100 text-red-800'\n                                  }`}>\n                                    {availabilityCheck[ingredient.id] ? 'Available' : 'Low Stock'}\n                                  </div>\n                                </div>\n                              </div>\n                            </div>\n                            <button\n                              type=\"button\"\n                              onClick={() => removeIngredient(ingredient.id)}\n                              className=\"ml-4 p-1 text-red-600 hover:text-red-800\"\n                            >\n                              <TrashIcon className=\"h-4 w-4\" />\n                            </button>\n                          </div>\n                        ))}\n                        <div className=\"mt-4 p-3 bg-green-50 rounded-lg\">\n                          <div className=\"text-sm font-medium text-green-800\">\n                            Total Recipe Cost: ${totalCost.toFixed(2)} ({ingredients.length} ingredients)\n                          </div>\n                          <div className=\"text-xs text-green-600 mt-1\">\n                            Cost per {newRecipe.unit_of_measure}: ${newRecipe.batch_size > 0 ? (totalCost / newRecipe.batch_size).toFixed(2) : '0.00'}\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {currentStep === 3 && (\n                <div className=\"space-y-4\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">Production Schedule</h3>\n                  <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Planned Production Date</label>\n                      <input\n                        type=\"date\"\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={schedule.planned_date}\n                        onChange={(e) => setSchedule({ ...schedule, planned_date: e.target.value })}\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Planned Start Time</label>\n                      <input\n                        type=\"time\"\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={schedule.planned_time}\n                        onChange={(e) => setSchedule({ ...schedule, planned_time: e.target.value })}\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Estimated Duration (hours)</label>\n                      <input\n                        type=\"number\"\n                        min=\"0.5\"\n                        step=\"0.5\"\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={schedule.estimated_duration}\n                        onChange={(e) => setSchedule({ ...schedule, estimated_duration: parseFloat(e.target.value) || 4 })}\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Priority</label>\n                      <select\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={schedule.priority}\n                        onChange={(e) => setSchedule({ ...schedule, priority: e.target.value as any })}\n                      >\n                        <option value=\"low\">Low</option>\n                        <option value=\"normal\">Normal</option>\n                        <option value=\"high\">High</option>\n                        <option value=\"urgent\">Urgent</option>\n                      </select>\n                    </div>\n                    <div className=\"sm:col-span-2\">\n                      <label className=\"block text-sm font-medium text-gray-700\">Assigned To</label>\n                      <select\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={schedule.assigned_to}\n                        onChange={(e) => setSchedule({ ...schedule, assigned_to: e.target.value })}\n                      >\n                        <option value=\"\">Select operator...</option>\n                        <option value=\"John Smith\">John Smith</option>\n                        <option value=\"Sarah Johnson\">Sarah Johnson</option>\n                        <option value=\"Mike Wilson\">Mike Wilson</option>\n                        <option value=\"Emily Davis\">Emily Davis</option>\n                      </select>\n                    </div>\n                    <div className=\"sm:col-span-2\">\n                      <label className=\"block text-sm font-medium text-gray-700\">Special Instructions</label>\n                      <textarea\n                        rows={3}\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={schedule.special_instructions}\n                        onChange={(e) => setSchedule({ ...schedule, special_instructions: e.target.value })}\n                        placeholder=\"Any special handling or production notes...\"\n                      />\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {currentStep === 4 && (\n                <div className=\"space-y-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">Review & Create Recipe</h3>\n\n                  {/* Recipe Summary */}\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\n                    <h4 className=\"font-medium text-gray-900 mb-3\">Recipe Summary</h4>\n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div><span className=\"font-medium\">Name:</span> {newRecipe.name}</div>\n                      <div><span className=\"font-medium\">Code:</span> {newRecipe.code}</div>\n                      <div><span className=\"font-medium\">Category:</span> {newRecipe.category}</div>\n                      <div><span className=\"font-medium\">Batch Size:</span> {newRecipe.batch_size} {newRecipe.unit_of_measure}</div>\n                      <div><span className=\"font-medium\">Shelf Life:</span> {newRecipe.shelf_life} months</div>\n                      <div><span className=\"font-medium\">Total Cost:</span> ${totalCost.toFixed(2)}</div>\n                    </div>\n                  </div>\n\n                  {/* Ingredients Summary */}\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\n                    <h4 className=\"font-medium text-gray-900 mb-3\">Ingredients ({ingredients.length})</h4>\n                    <div className=\"space-y-2\">\n                      {ingredients.map((ingredient) => (\n                        <div key={ingredient.id} className=\"flex justify-between text-sm\">\n                          <span>{ingredient.inventory_item_name}</span>\n                          <span>{ingredient.quantity} {ingredient.unit_of_measure} (${ingredient.total_cost.toFixed(2)})</span>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n\n                  {/* Schedule Summary */}\n                  {schedule.planned_date && (\n                    <div className=\"bg-gray-50 p-4 rounded-lg\">\n                      <h4 className=\"font-medium text-gray-900 mb-3\">Production Schedule</h4>\n                      <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                        <div><span className=\"font-medium\">Date:</span> {schedule.planned_date}</div>\n                        <div><span className=\"font-medium\">Time:</span> {schedule.planned_time}</div>\n                        <div><span className=\"font-medium\">Duration:</span> {schedule.estimated_duration} hours</div>\n                        <div><span className=\"font-medium\">Priority:</span> {schedule.priority}</div>\n                        {schedule.assigned_to && <div><span className=\"font-medium\">Assigned:</span> {schedule.assigned_to}</div>}\n                      </div>\n                    </div>\n                  )}\n                </div>\n              )}\n            </div>\n\n            {/* Navigation Buttons */}\n            <div className=\"flex justify-between pt-6 border-t border-gray-200\">\n              <button\n                type=\"button\"\n                onClick={currentStep === 1 ? () => { resetForm(); setShowAddModal(false); } : prevStep}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n              >\n                {currentStep === 1 ? 'Cancel' : 'Previous'}\n              </button>\n\n              <div className=\"flex space-x-3\">\n                {currentStep < 4 ? (\n                  <button\n                    type=\"button\"\n                    onClick={nextStep}\n                    className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700\"\n                  >\n                    Next Step\n                  </button>\n                ) : (\n                  <button\n                    type=\"button\"\n                    onClick={handleCreateRecipe}\n                    className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700\"\n                  >\n                    Create Recipe\n                  </button>\n                )}\n              </div>\n            </div>\n          </div>\n        </Modal>\n      </div>\n    </MainLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAiEe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,2BAA2B;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,MAAM;QACN,MAAM;QACN,UAAU;QACV,YAAY;QACZ,iBAAiB;QACjB,cAAc;QACd,aAAa;QACb,YAAY;QACZ,oBAAoB;IACtB;IAEA,qBAAqB;IACrB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACrE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,sBAAsB;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;QAC3D,cAAc;QACd,cAAc;QACd,oBAAoB;QACpB,UAAU;QACV,aAAa;QACb,oBAAoB,EAAE;QACtB,sBAAsB;IACxB;IAEA,kCAAkC;IAClC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B,CAAC;IACtF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAErE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,IAAI;gBACF,WAAW;gBAEX,iDAAiD;gBACjD,MAAM,CAAC,YAAY,cAAc,GAAG,MAAM,QAAQ,GAAG,CAAC;oBACpD,sHAAA,CAAA,kBAAe,CAAC,UAAU;oBAC1B,sHAAA,CAAA,kBAAe,CAAC,iBAAiB;iBAClC;gBAED,yCAAyC;gBACzC,MAAM,qBAAqB,cAAc,GAAG,CAAC,CAAA,OAAQ,CAAC;wBACpD,GAAG,IAAI;wBACP,eAAe,WAAW,KAAK,aAAa,CAAC,QAAQ;wBACrD,WAAW,WAAW,KAAK,SAAS,CAAC,QAAQ;oBAC/C,CAAC;gBAED,WAAW;gBACX,kBAAkB;YACpB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,0CAA0C;gBAC1C,MAAM,cAAwB;oBAC5B;wBACE,IAAI;wBACJ,MAAM;wBACN,MAAM;wBACN,UAAU;wBACV,YAAY;wBACZ,iBAAiB;wBACjB,gBAAgB;wBAChB,mBAAmB;wBACnB,SAAS;wBACT,WAAW;wBACX,YAAY;oBACd;iBACD;gBACD,WAAW;YACb,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,OAAO,YAAY,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,UAAU,EAAE;QACpE,aAAa;QAEb,gCAAgC;QAChC,MAAM,eAAyC,CAAC;QAChD,YAAY,OAAO,CAAC,CAAA;YAClB,MAAM,OAAO,eAAe,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,IAAI,iBAAiB;YACxE,YAAY,CAAC,IAAI,EAAE,CAAC,GAAG,OAAO,KAAK,aAAa,IAAI,IAAI,QAAQ,GAAG;QACrE;QACA,qBAAqB;IACvB,GAAG;QAAC;QAAa;KAAe;IAEhC,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,yBAAyB,sBAAsB,GAAG;QAEvD,MAAM,gBAAgB,eAAe,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC9D,IAAI,CAAC,eAAe;QAEpB,MAAM,YAAY,qBAAqB,cAAc,SAAS;QAC9D,MAAM,gBAAkC;YACtC,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,mBAAmB,cAAc,EAAE;YACnC,qBAAqB,cAAc,IAAI;YACvC,UAAU;YACV,iBAAiB,cAAc,eAAe;YAC9C,WAAW,cAAc,SAAS;YAClC,YAAY;YACZ,YAAY;YACZ,OAAO;QACT;QAEA,eAAe;eAAI;YAAa;SAAc;QAC9C,yBAAyB;QACzB,sBAAsB;QACtB,mBAAmB;IACrB;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe,YAAY,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;IACtD;IAEA,MAAM,iBAAiB;QACrB,MAAM,SAAmB,EAAE;QAE3B,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC;QACxC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC;QACxC,IAAI,YAAY,MAAM,KAAK,GAAG,OAAO,IAAI,CAAC;QAC1C,IAAI,UAAU,UAAU,IAAI,GAAG,OAAO,IAAI,CAAC;QAE3C,gCAAgC;QAChC,MAAM,yBAAyB,YAAY,MAAM,CAAC,CAAA,MAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;QACnF,IAAI,uBAAuB,MAAM,GAAG,GAAG;YACrC,OAAO,IAAI,CAAC,CAAC,wBAAwB,EAAE,uBAAuB,GAAG,CAAC,CAAA,MAAO,IAAI,mBAAmB,EAAE,IAAI,CAAC,OAAO;QAChH;QAEA,oBAAoB;QACpB,OAAO,OAAO,MAAM,KAAK;IAC3B;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,kBAAkB;QAEvB,IAAI;YACF,wBAAwB;YACxB,MAAM,gBAAgB,YAAY,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,QAAQ,EAAE;YAC3E,MAAM,6BAA6B,YAAY,GAAG,CAAC,CAAA,MAAO,CAAC;oBACzD,GAAG,GAAG;oBACN,YAAY,AAAC,IAAI,QAAQ,GAAG,gBAAiB;gBAC/C,CAAC;YAED,MAAM,aAAa;gBACjB,MAAM,UAAU,IAAI;gBACpB,MAAM,UAAU,IAAI;gBACpB,UAAU,UAAU,QAAQ;gBAC5B,YAAY,UAAU,UAAU;gBAChC,iBAAiB,UAAU,eAAe;gBAC1C,cAAc,UAAU,YAAY;gBACpC,WAAW;gBACX,SAAS;YACX;YAEA,4DAA4D;YAC5D,MAAM,SAAiB;gBACrB,IAAI,CAAC,QAAQ,MAAM,GAAG,CAAC,EAAE,QAAQ;gBACjC,GAAG,UAAU;gBACb,gBAAgB;gBAChB,mBAAmB,YAAY,MAAM;gBACrC,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,WAAW;mBAAI;gBAAS;aAAO;YAC/B;YACA,gBAAgB;YAEhB,MAAM,CAAC,QAAQ,EAAE,UAAU,IAAI,CAAC,sCAAsC,EAAE,UAAU,OAAO,CAAC,GAAG,eAAe,EAAE,YAAY,MAAM,EAAE;QACpI,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,MAAM,YAAY;QAChB,eAAe;QACf,aAAa;YACX,MAAM;YACN,MAAM;YACN,UAAU;YACV,YAAY;YACZ,iBAAiB;YACjB,cAAc;YACd,aAAa;YACb,YAAY;YACZ,oBAAoB;QACtB;QACA,eAAe,EAAE;QACjB,YAAY;YACV,cAAc;YACd,cAAc;YACd,oBAAoB;YACpB,UAAU;YACV,aAAa;YACb,oBAAoB,EAAE;YACtB,sBAAsB;QACxB;QACA,oBAAoB,EAAE;IACxB;IAEA,MAAM,WAAW;QACf,IAAI,cAAc,GAAG,eAAe,cAAc;IACpD;IAEA,MAAM,WAAW;QACf,IAAI,cAAc,GAAG,eAAe,cAAc;IACpD;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC,8IAAA,CAAA,aAAU;kBACT,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;;kDAEV,8OAAC,+MAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAqB,eAAY;;;;;;oCAAS;;;;;;;;;;;;;;;;;;8BAOpE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,iPAAA,CAAA,4BAAyB;gDAAC,WAAU;gDAAyB,eAAY;;;;;;;;;;;sDAE5E,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEACX,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQpD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,mOAAA,CAAA,qBAAkB;gDAAC,WAAU;gDAAyB,eAAY;;;;;;;;;;;sDAErE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;;4DAAoC;4DAC9C,QAAQ,MAAM,GAAG,IAAI,CAAC,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,cAAc,EAAE,KAAK,QAAQ,MAAM,EAAE,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQzH,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAqC;;;;;;;;;;;;;;;;sDAGzD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEACX,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAU3D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAmD;oCACjD,QAAQ,MAAM;oCAAC;;;;;;;4BAG9B,wBACC,8OAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;wCAAY,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;uCALP;;;;;;;;;qDAUd,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;sDAIhC,8OAAC;4CAAM,WAAU;sDACd,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;oDAAmB,WAAU;;sEAC5B,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;kFAAqC,OAAO,IAAI;;;;;;kFAC/D,8OAAC;wEAAI,WAAU;;4EACZ,OAAO,IAAI;4EAAC;4EAAK,OAAO,OAAO;4EAAC;4EAAI,OAAO,iBAAiB;4EAAC;;;;;;;;;;;;;;;;;;sEAIpE,8OAAC;4DAAG,WAAU;sEACX,OAAO,QAAQ;;;;;;sEAElB,8OAAC;4DAAG,WAAU;;gEACX,OAAO,UAAU;gEAAC;gEAAE,OAAO,eAAe;;;;;;;sEAE7C,8OAAC;4DAAG,WAAU;;gEAAoD;gEAC9D,OAAO,cAAc,CAAC,OAAO,CAAC;;;;;;;sEAElC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAW,CAAC,wEAAwE,EACxF,OAAO,SAAS,GACZ,gCACA,6BACJ;0EACC,OAAO,SAAS,GAAG,WAAW;;;;;;;;;;;sEAGnC,8OAAC;4DAAG,WAAU;sEACX,WAAW,OAAO,UAAU;;;;;;sEAE/B,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAO,WAAU;8EAA2C;;;;;;8EAG7D,8OAAC;oEAAO,WAAU;8EAAsC;;;;;;;;;;;;;mDAlCnD,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAgDhC,8OAAC,iIAAA,CAAA,QAAK;oBACJ,QAAQ;oBACR,SAAS;wBAAQ;wBAAa,gBAAgB;oBAAQ;oBACtD,OAAO,CAAC,yBAAyB,EAAE,YAAY,KAAK,CAAC;oBACrD,UAAS;8BAET,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAG;oCAAG;oCAAG;iCAAE,CAAC,GAAG,CAAC,CAAC,qBACjB,8OAAC;wCAAe,WAAU;;0DACxB,8OAAC;gDAAI,WAAW,CAAC,+DAA+D,EAC9E,QAAQ,cACJ,6CACA,iCACJ;0DACC,OAAO,4BACN,8OAAC,6NAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;yEAE3B,8OAAC;oDAAK,WAAU;8DAAuB;;;;;;;;;;;0DAG3C,8OAAC;gDAAI,WAAU;;oDACZ,SAAS,KAAK;oDACd,SAAS,KAAK;oDACd,SAAS,KAAK;oDACd,SAAS,KAAK;;;;;;;4CAEhB,OAAO,mBACN,8OAAC;gDAAI,WAAW,CAAC,gBAAgB,EAC/B,OAAO,cAAc,iBAAiB,eACtC;;;;;;;uCArBI;;;;;;;;;;4BA4Bb,iBAAiB,MAAM,GAAG,mBACzB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6OAAA,CAAA,0BAAuB;4CAAC,WAAU;;;;;;sDACnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAG,WAAU;8DACX,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,8OAAC;sEAAgB;2DAAR;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASrB,8OAAC;gCAAI,WAAU;;oCACZ,gBAAgB,mBACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,8OAAC;gEACC,MAAK;gEACL,QAAQ;gEACR,WAAU;gEACV,OAAO,UAAU,IAAI;gEACrB,UAAU,CAAC,IAAM,aAAa;wEAAE,GAAG,SAAS;wEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACnE,aAAY;;;;;;;;;;;;kEAGhB,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,8OAAC;gEACC,MAAK;gEACL,QAAQ;gEACR,WAAU;gEACV,OAAO,UAAU,IAAI;gEACrB,UAAU,CAAC,IAAM,aAAa;wEAAE,GAAG,SAAS;wEAAE,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW;oEAAG;gEACjF,aAAY;;;;;;;;;;;;kEAGhB,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,8OAAC;gEACC,WAAU;gEACV,OAAO,UAAU,QAAQ;gEACzB,UAAU,CAAC,IAAM,aAAa;wEAAE,GAAG,SAAS;wEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oEAAC;;kFAEvE,8OAAC;wEAAO,OAAM;kFAAgB;;;;;;kFAC9B,8OAAC;wEAAO,OAAM;kFAAmB;;;;;;kFACjC,8OAAC;wEAAO,OAAM;kFAAiB;;;;;;kFAC/B,8OAAC;wEAAO,OAAM;kFAAkB;;;;;;kFAChC,8OAAC;wEAAO,OAAM;kFAAgB;;;;;;kFAC9B,8OAAC;wEAAO,OAAM;kFAAkB;;;;;;;;;;;;;;;;;;kEAGpC,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,8OAAC;gEACC,WAAU;gEACV,OAAO,UAAU,eAAe;gEAChC,UAAU,CAAC,IAAM,aAAa;wEAAE,GAAG,SAAS;wEAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;oEAAC;;kFAE9E,8OAAC;wEAAO,OAAM;kFAAS;;;;;;kFACvB,8OAAC;wEAAO,OAAM;kFAAK;;;;;;kFACnB,8OAAC;wEAAO,OAAM;kFAAU;;;;;;kFACxB,8OAAC;wEAAO,OAAM;kFAAS;;;;;;;;;;;;;;;;;;kEAG3B,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,MAAK;gEACL,QAAQ;gEACR,WAAU;gEACV,OAAO,UAAU,UAAU;gEAC3B,UAAU,CAAC,IAAM,aAAa;wEAAE,GAAG,SAAS;wEAAE,YAAY,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oEAAE;;;;;;;;;;;;kEAG9F,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACJ,WAAU;gEACV,OAAO,UAAU,UAAU;gEAC3B,UAAU,CAAC,IAAM,aAAa;wEAAE,GAAG,SAAS;wEAAE,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;oEAAG;;;;;;;;;;;;kEAG7F,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,8OAAC;gEACC,MAAM;gEACN,WAAU;gEACV,OAAO,UAAU,WAAW;gEAC5B,UAAU,CAAC,IAAM,aAAa;wEAAE,GAAG,SAAS;wEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oEAAC;gEAC1E,aAAY;;;;;;;;;;;;kEAGhB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,OAAO,UAAU,kBAAkB;gEACnC,UAAU,CAAC,IAAM,aAAa;wEAAE,GAAG,SAAS;wEAAE,oBAAoB,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACjF,aAAY;;;;;;;;;;;;;;;;;;;;;;;;oCAOrB,gBAAgB,mBACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAGlD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAM,WAAU;kFAA0C;;;;;;kFAC3D,8OAAC;wEACC,WAAU;wEACV,OAAO;wEACP,UAAU,CAAC,IAAM,yBAAyB,EAAE,MAAM,CAAC,KAAK;;0FAExD,8OAAC;gFAAO,OAAM;0FAAG;;;;;;4EAChB,eAAe,GAAG,CAAC,CAAA,qBAClB,8OAAC;oFAAqB,OAAO,KAAK,EAAE;;wFACjC,KAAK,IAAI;wFAAC;wFAAG,KAAK,aAAa;wFAAC;wFAAE,KAAK,eAAe;wFAAC;;mFAD7C,KAAK,EAAE;;;;;;;;;;;;;;;;;0EAM1B,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA0C;;;;;;kFAC3D,8OAAC;wEACC,MAAK;wEACL,KAAI;wEACJ,MAAK;wEACL,WAAU;wEACV,OAAO;wEACP,UAAU,CAAC,IAAM,sBAAsB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;;0EAGzE,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA0C;;;;;;kFAC3D,8OAAC;wEACC,MAAK;wEACL,SAAS;wEACT,UAAU,CAAC,yBAAyB,sBAAsB;wEAC1D,WAAU;kFACX;;;;;;;;;;;;;;;;;;kEAKL,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,OAAO;gEACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gEAClD,aAAY;;;;;;;;;;;;;;;;;;0DAMlB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;;4DAAyC;4DAAqB,YAAY,MAAM;4DAAC;;;;;;;oDAC9F,YAAY,MAAM,KAAK,kBACtB,8OAAC;wDAAI,WAAU;kEAAiC;;;;;6EAIhD,8OAAC;wDAAI,WAAU;;4DACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC;oEAAwB,WAAU;;sFACjC,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAI,WAAU;;0GACb,8OAAC;gGAAI,WAAU;0GAA6B,WAAW,mBAAmB;;;;;;0GAC1E,8OAAC;gGAAI,WAAU;;oGACZ,WAAW,QAAQ;oGAAC;oGAAE,WAAW,eAAe;oGAAC;oGAAK,WAAW,SAAS,CAAC,OAAO,CAAC;oGAAG;oGAAK,WAAW,UAAU,CAAC,OAAO,CAAC;;;;;;;4FAE3H,WAAW,KAAK,kBACf,8OAAC;gGAAI,WAAU;0GAA8B,WAAW,KAAK;;;;;;;;;;;;kGAGjE,8OAAC;wFAAI,WAAU;kGACb,cAAA,8OAAC;4FAAI,WAAW,CAAC,oEAAoE,EACnF,iBAAiB,CAAC,WAAW,EAAE,CAAC,GAC5B,gCACA,2BACJ;sGACC,iBAAiB,CAAC,WAAW,EAAE,CAAC,GAAG,cAAc;;;;;;;;;;;;;;;;;;;;;;sFAK1D,8OAAC;4EACC,MAAK;4EACL,SAAS,IAAM,iBAAiB,WAAW,EAAE;4EAC7C,WAAU;sFAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;gFAAC,WAAU;;;;;;;;;;;;mEA5Bf,WAAW,EAAE;;;;;0EAgCzB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;4EAAqC;4EAC7B,UAAU,OAAO,CAAC;4EAAG;4EAAG,YAAY,MAAM;4EAAC;;;;;;;kFAElE,8OAAC;wEAAI,WAAU;;4EAA8B;4EACjC,UAAU,eAAe;4EAAC;4EAAI,UAAU,UAAU,GAAG,IAAI,CAAC,YAAY,UAAU,UAAU,EAAE,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAShI,gBAAgB,mBACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,OAAO,SAAS,YAAY;gEAC5B,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,cAAc,EAAE,MAAM,CAAC,KAAK;oEAAC;;;;;;;;;;;;kEAG7E,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,OAAO,SAAS,YAAY;gEAC5B,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,cAAc,EAAE,MAAM,CAAC,KAAK;oEAAC;;;;;;;;;;;;kEAG7E,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,MAAK;gEACL,WAAU;gEACV,OAAO,SAAS,kBAAkB;gEAClC,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,oBAAoB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oEAAE;;;;;;;;;;;;kEAGpG,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,8OAAC;gEACC,WAAU;gEACV,OAAO,SAAS,QAAQ;gEACxB,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oEAAQ;;kFAE5E,8OAAC;wEAAO,OAAM;kFAAM;;;;;;kFACpB,8OAAC;wEAAO,OAAM;kFAAS;;;;;;kFACvB,8OAAC;wEAAO,OAAM;kFAAO;;;;;;kFACrB,8OAAC;wEAAO,OAAM;kFAAS;;;;;;;;;;;;;;;;;;kEAG3B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,8OAAC;gEACC,WAAU;gEACV,OAAO,SAAS,WAAW;gEAC3B,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oEAAC;;kFAExE,8OAAC;wEAAO,OAAM;kFAAG;;;;;;kFACjB,8OAAC;wEAAO,OAAM;kFAAa;;;;;;kFAC3B,8OAAC;wEAAO,OAAM;kFAAgB;;;;;;kFAC9B,8OAAC;wEAAO,OAAM;kFAAc;;;;;;kFAC5B,8OAAC;wEAAO,OAAM;kFAAc;;;;;;;;;;;;;;;;;;kEAGhC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,8OAAC;gEACC,MAAM;gEACN,WAAU;gEACV,OAAO,SAAS,oBAAoB;gEACpC,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,sBAAsB,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACjF,aAAY;;;;;;;;;;;;;;;;;;;;;;;;oCAOrB,gBAAgB,mBACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAGlD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFAAI,8OAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAY;oEAAE,UAAU,IAAI;;;;;;;0EAC/D,8OAAC;;kFAAI,8OAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAY;oEAAE,UAAU,IAAI;;;;;;;0EAC/D,8OAAC;;kFAAI,8OAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAgB;oEAAE,UAAU,QAAQ;;;;;;;0EACvE,8OAAC;;kFAAI,8OAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAkB;oEAAE,UAAU,UAAU;oEAAC;oEAAE,UAAU,eAAe;;;;;;;0EACvG,8OAAC;;kFAAI,8OAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAkB;oEAAE,UAAU,UAAU;oEAAC;;;;;;;0EAC5E,8OAAC;;kFAAI,8OAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAkB;oEAAG,UAAU,OAAO,CAAC;;;;;;;;;;;;;;;;;;;0DAK9E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;;4DAAiC;4DAAc,YAAY,MAAM;4DAAC;;;;;;;kEAChF,8OAAC;wDAAI,WAAU;kEACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC;gEAAwB,WAAU;;kFACjC,8OAAC;kFAAM,WAAW,mBAAmB;;;;;;kFACrC,8OAAC;;4EAAM,WAAW,QAAQ;4EAAC;4EAAE,WAAW,eAAe;4EAAC;4EAAI,WAAW,UAAU,CAAC,OAAO,CAAC;4EAAG;;;;;;;;+DAFrF,WAAW,EAAE;;;;;;;;;;;;;;;;4CAS5B,SAAS,YAAY,kBACpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFAAI,8OAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAY;oEAAE,SAAS,YAAY;;;;;;;0EACtE,8OAAC;;kFAAI,8OAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAY;oEAAE,SAAS,YAAY;;;;;;;0EACtE,8OAAC;;kFAAI,8OAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAgB;oEAAE,SAAS,kBAAkB;oEAAC;;;;;;;0EACjF,8OAAC;;kFAAI,8OAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAgB;oEAAE,SAAS,QAAQ;;;;;;;4DACrE,SAAS,WAAW,kBAAI,8OAAC;;kFAAI,8OAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAgB;oEAAE,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAS9G,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS,gBAAgB,IAAI;4CAAQ;4CAAa,gBAAgB;wCAAQ,IAAI;wCAC9E,WAAU;kDAET,gBAAgB,IAAI,WAAW;;;;;;kDAGlC,8OAAC;wCAAI,WAAU;kDACZ,cAAc,kBACb,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;iEAID,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}]}