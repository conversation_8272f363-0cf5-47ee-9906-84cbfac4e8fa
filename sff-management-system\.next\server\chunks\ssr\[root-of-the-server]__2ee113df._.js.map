{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/providers.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\n\ninterface User {\n  id: string\n  username: string\n  email: string\n  role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n  full_name?: string\n  department?: string\n}\n\ninterface AuthContextType {\n  user: User | null\n  loading: boolean\n  signIn: (username: string, password: string) => Promise<{ success: boolean; error?: string }>\n  signOut: () => void\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\nexport function Providers({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Check for stored user session\n    const checkSession = () => {\n      try {\n        if (typeof window !== 'undefined') {\n          const storedUser = localStorage.getItem('sff_user')\n          if (storedUser) {\n            const userData = JSON.parse(storedUser)\n            setUser(userData)\n          }\n        }\n      } catch (error) {\n        if (typeof window !== 'undefined') {\n          localStorage.removeItem('sff_user')\n        }\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    checkSession()\n  }, [])\n\n  const signIn = async (username: string, password: string) => {\n    // Demo users\n    const demoUsers = [\n      { username: 'admin', password: 'admin123', role: 'admin', full_name: 'System Administrator', department: 'IT', email: '<EMAIL>' },\n      { username: 'quality', password: 'quality123', role: 'quality_manager', full_name: 'Sarah Johnson', department: 'Quality Assurance', email: '<EMAIL>' },\n      { username: 'production', password: 'production123', role: 'production_manager', full_name: 'Mike Wilson', department: 'Production', email: '<EMAIL>' },\n      { username: 'employee', password: 'employee123', role: 'employee', full_name: 'Emily Davis', department: 'Production', email: '<EMAIL>' }\n    ]\n\n    const cleanUsername = username.trim().toLowerCase()\n    const cleanPassword = password.trim()\n\n    const demoUser = demoUsers.find(u =>\n      u.username.toLowerCase() === cleanUsername && u.password === cleanPassword\n    )\n\n    if (!demoUser) {\n      return { success: false, error: 'Invalid username or password' }\n    }\n\n    const userData = {\n      id: `demo_${demoUser.username}`,\n      username: demoUser.username,\n      email: demoUser.email,\n      role: demoUser.role as any,\n      full_name: demoUser.full_name,\n      department: demoUser.department\n    }\n\n    setUser(userData)\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('sff_user', JSON.stringify(userData))\n    }\n\n    return { success: true }\n  }\n\n  const signOut = () => {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('sff_user')\n      setUser(null)\n      window.location.href = '/login'\n    }\n  }\n\n  const value = {\n    user,\n    loading,\n    signIn,\n    signOut,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAoBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS,UAAU,EAAE,QAAQ,EAAiC;IACnE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gCAAgC;QAChC,MAAM,eAAe;YACnB,IAAI;gBACF,uCAAmC;;gBAMnC;YACF,EAAE,OAAO,OAAO;gBACd,uCAAmC;;gBAEnC;YACF,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,SAAS,OAAO,UAAkB;QACtC,aAAa;QACb,MAAM,YAAY;YAChB;gBAAE,UAAU;gBAAS,UAAU;gBAAY,MAAM;gBAAS,WAAW;gBAAwB,YAAY;gBAAM,OAAO;YAAgB;YACtI;gBAAE,UAAU;gBAAW,UAAU;gBAAc,MAAM;gBAAmB,WAAW;gBAAiB,YAAY;gBAAqB,OAAO;YAAkB;YAC9J;gBAAE,UAAU;gBAAc,UAAU;gBAAiB,MAAM;gBAAsB,WAAW;gBAAe,YAAY;gBAAc,OAAO;YAAqB;YACjK;gBAAE,UAAU;gBAAY,UAAU;gBAAe,MAAM;gBAAY,WAAW;gBAAe,YAAY;gBAAc,OAAO;YAAmB;SAClJ;QAED,MAAM,gBAAgB,SAAS,IAAI,GAAG,WAAW;QACjD,MAAM,gBAAgB,SAAS,IAAI;QAEnC,MAAM,WAAW,UAAU,IAAI,CAAC,CAAA,IAC9B,EAAE,QAAQ,CAAC,WAAW,OAAO,iBAAiB,EAAE,QAAQ,KAAK;QAG/D,IAAI,CAAC,UAAU;YACb,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA+B;QACjE;QAEA,MAAM,WAAW;YACf,IAAI,CAAC,KAAK,EAAE,SAAS,QAAQ,EAAE;YAC/B,UAAU,SAAS,QAAQ;YAC3B,OAAO,SAAS,KAAK;YACrB,MAAM,SAAS,IAAI;YACnB,WAAW,SAAS,SAAS;YAC7B,YAAY,SAAS,UAAU;QACjC;QAEA,QAAQ;QACR,uCAAmC;;QAEnC;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA,MAAM,UAAU;QACd,uCAAmC;;QAInC;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 161, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}