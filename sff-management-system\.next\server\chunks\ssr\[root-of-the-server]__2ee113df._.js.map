{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/providers.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\n\ninterface User {\n  id: string\n  username: string\n  email: string\n  role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n}\n\ninterface AuthContextType {\n  user: User | null\n  loading: boolean\n  signOut: () => void\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\nexport function Providers({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Check for stored user session\n    const checkSession = () => {\n      try {\n        if (typeof window !== 'undefined') {\n          const storedUser = localStorage.getItem('sff_user')\n          if (storedUser) {\n            setUser(JSON.parse(storedUser))\n          }\n        }\n      } catch (error) {\n        console.error('Error parsing stored user:', error)\n        if (typeof window !== 'undefined') {\n          localStorage.removeItem('sff_user')\n        }\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    checkSession()\n\n    // Listen for storage changes (for multi-tab support)\n    const handleStorageChange = (e: StorageEvent) => {\n      if (e.key === 'sff_user') {\n        if (e.newValue) {\n          try {\n            setUser(JSON.parse(e.newValue))\n          } catch (error) {\n            console.error('Error parsing storage change:', error)\n            setUser(null)\n          }\n        } else {\n          setUser(null)\n        }\n      }\n    }\n\n    if (typeof window !== 'undefined') {\n      window.addEventListener('storage', handleStorageChange)\n      return () => window.removeEventListener('storage', handleStorageChange)\n    }\n  }, [])\n\n  const signOut = () => {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('sff_user')\n      setUser(null)\n      window.location.href = '/login'\n    }\n  }\n\n  const value = {\n    user,\n    loading,\n    signOut,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAiBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS,UAAU,EAAE,QAAQ,EAAiC;IACnE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gCAAgC;QAChC,MAAM,eAAe;YACnB,IAAI;gBACF,uCAAmC;;gBAKnC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,uCAAmC;;gBAEnC;YACF,SAAU;gBACR,WAAW;YACb;QACF;QAEA;QAEA,qDAAqD;QACrD,MAAM,sBAAsB,CAAC;YAC3B,IAAI,EAAE,GAAG,KAAK,YAAY;gBACxB,IAAI,EAAE,QAAQ,EAAE;oBACd,IAAI;wBACF,QAAQ,KAAK,KAAK,CAAC,EAAE,QAAQ;oBAC/B,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,iCAAiC;wBAC/C,QAAQ;oBACV;gBACF,OAAO;oBACL,QAAQ;gBACV;YACF;QACF;QAEA,uCAAmC;;QAGnC;IACF,GAAG,EAAE;IAEL,MAAM,UAAU;QACd,uCAAmC;;QAInC;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}