'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { useAuth } from '@/components/providers'
import { Modal } from '@/components/ui/modal'
import { PlusIcon, ClipboardDocumentListIcon, CurrencyDollarIcon } from '@heroicons/react/24/outline'

interface Recipe {
  id: string
  name: string
  code: string
  category: string
  batch_size: number
  unit_of_measure: string
  estimated_cost: number
  ingredients_count: number
  version: number
  is_active: boolean
  created_at: string
}

export default function RecipesPage() {
  const { user } = useAuth()
  const [recipes, setRecipes] = useState<Recipe[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [newRecipe, setNewRecipe] = useState({
    name: '',
    code: '',
    category: 'Fruit Flavors',
    batch_size: 10,
    unit_of_measure: 'liters',
    instructions: ''
  })

  useEffect(() => {
    // Mock recipes data
    const mockRecipes: Recipe[] = [
      {
        id: '1',
        name: 'Strawberry Vanilla Blend',
        code: 'SVB-001',
        category: 'Fruit Flavors',
        batch_size: 10.0,
        unit_of_measure: 'liters',
        estimated_cost: 185.50,
        ingredients_count: 3,
        version: 1,
        is_active: true,
        created_at: '2024-12-10T10:00:00Z'
      },
      {
        id: '2',
        name: 'Classic Vanilla Extract',
        code: 'CVE-001',
        category: 'Vanilla Products',
        batch_size: 5.0,
        unit_of_measure: 'liters',
        estimated_cost: 117.00,
        ingredients_count: 2,
        version: 1,
        is_active: true,
        created_at: '2024-12-10T11:00:00Z'
      },
      {
        id: '3',
        name: 'Citrus Burst Flavor',
        code: 'CBF-001',
        category: 'Citrus Flavors',
        batch_size: 8.0,
        unit_of_measure: 'liters',
        estimated_cost: 156.80,
        ingredients_count: 4,
        version: 2,
        is_active: true,
        created_at: '2024-12-09T14:30:00Z'
      },
      {
        id: '4',
        name: 'Chocolate Essence',
        code: 'CHE-001',
        category: 'Dessert Flavors',
        batch_size: 6.0,
        unit_of_measure: 'liters',
        estimated_cost: 198.60,
        ingredients_count: 5,
        version: 1,
        is_active: false,
        created_at: '2024-12-08T09:15:00Z'
      }
    ]

    setTimeout(() => {
      setRecipes(mockRecipes)
      setLoading(false)
    }, 1000)
  }, [])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const handleAddRecipe = () => {
    const recipe: Recipe = {
      id: (recipes.length + 1).toString(),
      name: newRecipe.name,
      code: newRecipe.code,
      category: newRecipe.category,
      batch_size: newRecipe.batch_size,
      unit_of_measure: newRecipe.unit_of_measure,
      estimated_cost: Math.random() * 200 + 50, // Mock cost calculation
      ingredients_count: Math.floor(Math.random() * 5) + 2,
      version: 1,
      is_active: true,
      created_at: new Date().toISOString()
    }

    setRecipes([...recipes, recipe])
    setShowAddModal(false)
    setNewRecipe({
      name: '',
      code: '',
      category: 'Fruit Flavors',
      batch_size: 10,
      unit_of_measure: 'liters',
      instructions: ''
    })
  }

  if (!user) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Recipe Management</h1>
            <p className="mt-2 text-sm text-gray-700">
              Create and manage product formulations and recipes
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button
              type="button"
              onClick={() => setShowAddModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
              New Recipe
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-3">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ClipboardDocumentListIcon className="h-6 w-6 text-green-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Active Recipes</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {recipes.filter(r => r.is_active).length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CurrencyDollarIcon className="h-6 w-6 text-green-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Avg. Cost per Batch</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      ${recipes.length > 0 ? (recipes.reduce((sum, r) => sum + r.estimated_cost, 0) / recipes.length).toFixed(2) : '0.00'}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-6 w-6 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-xs font-medium text-green-600">V</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Versions</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {recipes.reduce((sum, r) => sum + r.version, 0)}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recipes Table */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              All Recipes ({recipes.length})
            </h3>
            
            {loading ? (
              <div className="animate-pulse">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-4 py-4">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Recipe
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Category
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Batch Size
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Cost
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created
                      </th>
                      <th className="relative px-6 py-3">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {recipes.map((recipe) => (
                      <tr key={recipe.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{recipe.name}</div>
                            <div className="text-sm text-gray-500">
                              {recipe.code} • v{recipe.version} • {recipe.ingredients_count} ingredients
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {recipe.category}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {recipe.batch_size} {recipe.unit_of_measure}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${recipe.estimated_cost.toFixed(2)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            recipe.is_active 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {recipe.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(recipe.created_at)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button className="text-green-600 hover:text-green-900 mr-3">
                            View
                          </button>
                          <button className="text-green-600 hover:text-green-900">
                            Edit
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        {/* Add Recipe Modal */}
        <Modal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          title="Create New Recipe"
          maxWidth="xl"
        >
          <form onSubmit={(e) => { e.preventDefault(); handleAddRecipe(); }} className="space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label htmlFor="recipe_name" className="block text-sm font-medium text-gray-700">
                  Recipe Name
                </label>
                <input
                  type="text"
                  id="recipe_name"
                  required
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newRecipe.name}
                  onChange={(e) => setNewRecipe({ ...newRecipe, name: e.target.value })}
                />
              </div>
              <div>
                <label htmlFor="recipe_code" className="block text-sm font-medium text-gray-700">
                  Recipe Code
                </label>
                <input
                  type="text"
                  id="recipe_code"
                  required
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newRecipe.code}
                  onChange={(e) => setNewRecipe({ ...newRecipe, code: e.target.value })}
                />
              </div>
              <div>
                <label htmlFor="recipe_category" className="block text-sm font-medium text-gray-700">
                  Category
                </label>
                <select
                  id="recipe_category"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newRecipe.category}
                  onChange={(e) => setNewRecipe({ ...newRecipe, category: e.target.value })}
                >
                  <option value="Fruit Flavors">Fruit Flavors</option>
                  <option value="Vanilla Products">Vanilla Products</option>
                  <option value="Citrus Flavors">Citrus Flavors</option>
                  <option value="Dessert Flavors">Dessert Flavors</option>
                </select>
              </div>
              <div>
                <label htmlFor="recipe_unit" className="block text-sm font-medium text-gray-700">
                  Unit of Measure
                </label>
                <select
                  id="recipe_unit"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newRecipe.unit_of_measure}
                  onChange={(e) => setNewRecipe({ ...newRecipe, unit_of_measure: e.target.value })}
                >
                  <option value="liters">Liters</option>
                  <option value="kg">Kilograms</option>
                  <option value="gallons">Gallons</option>
                </select>
              </div>
              <div className="sm:col-span-2">
                <label htmlFor="batch_size" className="block text-sm font-medium text-gray-700">
                  Standard Batch Size
                </label>
                <input
                  type="number"
                  id="batch_size"
                  min="0.1"
                  step="0.1"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newRecipe.batch_size}
                  onChange={(e) => setNewRecipe({ ...newRecipe, batch_size: parseFloat(e.target.value) || 0 })}
                />
              </div>
              <div className="sm:col-span-2">
                <label htmlFor="instructions" className="block text-sm font-medium text-gray-700">
                  Instructions
                </label>
                <textarea
                  id="instructions"
                  rows={3}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newRecipe.instructions}
                  onChange={(e) => setNewRecipe({ ...newRecipe, instructions: e.target.value })}
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={() => setShowAddModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700"
              >
                Create Recipe
              </button>
            </div>
          </form>
        </Modal>
      </div>
    </MainLayout>
  )
}
