'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { useAuth } from '@/components/providers'
import { Modal } from '@/components/ui/modal'
import { DatabaseService } from '@/lib/database'
import {
  PlusIcon,
  ClipboardDocumentListIcon,
  CurrencyDollarIcon,
  TrashIcon,
  CalendarIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

interface Recipe {
  id: string
  name: string
  code: string
  category: string
  batch_size: number
  unit_of_measure: string
  estimated_cost: number
  ingredients_count: number
  version: number
  is_active: boolean
  created_at: string
}

interface InventoryItem {
  id: string
  name: string
  sku: string
  current_stock: number
  minimum_stock: number
  unit_of_measure: string
  unit_cost: number
  category_name?: string
}

interface RecipeIngredient {
  id: string
  inventory_item_id: string
  inventory_item_name: string
  quantity: number
  unit_of_measure: string
  unit_cost: number
  total_cost: number
  percentage: number
  notes?: string
}

interface ProductionSchedule {
  planned_date: string
  planned_time: string
  estimated_duration: number // in hours
  priority: 'low' | 'normal' | 'high' | 'urgent'
  assigned_to: string
  equipment_required: string[]
  special_instructions: string
}

export default function RecipesPage() {
  const { user } = useAuth()
  const [recipes, setRecipes] = useState<Recipe[]>([])
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)

  // Recipe basic information
  const [newRecipe, setNewRecipe] = useState({
    name: '',
    code: '',
    category: 'Fruit Flavors',
    batch_size: 10,
    unit_of_measure: 'liters',
    instructions: '',
    description: '',
    shelf_life: 12, // months
    storage_conditions: 'Cool, dry place'
  })

  // Recipe ingredients
  const [ingredients, setIngredients] = useState<RecipeIngredient[]>([])
  const [selectedInventoryItem, setSelectedInventoryItem] = useState('')
  const [ingredientQuantity, setIngredientQuantity] = useState(0)
  const [ingredientNotes, setIngredientNotes] = useState('')

  // Production schedule
  const [schedule, setSchedule] = useState<ProductionSchedule>({
    planned_date: '',
    planned_time: '09:00',
    estimated_duration: 4,
    priority: 'normal',
    assigned_to: '',
    equipment_required: [],
    special_instructions: ''
  })

  // Validation and cost calculation
  const [totalCost, setTotalCost] = useState(0)
  const [availabilityCheck, setAvailabilityCheck] = useState<{[key: string]: boolean}>({})
  const [validationErrors, setValidationErrors] = useState<string[]>([])

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true)

        // Load recipes and inventory items from database
        const [recipeData, inventoryData] = await Promise.all([
          DatabaseService.getRecipes(),
          DatabaseService.getInventoryItems()
        ])

        // Process inventory items for easier use
        const processedInventory = inventoryData.map(item => ({
          ...item,
          current_stock: parseFloat(item.current_stock.toString()),
          unit_cost: parseFloat(item.unit_cost.toString())
        }))

        setRecipes(recipeData)
        setInventoryItems(processedInventory)
      } catch (error) {
        console.error('Error loading data:', error)
        // Fallback to mock data if database fails
        const mockRecipes: Recipe[] = [
          {
            id: '1',
            name: 'Strawberry Vanilla Blend',
            code: 'SVB-001',
            category: 'Fruit Flavors',
            batch_size: 10.0,
            unit_of_measure: 'liters',
            estimated_cost: 185.50,
            ingredients_count: 3,
            version: 1,
            is_active: true,
            created_at: '2024-12-10T10:00:00Z'
          }
        ]
        setRecipes(mockRecipes)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  // Calculate total cost and update availability
  useEffect(() => {
    const cost = ingredients.reduce((sum, ing) => sum + (ing.total_cost || 0), 0)
    setTotalCost(cost || 0)

    // Check ingredient availability
    const availability: {[key: string]: boolean} = {}
    ingredients.forEach(ing => {
      const item = inventoryItems.find(inv => inv.id === ing.inventory_item_id)
      availability[ing.id] = item ? item.current_stock >= ing.quantity : false
    })
    setAvailabilityCheck(availability)
  }, [ingredients, inventoryItems])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const addIngredient = () => {
    if (!selectedInventoryItem || ingredientQuantity <= 0) return

    const inventoryItem = inventoryItems.find(item => item.id === selectedInventoryItem)
    if (!inventoryItem) return

    const unitCost = inventoryItem.unit_cost || 0
    const totalCost = ingredientQuantity * unitCost
    const newIngredient: RecipeIngredient = {
      id: Date.now().toString(),
      inventory_item_id: inventoryItem.id,
      inventory_item_name: inventoryItem.name,
      quantity: ingredientQuantity,
      unit_of_measure: inventoryItem.unit_of_measure,
      unit_cost: unitCost,
      total_cost: totalCost,
      percentage: 0, // Will be calculated after all ingredients are added
      notes: ingredientNotes
    }

    setIngredients([...ingredients, newIngredient])
    setSelectedInventoryItem('')
    setIngredientQuantity(0)
    setIngredientNotes('')
  }

  const removeIngredient = (id: string) => {
    setIngredients(ingredients.filter(ing => ing.id !== id))
  }

  const validateRecipe = () => {
    const errors: string[] = []

    if (!newRecipe.name.trim()) errors.push('Recipe name is required')
    if (!newRecipe.code.trim()) errors.push('Recipe code is required')
    if (ingredients.length === 0) errors.push('At least one ingredient is required')
    if (newRecipe.batch_size <= 0) errors.push('Batch size must be greater than 0')

    // Check ingredient availability
    const unavailableIngredients = ingredients.filter(ing => !availabilityCheck[ing.id])
    if (unavailableIngredients.length > 0) {
      errors.push(`Insufficient stock for: ${unavailableIngredients.map(ing => ing.inventory_item_name).join(', ')}`)
    }

    setValidationErrors(errors)
    return errors.length === 0
  }

  const handleCreateRecipe = async () => {
    if (!validateRecipe()) return

    try {
      // Calculate percentages
      const totalQuantity = ingredients.reduce((sum, ing) => sum + ing.quantity, 0)
      const ingredientsWithPercentages = ingredients.map(ing => ({
        ...ing,
        percentage: (ing.quantity / totalQuantity) * 100
      }))

      const recipeData = {
        name: newRecipe.name,
        code: newRecipe.code,
        category: newRecipe.category,
        batch_size: newRecipe.batch_size,
        unit_of_measure: newRecipe.unit_of_measure,
        instructions: newRecipe.instructions,
        is_active: true,
        version: 1
      }

      // In a real implementation, you would save to database here
      const recipe: Recipe = {
        id: (recipes.length + 1).toString(),
        ...recipeData,
        estimated_cost: totalCost,
        ingredients_count: ingredients.length,
        created_at: new Date().toISOString()
      }

      setRecipes([...recipes, recipe])
      resetForm()
      setShowAddModal(false)

      alert(`Recipe "${newRecipe.name}" created successfully!\nTotal Cost: $${(totalCost || 0).toFixed(2)}\nIngredients: ${ingredients.length}`)
    } catch (error) {
      console.error('Error creating recipe:', error)
      alert('Error creating recipe. Please try again.')
    }
  }

  const resetForm = () => {
    setCurrentStep(1)
    setNewRecipe({
      name: '',
      code: '',
      category: 'Fruit Flavors',
      batch_size: 10,
      unit_of_measure: 'liters',
      instructions: '',
      description: '',
      shelf_life: 12,
      storage_conditions: 'Cool, dry place'
    })
    setIngredients([])
    setSchedule({
      planned_date: '',
      planned_time: '09:00',
      estimated_duration: 4,
      priority: 'normal',
      assigned_to: '',
      equipment_required: [],
      special_instructions: ''
    })
    setValidationErrors([])
  }

  const nextStep = () => {
    if (currentStep < 4) setCurrentStep(currentStep + 1)
  }

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1)
  }

  if (!user) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Recipe Management</h1>
            <p className="mt-2 text-sm text-gray-700">
              Create and manage product formulations and recipes
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button
              type="button"
              onClick={() => setShowAddModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
              New Recipe
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-3">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ClipboardDocumentListIcon className="h-6 w-6 text-green-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Active Recipes</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {recipes.filter(r => r.is_active).length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CurrencyDollarIcon className="h-6 w-6 text-green-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Avg. Cost per Batch</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      ${recipes.length > 0 ? (recipes.reduce((sum, r) => sum + (r.estimated_cost || 0), 0) / recipes.length).toFixed(2) : '0.00'}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-6 w-6 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-xs font-medium text-green-600">V</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Versions</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {recipes.reduce((sum, r) => sum + r.version, 0)}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recipes Table */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              All Recipes ({recipes.length})
            </h3>
            
            {loading ? (
              <div className="animate-pulse">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-4 py-4">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Recipe
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Category
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Batch Size
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Cost
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created
                      </th>
                      <th className="relative px-6 py-3">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {recipes.map((recipe) => (
                      <tr key={recipe.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{recipe.name}</div>
                            <div className="text-sm text-gray-500">
                              {recipe.code} • v{recipe.version} • {recipe.ingredients_count} ingredients
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {recipe.category}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {recipe.batch_size} {recipe.unit_of_measure}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${(recipe.estimated_cost || 0).toFixed(2)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            recipe.is_active 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {recipe.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(recipe.created_at)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button className="text-green-600 hover:text-green-900 mr-3">
                            View
                          </button>
                          <button className="text-green-600 hover:text-green-900">
                            Edit
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        {/* Professional Recipe Creation Modal */}
        <Modal
          isOpen={showAddModal}
          onClose={() => { resetForm(); setShowAddModal(false); }}
          title={`Create New Recipe - Step ${currentStep} of 4`}
          maxWidth="2xl"
        >
          <div className="space-y-6">
            {/* Progress Steps */}
            <div className="flex items-center justify-between">
              {[1, 2, 3, 4].map((step) => (
                <div key={step} className="flex items-center">
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                    step <= currentStep
                      ? 'bg-green-600 border-green-600 text-white'
                      : 'border-gray-300 text-gray-500'
                  }`}>
                    {step < currentStep ? (
                      <CheckCircleIcon className="w-5 h-5" />
                    ) : (
                      <span className="text-sm font-medium">{step}</span>
                    )}
                  </div>
                  <div className="ml-2 text-sm font-medium text-gray-900">
                    {step === 1 && 'Basic Info'}
                    {step === 2 && 'Ingredients'}
                    {step === 3 && 'Schedule'}
                    {step === 4 && 'Review'}
                  </div>
                  {step < 4 && (
                    <div className={`ml-4 w-16 h-0.5 ${
                      step < currentStep ? 'bg-green-600' : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              ))}
            </div>

            {/* Validation Errors */}
            {validationErrors.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">Please fix the following errors:</h3>
                    <ul className="mt-2 text-sm text-red-700 list-disc list-inside">
                      {validationErrors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {/* Step Content */}
            <div className="min-h-96">
              {currentStep === 1 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Basic Recipe Information</h3>
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Recipe Name *</label>
                      <input
                        type="text"
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newRecipe.name}
                        onChange={(e) => setNewRecipe({ ...newRecipe, name: e.target.value })}
                        placeholder="e.g., Premium Vanilla Extract"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Recipe Code *</label>
                      <input
                        type="text"
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newRecipe.code}
                        onChange={(e) => setNewRecipe({ ...newRecipe, code: e.target.value.toUpperCase() })}
                        placeholder="e.g., PVE-001"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Category</label>
                      <select
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newRecipe.category}
                        onChange={(e) => setNewRecipe({ ...newRecipe, category: e.target.value })}
                      >
                        <option value="Fruit Flavors">Fruit Flavors</option>
                        <option value="Vanilla Products">Vanilla Products</option>
                        <option value="Citrus Flavors">Citrus Flavors</option>
                        <option value="Dessert Flavors">Dessert Flavors</option>
                        <option value="Spice Flavors">Spice Flavors</option>
                        <option value="Herbal Extracts">Herbal Extracts</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Unit of Measure</label>
                      <select
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newRecipe.unit_of_measure}
                        onChange={(e) => setNewRecipe({ ...newRecipe, unit_of_measure: e.target.value })}
                      >
                        <option value="liters">Liters</option>
                        <option value="kg">Kilograms</option>
                        <option value="gallons">Gallons</option>
                        <option value="pounds">Pounds</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Standard Batch Size *</label>
                      <input
                        type="number"
                        min="0.1"
                        step="0.1"
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newRecipe.batch_size}
                        onChange={(e) => setNewRecipe({ ...newRecipe, batch_size: parseFloat(e.target.value) || 0 })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Shelf Life (months)</label>
                      <input
                        type="number"
                        min="1"
                        max="60"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newRecipe.shelf_life}
                        onChange={(e) => setNewRecipe({ ...newRecipe, shelf_life: parseInt(e.target.value) || 12 })}
                      />
                    </div>
                    <div className="sm:col-span-2">
                      <label className="block text-sm font-medium text-gray-700">Description</label>
                      <textarea
                        rows={2}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newRecipe.description}
                        onChange={(e) => setNewRecipe({ ...newRecipe, description: e.target.value })}
                        placeholder="Brief description of the product..."
                      />
                    </div>
                    <div className="sm:col-span-2">
                      <label className="block text-sm font-medium text-gray-700">Storage Conditions</label>
                      <input
                        type="text"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newRecipe.storage_conditions}
                        onChange={(e) => setNewRecipe({ ...newRecipe, storage_conditions: e.target.value })}
                        placeholder="e.g., Cool, dry place away from direct sunlight"
                      />
                    </div>
                  </div>
                </div>
              )}

              {currentStep === 2 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Recipe Ingredients</h3>

                  {/* Add Ingredient Form */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="text-md font-medium text-gray-900 mb-3">Add Ingredient</h4>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
                      <div className="sm:col-span-2">
                        <label className="block text-sm font-medium text-gray-700">Inventory Item</label>
                        <select
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                          value={selectedInventoryItem}
                          onChange={(e) => setSelectedInventoryItem(e.target.value)}
                        >
                          <option value="">Select an ingredient...</option>
                          {inventoryItems.map(item => (
                            <option key={item.id} value={item.id}>
                              {item.name} ({item.current_stock} {item.unit_of_measure} available)
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Quantity</label>
                        <input
                          type="number"
                          min="0.001"
                          step="0.001"
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                          value={ingredientQuantity}
                          onChange={(e) => setIngredientQuantity(parseFloat(e.target.value) || 0)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Action</label>
                        <button
                          type="button"
                          onClick={addIngredient}
                          disabled={!selectedInventoryItem || ingredientQuantity <= 0}
                          className="mt-1 w-full px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400"
                        >
                          Add
                        </button>
                      </div>
                    </div>
                    <div className="mt-3">
                      <label className="block text-sm font-medium text-gray-700">Notes (optional)</label>
                      <input
                        type="text"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={ingredientNotes}
                        onChange={(e) => setIngredientNotes(e.target.value)}
                        placeholder="Special handling instructions..."
                      />
                    </div>
                  </div>

                  {/* Ingredients List */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-3">Recipe Ingredients ({ingredients.length})</h4>
                    {ingredients.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        No ingredients added yet. Add ingredients from your inventory above.
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {ingredients.map((ingredient) => (
                          <div key={ingredient.id} className="flex items-center justify-between p-3 bg-white border rounded-lg">
                            <div className="flex-1">
                              <div className="flex items-center space-x-4">
                                <div className="flex-1">
                                  <div className="font-medium text-gray-900">{ingredient.inventory_item_name}</div>
                                  <div className="text-sm text-gray-500">
                                    {ingredient.quantity} {ingredient.unit_of_measure} × ${(ingredient.unit_cost || 0).toFixed(2)} = ${(ingredient.total_cost || 0).toFixed(2)}
                                  </div>
                                  {ingredient.notes && (
                                    <div className="text-xs text-gray-400 mt-1">{ingredient.notes}</div>
                                  )}
                                </div>
                                <div className="text-right">
                                  <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                    availabilityCheck[ingredient.id]
                                      ? 'bg-green-100 text-green-800'
                                      : 'bg-red-100 text-red-800'
                                  }`}>
                                    {availabilityCheck[ingredient.id] ? 'Available' : 'Low Stock'}
                                  </div>
                                </div>
                              </div>
                            </div>
                            <button
                              type="button"
                              onClick={() => removeIngredient(ingredient.id)}
                              className="ml-4 p-1 text-red-600 hover:text-red-800"
                            >
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
                        <div className="mt-4 p-3 bg-green-50 rounded-lg">
                          <div className="text-sm font-medium text-green-800">
                            Total Recipe Cost: ${(totalCost || 0).toFixed(2)} ({ingredients.length} ingredients)
                          </div>
                          <div className="text-xs text-green-600 mt-1">
                            Cost per {newRecipe.unit_of_measure}: ${newRecipe.batch_size > 0 ? ((totalCost || 0) / newRecipe.batch_size).toFixed(2) : '0.00'}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {currentStep === 3 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Production Schedule</h3>
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Planned Production Date</label>
                      <input
                        type="date"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={schedule.planned_date}
                        onChange={(e) => setSchedule({ ...schedule, planned_date: e.target.value })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Planned Start Time</label>
                      <input
                        type="time"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={schedule.planned_time}
                        onChange={(e) => setSchedule({ ...schedule, planned_time: e.target.value })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Estimated Duration (hours)</label>
                      <input
                        type="number"
                        min="0.5"
                        step="0.5"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={schedule.estimated_duration}
                        onChange={(e) => setSchedule({ ...schedule, estimated_duration: parseFloat(e.target.value) || 4 })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Priority</label>
                      <select
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={schedule.priority}
                        onChange={(e) => setSchedule({ ...schedule, priority: e.target.value as any })}
                      >
                        <option value="low">Low</option>
                        <option value="normal">Normal</option>
                        <option value="high">High</option>
                        <option value="urgent">Urgent</option>
                      </select>
                    </div>
                    <div className="sm:col-span-2">
                      <label className="block text-sm font-medium text-gray-700">Assigned To</label>
                      <select
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={schedule.assigned_to}
                        onChange={(e) => setSchedule({ ...schedule, assigned_to: e.target.value })}
                      >
                        <option value="">Select operator...</option>
                        <option value="John Smith">John Smith</option>
                        <option value="Sarah Johnson">Sarah Johnson</option>
                        <option value="Mike Wilson">Mike Wilson</option>
                        <option value="Emily Davis">Emily Davis</option>
                      </select>
                    </div>
                    <div className="sm:col-span-2">
                      <label className="block text-sm font-medium text-gray-700">Special Instructions</label>
                      <textarea
                        rows={3}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={schedule.special_instructions}
                        onChange={(e) => setSchedule({ ...schedule, special_instructions: e.target.value })}
                        placeholder="Any special handling or production notes..."
                      />
                    </div>
                  </div>
                </div>
              )}

              {currentStep === 4 && (
                <div className="space-y-6">
                  <h3 className="text-lg font-medium text-gray-900">Review & Create Recipe</h3>

                  {/* Recipe Summary */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-3">Recipe Summary</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div><span className="font-medium">Name:</span> {newRecipe.name}</div>
                      <div><span className="font-medium">Code:</span> {newRecipe.code}</div>
                      <div><span className="font-medium">Category:</span> {newRecipe.category}</div>
                      <div><span className="font-medium">Batch Size:</span> {newRecipe.batch_size} {newRecipe.unit_of_measure}</div>
                      <div><span className="font-medium">Shelf Life:</span> {newRecipe.shelf_life} months</div>
                      <div><span className="font-medium">Total Cost:</span> ${(totalCost || 0).toFixed(2)}</div>
                    </div>
                  </div>

                  {/* Ingredients Summary */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-3">Ingredients ({ingredients.length})</h4>
                    <div className="space-y-2">
                      {ingredients.map((ingredient) => (
                        <div key={ingredient.id} className="flex justify-between text-sm">
                          <span>{ingredient.inventory_item_name}</span>
                          <span>{ingredient.quantity} {ingredient.unit_of_measure} (${(ingredient.total_cost || 0).toFixed(2)})</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Schedule Summary */}
                  {schedule.planned_date && (
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h4 className="font-medium text-gray-900 mb-3">Production Schedule</h4>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div><span className="font-medium">Date:</span> {schedule.planned_date}</div>
                        <div><span className="font-medium">Time:</span> {schedule.planned_time}</div>
                        <div><span className="font-medium">Duration:</span> {schedule.estimated_duration} hours</div>
                        <div><span className="font-medium">Priority:</span> {schedule.priority}</div>
                        {schedule.assigned_to && <div><span className="font-medium">Assigned:</span> {schedule.assigned_to}</div>}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={currentStep === 1 ? () => { resetForm(); setShowAddModal(false); } : prevStep}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                {currentStep === 1 ? 'Cancel' : 'Previous'}
              </button>

              <div className="flex space-x-3">
                {currentStep < 4 ? (
                  <button
                    type="button"
                    onClick={nextStep}
                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                  >
                    Next Step
                  </button>
                ) : (
                  <button
                    type="button"
                    onClick={handleCreateRecipe}
                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                  >
                    Create Recipe
                  </button>
                )}
              </div>
            </div>
          </div>
        </Modal>
      </div>
    </MainLayout>
  )
}
