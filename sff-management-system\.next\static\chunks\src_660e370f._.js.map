{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\nimport {\n  HomeIcon,\n  CubeIcon,\n  BeakerIcon,\n  CogIcon,\n  DocumentTextIcon,\n  UsersIcon,\n  ChartBarIcon,\n  ClipboardDocumentListIcon,\n} from '@heroicons/react/24/outline'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { clsx } from 'clsx'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/', icon: HomeIcon },\n  { name: 'Inventory', href: '/inventory', icon: CubeIcon },\n  { name: 'Recipes', href: '/recipes', icon: ClipboardDocumentListIcon },\n  { name: 'Production', href: '/production', icon: CogIcon },\n  { name: 'Quality', href: '/quality', icon: BeakerIcon },\n  { name: 'Reports', href: '/reports', icon: ChartBarIcon },\n  { name: 'Documents', href: '/documents', icon: DocumentTextIcon },\n  { name: 'Users', href: '/users', icon: UsersIcon },\n]\n\ninterface SidebarProps {\n  open: boolean\n  setOpen: (open: boolean) => void\n}\n\nexport function Sidebar({ open, setOpen }: SidebarProps) {\n  const pathname = usePathname()\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <Transition.Root show={open} as={Fragment}>\n        <Dialog as=\"div\" className=\"relative z-50 lg:hidden\" onClose={setOpen}>\n          <Transition.Child\n            as={Fragment}\n            enter=\"transition-opacity ease-linear duration-300\"\n            enterFrom=\"opacity-0\"\n            enterTo=\"opacity-100\"\n            leave=\"transition-opacity ease-linear duration-300\"\n            leaveFrom=\"opacity-100\"\n            leaveTo=\"opacity-0\"\n          >\n            <div className=\"fixed inset-0 bg-gray-900/80\" />\n          </Transition.Child>\n\n          <div className=\"fixed inset-0 flex\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"transition ease-in-out duration-300 transform\"\n              enterFrom=\"-translate-x-full\"\n              enterTo=\"translate-x-0\"\n              leave=\"transition ease-in-out duration-300 transform\"\n              leaveFrom=\"translate-x-0\"\n              leaveTo=\"-translate-x-full\"\n            >\n              <Dialog.Panel className=\"relative mr-16 flex w-full max-w-xs flex-1\">\n                <Transition.Child\n                  as={Fragment}\n                  enter=\"ease-in-out duration-300\"\n                  enterFrom=\"opacity-0\"\n                  enterTo=\"opacity-100\"\n                  leave=\"ease-in-out duration-300\"\n                  leaveFrom=\"opacity-100\"\n                  leaveTo=\"opacity-0\"\n                >\n                  <div className=\"absolute left-full top-0 flex w-16 justify-center pt-5\">\n                    <button type=\"button\" className=\"-m-2.5 p-2.5\" onClick={() => setOpen(false)}>\n                      <span className=\"sr-only\">Close sidebar</span>\n                      <XMarkIcon className=\"h-6 w-6 text-white\" aria-hidden=\"true\" />\n                    </button>\n                  </div>\n                </Transition.Child>\n                <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4\">\n                  <div className=\"flex h-16 shrink-0 items-center\">\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">SFF</span>\n                    </div>\n                    <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n                  </div>\n                  <nav className=\"flex flex-1 flex-col\">\n                    <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n                      <li>\n                        <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                          {navigation.map((item) => (\n                            <li key={item.name}>\n                              <Link\n                                href={item.href}\n                                className={clsx(\n                                  pathname === item.href\n                                    ? 'bg-green-50 text-green-700'\n                                    : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                                  'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                                )}\n                              >\n                                <item.icon\n                                  className={clsx(\n                                    pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                                    'h-6 w-6 shrink-0'\n                                  )}\n                                  aria-hidden=\"true\"\n                                />\n                                {item.name}\n                              </Link>\n                            </li>\n                          ))}\n                        </ul>\n                      </li>\n                    </ul>\n                  </nav>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </Dialog>\n      </Transition.Root>\n\n      {/* Static sidebar for desktop */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 bg-white px-6 pb-4\">\n          <div className=\"flex h-16 shrink-0 items-center\">\n            <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-sm\">SFF</span>\n            </div>\n            <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n          </div>\n          <nav className=\"flex flex-1 flex-col\">\n            <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n              <li>\n                <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                  {navigation.map((item) => (\n                    <li key={item.name}>\n                      <Link\n                        href={item.href}\n                        className={clsx(\n                          pathname === item.href\n                            ? 'bg-green-50 text-green-700'\n                            : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                          'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                        )}\n                      >\n                        <item.icon\n                          className={clsx(\n                            pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                            'h-6 w-6 shrink-0'\n                          )}\n                          aria-hidden=\"true\"\n                        />\n                        {item.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </li>\n            </ul>\n          </nav>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;;;AAjBA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,kNAAA,CAAA,WAAQ;IAAC;IAC/C;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,oPAAA,CAAA,4BAAyB;IAAC;IACrE;QAAE,MAAM;QAAc,MAAM;QAAe,MAAM,gNAAA,CAAA,UAAO;IAAC;IACzD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,sNAAA,CAAA,aAAU;IAAC;IACtD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,0NAAA,CAAA,eAAY;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kOAAA,CAAA,mBAAgB;IAAC;IAChE;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,oNAAA,CAAA,YAAS;IAAC;CAClD;AAOM,SAAS,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAgB;;IACrD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE;;0BAEE,6LAAC,0LAAA,CAAA,aAAU,CAAC,IAAI;gBAAC,MAAM;gBAAM,IAAI,6JAAA,CAAA,WAAQ;0BACvC,cAAA,6LAAC,kLAAA,CAAA,SAAM;oBAAC,IAAG;oBAAM,WAAU;oBAA0B,SAAS;;sCAC5D,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,6JAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;sCAGjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;gCACf,IAAI,6JAAA,CAAA,WAAQ;gCACZ,OAAM;gCACN,WAAU;gCACV,SAAQ;gCACR,OAAM;gCACN,WAAU;gCACV,SAAQ;0CAER,cAAA,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;oCAAC,WAAU;;sDACtB,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4CACf,IAAI,6JAAA,CAAA,WAAQ;4CACZ,OAAM;4CACN,WAAU;4CACV,SAAQ;4CACR,OAAM;4CACN,WAAU;4CACV,SAAQ;sDAER,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAO,MAAK;oDAAS,WAAU;oDAAe,SAAS,IAAM,QAAQ;;sEACpE,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;4DAAqB,eAAY;;;;;;;;;;;;;;;;;;;;;;sDAI5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,6LAAC;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;8DAE7D,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAG,MAAK;wDAAO,WAAU;kEACxB,cAAA,6LAAC;sEACC,cAAA,6LAAC;gEAAG,MAAK;gEAAO,WAAU;0EACvB,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;kFACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4EACH,MAAM,KAAK,IAAI;4EACf,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;8FAGF,6LAAC,KAAK,IAAI;oFACR,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;oFAEF,eAAY;;;;;;gFAEb,KAAK,IAAI;;;;;;;uEAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiCxC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAE7D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,MAAK;gCAAO,WAAU;0CACxB,cAAA,6LAAC;8CACC,cAAA,6LAAC;wCAAG,MAAK;wCAAO,WAAU;kDACvB,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;sEAGF,6LAAC,KAAK,IAAI;4DACR,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;4DAEF,eAAY;;;;;;wDAEb,KAAK,IAAI;;;;;;;+CAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BtC;GAtIgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Menu, Transition } from '@headlessui/react'\nimport { Bars3Icon, BellIcon } from '@heroicons/react/24/outline'\nimport { useAuth } from '@/components/providers'\nimport { clsx } from 'clsx'\n\ninterface HeaderProps {\n  setSidebarOpen: (open: boolean) => void\n}\n\nexport function Header({ setSidebarOpen }: HeaderProps) {\n  const { user, signOut } = useAuth()\n\n  const userNavigation = [\n    { name: 'Your profile', href: '#' },\n    { name: 'Settings', href: '#' },\n    { name: 'Sign out', href: '#', onClick: signOut },\n  ]\n\n  return (\n    <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n      <button\n        type=\"button\"\n        className=\"-m-2.5 p-2.5 text-gray-700 lg:hidden\"\n        onClick={() => setSidebarOpen(true)}\n      >\n        <span className=\"sr-only\">Open sidebar</span>\n        <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n      </button>\n\n      {/* Separator */}\n      <div className=\"h-6 w-px bg-gray-200 lg:hidden\" aria-hidden=\"true\" />\n\n      <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n        <div className=\"relative flex flex-1\">\n          {/* Search can be added here later */}\n        </div>\n        <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n          <button type=\"button\" className=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\">\n            <span className=\"sr-only\">View notifications</span>\n            <BellIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n\n          {/* Separator */}\n          <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200\" aria-hidden=\"true\" />\n\n          {/* Profile dropdown */}\n          <Menu as=\"div\" className=\"relative\">\n            <Menu.Button className=\"-m-1.5 flex items-center p-1.5\">\n              <span className=\"sr-only\">Open user menu</span>\n              <div className=\"h-8 w-8 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.username?.charAt(0).toUpperCase() || 'U'}\n                </span>\n              </div>\n              <span className=\"hidden lg:flex lg:items-center\">\n                <span className=\"ml-4 text-sm font-semibold leading-6 text-gray-900\" aria-hidden=\"true\">\n                  {user?.username || 'User'}\n                </span>\n                <span className=\"ml-2 text-xs text-gray-500 capitalize\">\n                  ({user?.role?.replace('_', ' ') || 'Role'})\n                </span>\n              </span>\n            </Menu.Button>\n            <Transition\n              as={Fragment}\n              enter=\"transition ease-out duration-100\"\n              enterFrom=\"transform opacity-0 scale-95\"\n              enterTo=\"transform opacity-100 scale-100\"\n              leave=\"transition ease-in duration-75\"\n              leaveFrom=\"transform opacity-100 scale-100\"\n              leaveTo=\"transform opacity-0 scale-95\"\n            >\n              <Menu.Items className=\"absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none\">\n                {userNavigation.map((item) => (\n                  <Menu.Item key={item.name}>\n                    {({ active }) => (\n                      <button\n                        onClick={item.onClick}\n                        className={clsx(\n                          active ? 'bg-gray-50' : '',\n                          'block w-full text-left px-3 py-1 text-sm leading-6 text-gray-900'\n                        )}\n                      >\n                        {item.name}\n                      </button>\n                    )}\n                  </Menu.Item>\n                ))}\n              </Menu.Items>\n            </Transition>\n          </Menu>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;;;AANA;;;;;;AAYO,SAAS,OAAO,EAAE,cAAc,EAAe;;IACpD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAAgB,MAAM;QAAI;QAClC;YAAE,MAAM;YAAY,MAAM;QAAI;QAC9B;YAAE,MAAM;YAAY,MAAM;YAAK,SAAS;QAAQ;KACjD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,eAAe;;kCAE9B,6LAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;wBAAU,eAAY;;;;;;;;;;;;0BAI7C,6LAAC;gBAAI,WAAU;gBAAiC,eAAY;;;;;;0BAE5D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,MAAK;gCAAS,WAAU;;kDAC9B,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;gCAAgD,eAAY;;;;;;0CAG3E,6LAAC,8KAAA,CAAA,OAAI;gCAAC,IAAG;gCAAM,WAAU;;kDACvB,6LAAC,8KAAA,CAAA,OAAI,CAAC,MAAM;wCAAC,WAAU;;0DACrB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb,MAAM,UAAU,OAAO,GAAG,iBAAiB;;;;;;;;;;;0DAGhD,6LAAC;gDAAK,WAAU;;kEACd,6LAAC;wDAAK,WAAU;wDAAqD,eAAY;kEAC9E,MAAM,YAAY;;;;;;kEAErB,6LAAC;wDAAK,WAAU;;4DAAwC;4DACpD,MAAM,MAAM,QAAQ,KAAK,QAAQ;4DAAO;;;;;;;;;;;;;;;;;;;kDAIhD,6LAAC,0LAAA,CAAA,aAAU;wCACT,IAAI,6JAAA,CAAA,WAAQ;wCACZ,OAAM;wCACN,WAAU;wCACV,SAAQ;wCACR,OAAM;wCACN,WAAU;wCACV,SAAQ;kDAER,cAAA,6LAAC,8KAAA,CAAA,OAAI,CAAC,KAAK;4CAAC,WAAU;sDACnB,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;8DACP,CAAC,EAAE,MAAM,EAAE,iBACV,6LAAC;4DACC,SAAS,KAAK,OAAO;4DACrB,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,SAAS,eAAe,IACxB;sEAGD,KAAK,IAAI;;;;;;mDATA,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqB3C;GAtFgB;;QACY,kIAAA,CAAA,UAAO;;;KADnB", "debugId": null}}, {"offset": {"line": 680, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/components/providers'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const { user } = useAuth()\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Sidebar open={sidebarOpen} setOpen={setSidebarOpen} />\n      \n      <div className=\"lg:pl-72\">\n        <Header setSidebarOpen={setSidebarOpen} />\n        \n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAWO,SAAS,WAAW,EAAE,QAAQ,EAAmB;;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAO;gBAAC,MAAM;gBAAa,SAAS;;;;;;0BAErC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,SAAM;wBAAC,gBAAgB;;;;;;kCAExB,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAnBgB;;QAEG,kIAAA,CAAA,UAAO;;;KAFV", "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/ui/modal.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\n\ninterface ModalProps {\n  isOpen: boolean\n  onClose: () => void\n  title: string\n  children: React.ReactNode\n  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl'\n}\n\nexport function Modal({ isOpen, onClose, title, children, maxWidth = 'lg' }: ModalProps) {\n  const maxWidthClasses = {\n    sm: 'sm:max-w-sm',\n    md: 'sm:max-w-md',\n    lg: 'sm:max-w-lg',\n    xl: 'sm:max-w-xl',\n    '2xl': 'sm:max-w-2xl'\n  }\n\n  return (\n    <Transition.Root show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={onClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-out duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in duration-200\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 z-10 overflow-y-auto\">\n          <div className=\"flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"ease-out duration-300\"\n              enterFrom=\"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95\"\n              enterTo=\"opacity-100 translate-y-0 sm:scale-100\"\n              leave=\"ease-in duration-200\"\n              leaveFrom=\"opacity-100 translate-y-0 sm:scale-100\"\n              leaveTo=\"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95\"\n            >\n              <Dialog.Panel className={`relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full ${maxWidthClasses[maxWidth]} sm:p-6`}>\n                <div className=\"absolute right-0 top-0 hidden pr-4 pt-4 sm:block\">\n                  <button\n                    type=\"button\"\n                    className=\"rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2\"\n                    onClick={onClose}\n                  >\n                    <span className=\"sr-only\">Close</span>\n                    <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                  </button>\n                </div>\n                <div className=\"sm:flex sm:items-start\">\n                  <div className=\"mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full\">\n                    <Dialog.Title as=\"h3\" className=\"text-lg font-semibold leading-6 text-gray-900 mb-4\">\n                      {title}\n                    </Dialog.Title>\n                    <div className=\"mt-2\">\n                      {children}\n                    </div>\n                  </div>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </div>\n      </Dialog>\n    </Transition.Root>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAcO,SAAS,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAc;IACrF,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;IACT;IAEA,qBACE,6LAAC,0LAAA,CAAA,aAAU,CAAC,IAAI;QAAC,MAAM;QAAQ,IAAI,6JAAA,CAAA,WAAQ;kBACzC,cAAA,6LAAC,kLAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,6JAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,6JAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;gCAAC,WAAW,CAAC,2HAA2H,EAAE,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC;;kDACvL,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS;;8DAET,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;;;;;;kDAG/C,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;oDAAC,IAAG;oDAAK,WAAU;8DAC7B;;;;;;8DAEH,6LAAC;oDAAI,WAAU;8DACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvB;KA/DgB", "debugId": null}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/app/recipes/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { useAuth } from '@/components/providers'\nimport { Modal } from '@/components/ui/modal'\nimport { DatabaseService } from '@/lib/database'\nimport {\n  PlusIcon,\n  ClipboardDocumentListIcon,\n  CurrencyDollarIcon,\n  TrashIcon,\n  CalendarIcon,\n  ClockIcon,\n  CheckCircleIcon,\n  ExclamationTriangleIcon\n} from '@heroicons/react/24/outline'\n\ninterface Recipe {\n  id: string\n  name: string\n  code: string\n  category: string\n  batch_size: number\n  unit_of_measure: string\n  estimated_cost: number\n  ingredients_count: number\n  version: number\n  is_active: boolean\n  created_at: string\n}\n\ninterface InventoryItem {\n  id: string\n  name: string\n  sku: string\n  current_stock: number\n  minimum_stock: number\n  unit_of_measure: string\n  unit_cost: number\n  category_name?: string\n}\n\ninterface RecipeIngredient {\n  id: string\n  inventory_item_id: string\n  inventory_item_name: string\n  quantity: number\n  unit_of_measure: string\n  unit_cost: number\n  total_cost: number\n  percentage: number\n  notes?: string\n}\n\ninterface ProductionSchedule {\n  planned_date: string\n  planned_time: string\n  estimated_duration: number // in hours\n  priority: 'low' | 'normal' | 'high' | 'urgent'\n  assigned_to: string\n  equipment_required: string[]\n  special_instructions: string\n}\n\nexport default function RecipesPage() {\n  const { user } = useAuth()\n  const [recipes, setRecipes] = useState<Recipe[]>([])\n  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showAddModal, setShowAddModal] = useState(false)\n  const [currentStep, setCurrentStep] = useState(1)\n\n  // Recipe basic information\n  const [newRecipe, setNewRecipe] = useState({\n    name: '',\n    code: '',\n    category: 'Fruit Flavors',\n    batch_size: 10,\n    unit_of_measure: 'liters',\n    instructions: '',\n    description: '',\n    shelf_life: 12, // months\n    storage_conditions: 'Cool, dry place'\n  })\n\n  // Recipe ingredients\n  const [ingredients, setIngredients] = useState<RecipeIngredient[]>([])\n  const [selectedInventoryItem, setSelectedInventoryItem] = useState('')\n  const [ingredientQuantity, setIngredientQuantity] = useState(0)\n  const [ingredientNotes, setIngredientNotes] = useState('')\n\n  // Production schedule\n  const [schedule, setSchedule] = useState<ProductionSchedule>({\n    planned_date: '',\n    planned_time: '09:00',\n    estimated_duration: 4,\n    priority: 'normal',\n    assigned_to: '',\n    equipment_required: [],\n    special_instructions: ''\n  })\n\n  // Validation and cost calculation\n  const [totalCost, setTotalCost] = useState(0)\n  const [availabilityCheck, setAvailabilityCheck] = useState<{[key: string]: boolean}>({})\n  const [validationErrors, setValidationErrors] = useState<string[]>([])\n\n  useEffect(() => {\n    // Mock recipes data\n    const mockRecipes: Recipe[] = [\n      {\n        id: '1',\n        name: 'Strawberry Vanilla Blend',\n        code: 'SVB-001',\n        category: 'Fruit Flavors',\n        batch_size: 10.0,\n        unit_of_measure: 'liters',\n        estimated_cost: 185.50,\n        ingredients_count: 3,\n        version: 1,\n        is_active: true,\n        created_at: '2024-12-10T10:00:00Z'\n      },\n      {\n        id: '2',\n        name: 'Classic Vanilla Extract',\n        code: 'CVE-001',\n        category: 'Vanilla Products',\n        batch_size: 5.0,\n        unit_of_measure: 'liters',\n        estimated_cost: 117.00,\n        ingredients_count: 2,\n        version: 1,\n        is_active: true,\n        created_at: '2024-12-10T11:00:00Z'\n      },\n      {\n        id: '3',\n        name: 'Citrus Burst Flavor',\n        code: 'CBF-001',\n        category: 'Citrus Flavors',\n        batch_size: 8.0,\n        unit_of_measure: 'liters',\n        estimated_cost: 156.80,\n        ingredients_count: 4,\n        version: 2,\n        is_active: true,\n        created_at: '2024-12-09T14:30:00Z'\n      },\n      {\n        id: '4',\n        name: 'Chocolate Essence',\n        code: 'CHE-001',\n        category: 'Dessert Flavors',\n        batch_size: 6.0,\n        unit_of_measure: 'liters',\n        estimated_cost: 198.60,\n        ingredients_count: 5,\n        version: 1,\n        is_active: false,\n        created_at: '2024-12-08T09:15:00Z'\n      }\n    ]\n\n    setTimeout(() => {\n      setRecipes(mockRecipes)\n      setLoading(false)\n    }, 1000)\n  }, [])\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    })\n  }\n\n  const handleAddRecipe = () => {\n    const recipe: Recipe = {\n      id: (recipes.length + 1).toString(),\n      name: newRecipe.name,\n      code: newRecipe.code,\n      category: newRecipe.category,\n      batch_size: newRecipe.batch_size,\n      unit_of_measure: newRecipe.unit_of_measure,\n      estimated_cost: Math.random() * 200 + 50, // Mock cost calculation\n      ingredients_count: Math.floor(Math.random() * 5) + 2,\n      version: 1,\n      is_active: true,\n      created_at: new Date().toISOString()\n    }\n\n    setRecipes([...recipes, recipe])\n    setShowAddModal(false)\n    setNewRecipe({\n      name: '',\n      code: '',\n      category: 'Fruit Flavors',\n      batch_size: 10,\n      unit_of_measure: 'liters',\n      instructions: ''\n    })\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return (\n    <MainLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"sm:flex sm:items-center sm:justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Recipe Management</h1>\n            <p className=\"mt-2 text-sm text-gray-700\">\n              Create and manage product formulations and recipes\n            </p>\n          </div>\n          <div className=\"mt-4 sm:mt-0\">\n            <button\n              type=\"button\"\n              onClick={() => setShowAddModal(true)}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n            >\n              <PlusIcon className=\"-ml-1 mr-2 h-5 w-5\" aria-hidden=\"true\" />\n              New Recipe\n            </button>\n          </div>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-3\">\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <ClipboardDocumentListIcon className=\"h-6 w-6 text-green-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Active Recipes</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {recipes.filter(r => r.is_active).length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <CurrencyDollarIcon className=\"h-6 w-6 text-green-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Avg. Cost per Batch</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      ${recipes.length > 0 ? (recipes.reduce((sum, r) => sum + r.estimated_cost, 0) / recipes.length).toFixed(2) : '0.00'}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"h-6 w-6 bg-green-100 rounded-full flex items-center justify-center\">\n                    <span className=\"text-xs font-medium text-green-600\">V</span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Versions</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {recipes.reduce((sum, r) => sum + r.version, 0)}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Recipes Table */}\n        <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n              All Recipes ({recipes.length})\n            </h3>\n            \n            {loading ? (\n              <div className=\"animate-pulse\">\n                {[...Array(4)].map((_, i) => (\n                  <div key={i} className=\"flex items-center space-x-4 py-4\">\n                    <div className=\"h-4 bg-gray-200 rounded w-1/4\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full divide-y divide-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Recipe\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Category\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Batch Size\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Cost\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Created\n                      </th>\n                      <th className=\"relative px-6 py-3\">\n                        <span className=\"sr-only\">Actions</span>\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {recipes.map((recipe) => (\n                      <tr key={recipe.id} className=\"hover:bg-gray-50\">\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div>\n                            <div className=\"text-sm font-medium text-gray-900\">{recipe.name}</div>\n                            <div className=\"text-sm text-gray-500\">\n                              {recipe.code} • v{recipe.version} • {recipe.ingredients_count} ingredients\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {recipe.category}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {recipe.batch_size} {recipe.unit_of_measure}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          ${recipe.estimated_cost.toFixed(2)}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                            recipe.is_active \n                              ? 'bg-green-100 text-green-800' \n                              : 'bg-gray-100 text-gray-800'\n                          }`}>\n                            {recipe.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                          {formatDate(recipe.created_at)}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                          <button className=\"text-green-600 hover:text-green-900 mr-3\">\n                            View\n                          </button>\n                          <button className=\"text-green-600 hover:text-green-900\">\n                            Edit\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Add Recipe Modal */}\n        <Modal\n          isOpen={showAddModal}\n          onClose={() => setShowAddModal(false)}\n          title=\"Create New Recipe\"\n          maxWidth=\"xl\"\n        >\n          <form onSubmit={(e) => { e.preventDefault(); handleAddRecipe(); }} className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n              <div>\n                <label htmlFor=\"recipe_name\" className=\"block text-sm font-medium text-gray-700\">\n                  Recipe Name\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"recipe_name\"\n                  required\n                  className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                  value={newRecipe.name}\n                  onChange={(e) => setNewRecipe({ ...newRecipe, name: e.target.value })}\n                />\n              </div>\n              <div>\n                <label htmlFor=\"recipe_code\" className=\"block text-sm font-medium text-gray-700\">\n                  Recipe Code\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"recipe_code\"\n                  required\n                  className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                  value={newRecipe.code}\n                  onChange={(e) => setNewRecipe({ ...newRecipe, code: e.target.value })}\n                />\n              </div>\n              <div>\n                <label htmlFor=\"recipe_category\" className=\"block text-sm font-medium text-gray-700\">\n                  Category\n                </label>\n                <select\n                  id=\"recipe_category\"\n                  className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                  value={newRecipe.category}\n                  onChange={(e) => setNewRecipe({ ...newRecipe, category: e.target.value })}\n                >\n                  <option value=\"Fruit Flavors\">Fruit Flavors</option>\n                  <option value=\"Vanilla Products\">Vanilla Products</option>\n                  <option value=\"Citrus Flavors\">Citrus Flavors</option>\n                  <option value=\"Dessert Flavors\">Dessert Flavors</option>\n                </select>\n              </div>\n              <div>\n                <label htmlFor=\"recipe_unit\" className=\"block text-sm font-medium text-gray-700\">\n                  Unit of Measure\n                </label>\n                <select\n                  id=\"recipe_unit\"\n                  className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                  value={newRecipe.unit_of_measure}\n                  onChange={(e) => setNewRecipe({ ...newRecipe, unit_of_measure: e.target.value })}\n                >\n                  <option value=\"liters\">Liters</option>\n                  <option value=\"kg\">Kilograms</option>\n                  <option value=\"gallons\">Gallons</option>\n                </select>\n              </div>\n              <div className=\"sm:col-span-2\">\n                <label htmlFor=\"batch_size\" className=\"block text-sm font-medium text-gray-700\">\n                  Standard Batch Size\n                </label>\n                <input\n                  type=\"number\"\n                  id=\"batch_size\"\n                  min=\"0.1\"\n                  step=\"0.1\"\n                  className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                  value={newRecipe.batch_size}\n                  onChange={(e) => setNewRecipe({ ...newRecipe, batch_size: parseFloat(e.target.value) || 0 })}\n                />\n              </div>\n              <div className=\"sm:col-span-2\">\n                <label htmlFor=\"instructions\" className=\"block text-sm font-medium text-gray-700\">\n                  Instructions\n                </label>\n                <textarea\n                  id=\"instructions\"\n                  rows={3}\n                  className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                  value={newRecipe.instructions}\n                  onChange={(e) => setNewRecipe({ ...newRecipe, instructions: e.target.value })}\n                />\n              </div>\n            </div>\n            <div className=\"flex justify-end space-x-3 pt-4\">\n              <button\n                type=\"button\"\n                onClick={() => setShowAddModal(false)}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700\"\n              >\n                Create Recipe\n              </button>\n            </div>\n          </form>\n        </Modal>\n      </div>\n    </MainLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;;;AAPA;;;;;;AAiEe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,2BAA2B;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,MAAM;QACN,MAAM;QACN,UAAU;QACV,YAAY;QACZ,iBAAiB;QACjB,cAAc;QACd,aAAa;QACb,YAAY;QACZ,oBAAoB;IACtB;IAEA,qBAAqB;IACrB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACrE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,sBAAsB;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;QAC3D,cAAc;QACd,cAAc;QACd,oBAAoB;QACpB,UAAU;QACV,aAAa;QACb,oBAAoB,EAAE;QACtB,sBAAsB;IACxB;IAEA,kCAAkC;IAClC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B,CAAC;IACtF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAErE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,oBAAoB;YACpB,MAAM,cAAwB;gBAC5B;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,UAAU;oBACV,YAAY;oBACZ,iBAAiB;oBACjB,gBAAgB;oBAChB,mBAAmB;oBACnB,SAAS;oBACT,WAAW;oBACX,YAAY;gBACd;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,UAAU;oBACV,YAAY;oBACZ,iBAAiB;oBACjB,gBAAgB;oBAChB,mBAAmB;oBACnB,SAAS;oBACT,WAAW;oBACX,YAAY;gBACd;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,UAAU;oBACV,YAAY;oBACZ,iBAAiB;oBACjB,gBAAgB;oBAChB,mBAAmB;oBACnB,SAAS;oBACT,WAAW;oBACX,YAAY;gBACd;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,UAAU;oBACV,YAAY;oBACZ,iBAAiB;oBACjB,gBAAgB;oBAChB,mBAAmB;oBACnB,SAAS;oBACT,WAAW;oBACX,YAAY;gBACd;aACD;YAED;yCAAW;oBACT,WAAW;oBACX,WAAW;gBACb;wCAAG;QACL;gCAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,kBAAkB;QACtB,MAAM,SAAiB;YACrB,IAAI,CAAC,QAAQ,MAAM,GAAG,CAAC,EAAE,QAAQ;YACjC,MAAM,UAAU,IAAI;YACpB,MAAM,UAAU,IAAI;YACpB,UAAU,UAAU,QAAQ;YAC5B,YAAY,UAAU,UAAU;YAChC,iBAAiB,UAAU,eAAe;YAC1C,gBAAgB,KAAK,MAAM,KAAK,MAAM;YACtC,mBAAmB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;YACnD,SAAS;YACT,WAAW;YACX,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,WAAW;eAAI;YAAS;SAAO;QAC/B,gBAAgB;QAChB,aAAa;YACX,MAAM;YACN,MAAM;YACN,UAAU;YACV,YAAY;YACZ,iBAAiB;YACjB,cAAc;QAChB;IACF;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC,iJAAA,CAAA,aAAU;kBACT,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;;kDAEV,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAqB,eAAY;;;;;;oCAAS;;;;;;;;;;;;;;;;;;8BAOpE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,oPAAA,CAAA,4BAAyB;gDAAC,WAAU;gDAAyB,eAAY;;;;;;;;;;;sDAE5E,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEACX,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQpD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,sOAAA,CAAA,qBAAkB;gDAAC,WAAU;gDAAyB,eAAY;;;;;;;;;;;sDAErE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;;4DAAoC;4DAC9C,QAAQ,MAAM,GAAG,IAAI,CAAC,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,cAAc,EAAE,KAAK,QAAQ,MAAM,EAAE,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQzH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAqC;;;;;;;;;;;;;;;;sDAGzD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEACX,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAU3D,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAmD;oCACjD,QAAQ,MAAM;oCAAC;;;;;;;4BAG9B,wBACC,6LAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wCAAY,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;uCALP;;;;;;;;;qDAUd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;sDAIhC,6LAAC;4CAAM,WAAU;sDACd,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;oDAAmB,WAAU;;sEAC5B,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;kFAAqC,OAAO,IAAI;;;;;;kFAC/D,6LAAC;wEAAI,WAAU;;4EACZ,OAAO,IAAI;4EAAC;4EAAK,OAAO,OAAO;4EAAC;4EAAI,OAAO,iBAAiB;4EAAC;;;;;;;;;;;;;;;;;;sEAIpE,6LAAC;4DAAG,WAAU;sEACX,OAAO,QAAQ;;;;;;sEAElB,6LAAC;4DAAG,WAAU;;gEACX,OAAO,UAAU;gEAAC;gEAAE,OAAO,eAAe;;;;;;;sEAE7C,6LAAC;4DAAG,WAAU;;gEAAoD;gEAC9D,OAAO,cAAc,CAAC,OAAO,CAAC;;;;;;;sEAElC,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,CAAC,wEAAwE,EACxF,OAAO,SAAS,GACZ,gCACA,6BACJ;0EACC,OAAO,SAAS,GAAG,WAAW;;;;;;;;;;;sEAGnC,6LAAC;4DAAG,WAAU;sEACX,WAAW,OAAO,UAAU;;;;;;sEAE/B,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAO,WAAU;8EAA2C;;;;;;8EAG7D,6LAAC;oEAAO,WAAU;8EAAsC;;;;;;;;;;;;;mDAlCnD,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAgDhC,6LAAC,oIAAA,CAAA,QAAK;oBACJ,QAAQ;oBACR,SAAS,IAAM,gBAAgB;oBAC/B,OAAM;oBACN,UAAS;8BAET,cAAA,6LAAC;wBAAK,UAAU,CAAC;4BAAQ,EAAE,cAAc;4BAAI;wBAAmB;wBAAG,WAAU;;0CAC3E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAc,WAAU;0DAA0C;;;;;;0DAGjF,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,QAAQ;gDACR,WAAU;gDACV,OAAO,UAAU,IAAI;gDACrB,UAAU,CAAC,IAAM,aAAa;wDAAE,GAAG,SAAS;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;kDAGvE,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAc,WAAU;0DAA0C;;;;;;0DAGjF,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,QAAQ;gDACR,WAAU;gDACV,OAAO,UAAU,IAAI;gDACrB,UAAU,CAAC,IAAM,aAAa;wDAAE,GAAG,SAAS;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;kDAGvE,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAkB,WAAU;0DAA0C;;;;;;0DAGrF,6LAAC;gDACC,IAAG;gDACH,WAAU;gDACV,OAAO,UAAU,QAAQ;gDACzB,UAAU,CAAC,IAAM,aAAa;wDAAE,GAAG,SAAS;wDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oDAAC;;kEAEvE,6LAAC;wDAAO,OAAM;kEAAgB;;;;;;kEAC9B,6LAAC;wDAAO,OAAM;kEAAmB;;;;;;kEACjC,6LAAC;wDAAO,OAAM;kEAAiB;;;;;;kEAC/B,6LAAC;wDAAO,OAAM;kEAAkB;;;;;;;;;;;;;;;;;;kDAGpC,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAc,WAAU;0DAA0C;;;;;;0DAGjF,6LAAC;gDACC,IAAG;gDACH,WAAU;gDACV,OAAO,UAAU,eAAe;gDAChC,UAAU,CAAC,IAAM,aAAa;wDAAE,GAAG,SAAS;wDAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAAC;;kEAE9E,6LAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,6LAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,6LAAC;wDAAO,OAAM;kEAAU;;;;;;;;;;;;;;;;;;kDAG5B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,SAAQ;gDAAa,WAAU;0DAA0C;;;;;;0DAGhF,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,KAAI;gDACJ,MAAK;gDACL,WAAU;gDACV,OAAO,UAAU,UAAU;gDAC3B,UAAU,CAAC,IAAM,aAAa;wDAAE,GAAG,SAAS;wDAAE,YAAY,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDAAE;;;;;;;;;;;;kDAG9F,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,SAAQ;gDAAe,WAAU;0DAA0C;;;;;;0DAGlF,6LAAC;gDACC,IAAG;gDACH,MAAM;gDACN,WAAU;gDACV,OAAO,UAAU,YAAY;gDAC7B,UAAU,CAAC,IAAM,aAAa;wDAAE,GAAG,SAAS;wDAAE,cAAc,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;;;;;;;0CAIjF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GApbwB;;QACL,kIAAA,CAAA,UAAO;;;KADF", "debugId": null}}]}