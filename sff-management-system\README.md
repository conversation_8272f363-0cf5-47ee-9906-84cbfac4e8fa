# SFF Production & Quality Management System

A comprehensive production and quality management system built with Next.js, TypeScript, and Supabase.

## 🚀 Features

### 📊 Dashboard
- Real-time statistics and KPIs
- Recent activity feed
- Inventory alerts and notifications
- Quick action shortcuts

### 📦 Inventory Management
- Complete inventory tracking with categories
- Real-time stock levels and alerts
- Automatic low stock notifications
- Supplier information and batch tracking
- Expiry date management

### 📋 Recipe Management
- Recipe creation and versioning
- Ingredient management with precise ratios
- Cost calculation based on current inventory prices
- Recipe availability checking before production

### 🏭 Production Management
- Test batch production for quality verification
- Full-scale production batch management
- Real-time inventory integration
- Production cost tracking and yield analysis
- Status tracking (planned, in-progress, completed)

### 🧪 Quality Management
- **MSDS (Material Safety Data Sheet)** generation and management
- **COA (Certificate of Analysis)** creation and tracking
- **TDS (Technical Data Sheet)** documentation
- Quality specifications and HACCP compliance
- Laboratory test result recording

### 👥 User Management & Security
- **Beautiful Login Design** with white and green theme
- **Username/Password Authentication** (no email required)
- Role-based access control (Admin, Quality Manager, Production Manager, Employee)
- Demo accounts with different permission levels
- Secure session management
- Audit logging for all activities

### 📈 Reports & Analytics
- Production efficiency reports
- Cost analysis and tracking
- Quality compliance reports
- Inventory turnover analysis

## 🛠 Technology Stack

- **Frontend**: Next.js 14, React, TypeScript
- **Styling**: Tailwind CSS, Headless UI
- **Database**: PostgreSQL (Supabase)
- **Authentication**: Supabase Auth
- **Real-time**: Supabase Real-time subscriptions
- **Icons**: Heroicons
- **Charts**: Recharts
- **PDF Generation**: jsPDF

## 🏗 Database Schema

### Core Tables
- `profiles` - User profiles with roles
- `inventory_categories` - Inventory categorization
- `inventory_items` - Complete inventory management
- `stock_movements` - Inventory transaction history
- `recipes` - Recipe definitions and versions
- `recipe_ingredients` - Recipe composition
- `production_batches` - Production tracking
- `test_samples` - Quality testing records
- `quality_documents` - MSDS, COA, TDS documents
- `audit_logs` - System activity tracking

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- Supabase account

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd sff-management-system
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   Create a `.env.local` file with your Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open the application**
   Navigate to [http://localhost:3000](http://localhost:3000)

6. **Login with demo credentials**
   Use any of the provided demo accounts (see SETUP.md for details)

## 📋 User Roles & Permissions

### Admin
- Full system access
- User management
- System configuration
- All CRUD operations

### Quality Manager
- Quality document management (MSDS, COA, TDS)
- Test sample recording
- Quality specifications
- Production quality approval

### Production Manager
- Inventory management
- Recipe creation and modification
- Production batch management
- Cost analysis

### Employee
- Limited read access
- Basic inventory viewing
- Production status updates

## 🔐 Security Features

- **Row Level Security (RLS)** - Database-level security
- **Role-based Access Control** - Feature-level permissions
- **Audit Logging** - Complete activity tracking
- **Secure Authentication** - Supabase Auth integration
- **Data Validation** - Input sanitization and validation

## 📊 Key Integrations

### Inventory ↔ Production
- Automatic ingredient availability checking
- Real-time stock deduction during production
- Automatic purchase request generation

### Production ↔ Quality
- Test batch approval workflow
- Quality document generation
- Batch tracking and traceability

### Cost Management
- Real-time cost calculation
- Ingredient cost tracking
- Production efficiency metrics

## 🎯 Sample Data

The system includes sample data for testing:
- Inventory categories (Flavors, Base Materials, Chemicals, etc.)
- Sample inventory items with stock levels
- Example recipes (Strawberry Vanilla Blend, Classic Vanilla Extract)
- Recipe ingredients with precise ratios

## 🔄 Development Workflow

1. **Feature Development**: Create feature branches
2. **Testing**: Use sample data for testing
3. **Quality Assurance**: Test role-based permissions
4. **Deployment**: Deploy to production environment

## 📞 Support

For technical support or feature requests, please contact the development team.

---

**Built with ❤️ for SFF Production & Quality Management**
