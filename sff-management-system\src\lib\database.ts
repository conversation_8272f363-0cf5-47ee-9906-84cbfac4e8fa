import { createSupabaseClient } from './supabase'

const supabase = createSupabaseClient()

// Inventory Items
export interface InventoryItem {
  id: string
  name: string
  sku: string
  category_id: string
  current_stock: number
  minimum_stock: number
  unit_of_measure: string
  unit_cost: number
  supplier?: string
  description?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface InventoryCategory {
  id: string
  name: string
  description?: string
  is_active: boolean
}

// Recipes
export interface Recipe {
  id: string
  name: string
  code: string
  category: string
  batch_size: number
  unit_of_measure: string
  instructions?: string
  is_active: boolean
  version: number
  created_at: string
  updated_at: string
}

// Production Batches
export interface ProductionBatch {
  id: string
  batch_number: string
  recipe_id: string
  batch_type: 'test' | 'production'
  planned_quantity: number
  actual_quantity?: number
  unit_of_measure: string
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'
  priority: 'low' | 'normal' | 'high' | 'urgent'
  planned_start_date?: string
  planned_end_date?: string
  actual_start_date?: string
  actual_end_date?: string
  assigned_to?: string
  notes?: string
  created_at: string
  updated_at: string
}

// Quality Documents
export interface QualityDocument {
  id: string
  document_type: 'msds' | 'coa' | 'tds' | 'quality_spec' | 'lab_report'
  title: string
  document_number: string
  version: string
  item_id?: string
  batch_id?: string
  status: 'draft' | 'review' | 'approved' | 'expired'
  valid_from?: string
  valid_until?: string
  file_path?: string
  created_by: string
  created_at: string
  updated_at: string
}

// Database service functions
export class DatabaseService {
  // Inventory Items
  static async getInventoryItems(): Promise<InventoryItem[]> {
    try {
      const { data, error } = await supabase
        .from('inventory_items')
        .select(`
          *,
          inventory_categories(name)
        `)
        .eq('is_active', true)
        .order('name')

      if (error) {
        console.error('Error fetching inventory items:', error)
        // Only return demo data if it's a connection error
        if (error.message.includes('Failed to fetch') || error.message.includes('network')) {
          return this.getDemoInventoryItems()
        }
        return []
      }

      // Return actual data from database (even if empty)
      return data || []
    } catch (error) {
      console.error('Database connection error:', error)
      return this.getDemoInventoryItems()
    }
  }

  static getDemoInventoryItems(): InventoryItem[] {
    return [
      {
        id: 'demo_item_1',
        name: 'Vanilla Extract Premium',
        sku: 'VAN-001',
        category_id: 'demo_cat_1',
        current_stock: 25.5,
        minimum_stock: 10.0,
        unit_of_measure: 'liters',
        unit_cost: 12.50,
        supplier: 'Premium Ingredients Co.',
        description: 'High-quality vanilla extract for premium flavoring applications',
        is_active: true,
        created_at: '2024-01-15T08:00:00Z',
        updated_at: '2024-01-15T08:00:00Z'
      },
      {
        id: 'demo_item_2',
        name: 'Strawberry Concentrate',
        sku: 'STR-002',
        category_id: 'demo_cat_1',
        current_stock: 8.2,
        minimum_stock: 15.0,
        unit_of_measure: 'liters',
        unit_cost: 18.75,
        supplier: 'Fruit Essences Ltd.',
        description: 'Natural strawberry concentrate for beverage and dessert applications',
        is_active: true,
        created_at: '2024-01-20T10:30:00Z',
        updated_at: '2024-01-20T10:30:00Z'
      },
      {
        id: 'demo_item_3',
        name: 'Citric Acid Food Grade',
        sku: 'CIT-003',
        category_id: 'demo_cat_2',
        current_stock: 45.0,
        minimum_stock: 20.0,
        unit_of_measure: 'kg',
        unit_cost: 3.25,
        supplier: 'Chemical Solutions Inc.',
        description: 'Food-grade citric acid for pH adjustment and preservation',
        is_active: true,
        created_at: '2024-02-01T14:15:00Z',
        updated_at: '2024-02-01T14:15:00Z'
      },
      {
        id: 'demo_item_4',
        name: 'Glass Bottles 500ml',
        sku: 'BTL-004',
        category_id: 'demo_cat_3',
        current_stock: 0,
        minimum_stock: 100,
        unit_of_measure: 'pieces',
        unit_cost: 0.85,
        supplier: 'Packaging Solutions Ltd.',
        description: 'Clear glass bottles with screw caps for beverage packaging',
        is_active: true,
        created_at: '2024-02-10T09:45:00Z',
        updated_at: '2024-02-10T09:45:00Z'
      },
      {
        id: 'demo_item_5',
        name: 'Natural Lemon Oil',
        sku: 'LEM-005',
        category_id: 'demo_cat_1',
        current_stock: 3.8,
        minimum_stock: 5.0,
        unit_of_measure: 'liters',
        unit_cost: 45.00,
        supplier: 'Essential Oils Direct',
        description: 'Cold-pressed natural lemon oil for citrus flavoring',
        is_active: true,
        created_at: '2024-02-15T11:20:00Z',
        updated_at: '2024-02-15T11:20:00Z'
      }
    ]
  }

  static async createInventoryItem(item: Omit<InventoryItem, 'id' | 'created_at' | 'updated_at'>): Promise<InventoryItem | null> {
    try {
      const { data, error } = await supabase
        .from('inventory_items')
        .insert([item])
        .select()
        .single()

      if (error) {
        console.error('Error creating inventory item:', error)
        // Only use demo fallback for connection errors
        if (error.message.includes('Failed to fetch') || error.message.includes('network')) {
          return this.createDemoInventoryItem(item)
        }
        return null
      }

      return data
    } catch (error) {
      console.error('Database connection error:', error)
      return this.createDemoInventoryItem(item)
    }
  }

  static createDemoInventoryItem(item: Omit<InventoryItem, 'id' | 'created_at' | 'updated_at'>): InventoryItem {
    return {
      ...item,
      id: `demo_item_${Date.now()}`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  }

  static async updateInventoryItem(id: string, updates: Partial<InventoryItem>): Promise<InventoryItem | null> {
    const { data, error } = await supabase
      .from('inventory_items')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating inventory item:', error)
      return null
    }

    return data
  }

  // Inventory Categories
  static async getInventoryCategories(): Promise<InventoryCategory[]> {
    try {
      const { data, error } = await supabase
        .from('inventory_categories')
        .select('*')
        .order('name')

      if (error) {
        console.error('Error fetching inventory categories:', error)
        // Only return demo data if it's a connection error
        if (error.message.includes('Failed to fetch') || error.message.includes('network')) {
          return this.getDemoInventoryCategories()
        }
        return []
      }

      // Return actual data from database (even if empty)
      return data || []
    } catch (error) {
      console.error('Database connection error:', error)
      return this.getDemoInventoryCategories()
    }
  }

  static getDemoInventoryCategories(): InventoryCategory[] {
    return [
      {
        id: 'demo_cat_1',
        name: 'Flavoring Agents',
        description: 'Natural and artificial flavoring compounds and extracts',
        is_active: true
      },
      {
        id: 'demo_cat_2',
        name: 'Preservatives & Additives',
        description: 'Food-grade preservatives, stabilizers, and additives',
        is_active: true
      },
      {
        id: 'demo_cat_3',
        name: 'Packaging Materials',
        description: 'Bottles, caps, labels, and packaging supplies',
        is_active: true
      },
      {
        id: 'demo_cat_4',
        name: 'Raw Materials',
        description: 'Base ingredients and raw materials for production',
        is_active: true
      },
      {
        id: 'demo_cat_5',
        name: 'Quality Control',
        description: 'Testing materials and quality control supplies',
        is_active: true
      }
    ]
  }

  // Recipes
  static async getRecipes(): Promise<Recipe[]> {
    const { data, error } = await supabase
      .from('recipes')
      .select('*')
      .eq('is_active', true)
      .order('name')

    if (error) {
      console.error('Error fetching recipes:', error)
      return []
    }

    return data || []
  }

  static async createRecipe(recipe: Omit<Recipe, 'id' | 'created_at' | 'updated_at'>): Promise<Recipe | null> {
    const { data, error } = await supabase
      .from('recipes')
      .insert([recipe])
      .select()
      .single()

    if (error) {
      console.error('Error creating recipe:', error)
      return null
    }

    return data
  }

  // Production Batches
  static async getProductionBatches(): Promise<ProductionBatch[]> {
    const { data, error } = await supabase
      .from('production_batches')
      .select(`
        *,
        recipes(name, code)
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching production batches:', error)
      return []
    }

    return data || []
  }

  static async createProductionBatch(batch: Omit<ProductionBatch, 'id' | 'created_at' | 'updated_at'>): Promise<ProductionBatch | null> {
    const { data, error } = await supabase
      .from('production_batches')
      .insert([batch])
      .select()
      .single()

    if (error) {
      console.error('Error creating production batch:', error)
      return null
    }

    return data
  }

  // Quality Documents
  static async getQualityDocuments(): Promise<QualityDocument[]> {
    const { data, error } = await supabase
      .from('quality_documents')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching quality documents:', error)
      return []
    }

    return data || []
  }

  static async createQualityDocument(doc: Omit<QualityDocument, 'id' | 'created_at' | 'updated_at'>): Promise<QualityDocument | null> {
    try {
      const { data, error } = await supabase
        .from('quality_documents')
        .insert([doc])
        .select()
        .single()

      if (error) {
        console.error('Error creating quality document:', error)
        // Only use demo fallback for connection errors
        if (error.message.includes('Failed to fetch') || error.message.includes('network')) {
          return {
            ...doc,
            id: `demo_doc_${Date.now()}`,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        }
        return null
      }

      return data
    } catch (error) {
      console.error('Database connection error:', error)
      return {
        ...doc,
        id: `demo_doc_${Date.now()}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    }
  }

  // User Management
  static async getUsers(): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        // Silently handle database errors and return demo users
        // This is expected when the database is empty or has constraints
        return this.getDemoUsers()
      }

      // If no users in database, return demo users
      if (!data || data.length === 0) {
        return this.getDemoUsers()
      }

      return data
    } catch (error) {
      // Silently handle connection errors and return demo users
      return this.getDemoUsers()
    }
  }

  static getDemoUsers(): any[] {
    return [
      {
        id: 'demo_admin',
        username: 'admin',
        full_name: 'System Administrator',
        email: '<EMAIL>',
        role: 'admin',
        department: 'IT',
        phone: '+****************',
        is_active: true,
        created_at: '2024-01-15T08:00:00Z',
        updated_at: '2024-01-15T08:00:00Z'
      },
      {
        id: 'demo_quality',
        username: 'quality',
        full_name: 'Sarah Johnson',
        email: '<EMAIL>',
        role: 'quality_manager',
        department: 'Quality Assurance',
        phone: '+****************',
        is_active: true,
        created_at: '2024-02-01T10:00:00Z',
        updated_at: '2024-02-01T10:00:00Z'
      },
      {
        id: 'demo_production',
        username: 'production',
        full_name: 'Mike Wilson',
        email: '<EMAIL>',
        role: 'production_manager',
        department: 'Production',
        phone: '+****************',
        is_active: true,
        created_at: '2024-02-15T14:30:00Z',
        updated_at: '2024-02-15T14:30:00Z'
      },
      {
        id: 'demo_employee',
        username: 'employee',
        full_name: 'Emily Davis',
        email: '<EMAIL>',
        role: 'employee',
        department: 'Production',
        phone: '+****************',
        is_active: true,
        created_at: '2024-03-01T09:00:00Z',
        updated_at: '2024-03-01T09:00:00Z'
      }
    ]
  }

  static async createUser(userData: {
    username: string
    email: string
    password_hash: string
    role: string
    full_name: string
    department: string
    phone?: string
    is_active: boolean
  }): Promise<any | null> {
    try {
      // For demo purposes, since we can't create auth users directly,
      // we'll simulate user creation and return a demo user object
      const newUser = {
        id: `demo_${Date.now()}`,
        username: userData.username,
        email: userData.email,
        full_name: userData.full_name,
        role: userData.role,
        department: userData.department,
        phone: userData.phone || null,
        is_active: userData.is_active,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      // In a real implementation, this would create the user in the database
      // For now, we'll just return the user object for demo purposes
      return newUser

      // Commented out actual database insertion due to auth constraints
      /*
      const { data, error } = await supabase
        .from('profiles')
        .insert([{
          ...userData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single()

      if (error) {
        console.error('Error creating user:', error)
        return null
      }

      return data
      */
    } catch (error) {
      return null
    }
  }

  static async updateUser(id: string, updates: any): Promise<any | null> {
    const { data, error } = await supabase
      .from('profiles')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating user:', error)
      return null
    }

    return data
  }

  static async toggleUserStatus(id: string, isActive: boolean): Promise<boolean> {
    try {
      // For demo users (those with demo_ prefix), just return success
      if (id.startsWith('demo_')) {
        return true
      }

      const { error } = await supabase
        .from('profiles')
        .update({ is_active: isActive, updated_at: new Date().toISOString() })
        .eq('id', id)

      if (error) {
        return false
      }

      return true
    } catch (error) {
      return false
    }
  }
}
