{"name": "sff-management-system", "version": "1.0.0", "description": "SFF Production & Quality Management System - Desktop Application", "main": "electron/main.js", "homepage": "./", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"", "build-electron": "npm run build && npm run export && electron-builder", "export": "next export", "dist": "npm run build && npm run export && electron-builder --publish=never", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps", "desktop": "node run-desktop.js", "build-desktop": "node build-desktop.js", "quick-start": "npm run desktop"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.50.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.514.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.3", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.1.2", "electron": "^36.4.0", "electron-builder": "^26.0.12", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5", "wait-on": "^8.0.3"}, "build": {"appId": "com.sff.management-system", "productName": "SFF Management System", "directories": {"output": "dist"}, "files": ["out/**/*", "electron/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "public", "to": "public", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "icon": "public/icon.ico", "publisherName": "SFF Company", "verifyUpdateCodeSignature": false}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "public/icon.icns", "category": "public.app-category.business"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "public/icon.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "SFF Management System"}}}