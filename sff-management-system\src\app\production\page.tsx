'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { useAuth } from '@/components/providers'
import { Modal } from '@/components/ui/modal'
import { DatabaseService } from '@/lib/database'
import {
  PlusIcon,
  CogIcon,
  ClockIcon,
  CheckCircleIcon,
  TableCellsIcon,
  ExclamationTriangleIcon,
  DocumentArrowDownIcon,
  ChartBarIcon,
  BeakerIcon,
  CalendarIcon,
  UserIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline'

interface ProductionBatch {
  id: string
  batch_number: string
  recipe_id: string
  recipe_name: string
  batch_type: 'test' | 'production'
  planned_quantity: number
  actual_quantity?: number
  unit_of_measure: string
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'
  priority: 'low' | 'normal' | 'high' | 'urgent'
  planned_start_date: string
  actual_start_date?: string
  planned_end_date: string
  actual_end_date?: string
  assigned_to: string
  progress: number
  estimated_cost: number
  actual_cost?: number
  yield_percentage?: number
  quality_status?: 'pending' | 'passed' | 'failed' | 'requires_retest'
  notes?: string
}

interface Recipe {
  id: string
  name: string
  code: string
  category: string
  batch_size: number
  unit_of_measure: string
  estimated_cost: number
  ingredients_count: number
  version: number
  is_active: boolean
}

interface InventoryItem {
  id: string
  name: string
  sku: string
  current_stock: number
  minimum_stock: number
  unit_of_measure: string
  unit_cost: number
  category_name?: string
}

interface MaterialAllocation {
  id: string
  inventory_item_id: string
  inventory_item_name: string
  required_quantity: number
  allocated_quantity: number
  unit_of_measure: string
  unit_cost: number
  total_cost: number
  availability_status: 'available' | 'insufficient' | 'unavailable'
}

interface BatchSpreadsheetData {
  batch_number: string
  recipe_name: string
  batch_type: string
  planned_quantity: number
  actual_quantity: number
  status: string
  priority: string
  assigned_to: string
  planned_start: string
  planned_end: string
  actual_start: string
  actual_end: string
  estimated_cost: number
  actual_cost: number
  yield_percentage: number
  quality_status: string
  progress: number
  materials_allocated: boolean
  notes: string
}

export default function ProductionPage() {
  const { user } = useAuth()
  const [batches, setBatches] = useState<ProductionBatch[]>([])
  const [recipes, setRecipes] = useState<Recipe[]>([])
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [showSpreadsheetView, setShowSpreadsheetView] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)

  // Batch creation state
  const [newBatch, setNewBatch] = useState({
    recipe_id: '',
    batch_type: 'production' as 'test' | 'production',
    planned_quantity: 0,
    priority: 'normal' as 'low' | 'normal' | 'high' | 'urgent',
    planned_start_date: '',
    planned_end_date: '',
    assigned_to: '',
    notes: ''
  })

  // Material allocation state
  const [materialAllocations, setMaterialAllocations] = useState<MaterialAllocation[]>([])
  const [totalMaterialCost, setTotalMaterialCost] = useState(0)
  const [allMaterialsAvailable, setAllMaterialsAvailable] = useState(false)
  const [validationErrors, setValidationErrors] = useState<string[]>([])

  // Spreadsheet data
  const [spreadsheetData, setSpreadsheetData] = useState<BatchSpreadsheetData[]>([])
  const [selectedBatches, setSelectedBatches] = useState<string[]>([])
  const [sortField, setSortField] = useState<keyof BatchSpreadsheetData>('batch_number')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true)

        // Load production batches, recipes, and inventory from database
        const [batchData, recipeData, inventoryData] = await Promise.all([
          DatabaseService.getProductionBatches(),
          DatabaseService.getRecipes(),
          DatabaseService.getInventoryItems()
        ])

        // Process data
        const processedBatches = batchData.map(batch => ({
          ...batch,
          estimated_cost: parseFloat(batch.estimated_cost?.toString() || '0'),
          actual_cost: parseFloat(batch.actual_cost?.toString() || '0'),
          yield_percentage: parseFloat(batch.yield_percentage?.toString() || '100')
        }))

        const processedInventory = inventoryData.map(item => ({
          ...item,
          current_stock: parseFloat(item.current_stock.toString()),
          unit_cost: parseFloat(item.unit_cost.toString())
        }))

        setBatches(processedBatches)
        setRecipes(recipeData)
        setInventoryItems(processedInventory)

        // Generate spreadsheet data
        generateSpreadsheetData(processedBatches)

        // Set default values for new batch
        if (recipeData.length > 0 && !newBatch.recipe_id) {
          const firstRecipe = recipeData[0]
          setNewBatch(prev => ({
            ...prev,
            recipe_id: firstRecipe.id,
            planned_quantity: firstRecipe.batch_size
          }))
        }
      } catch (error) {
        console.error('Error loading production data:', error)
        // Fallback to mock data
        const mockBatches: ProductionBatch[] = [
          {
            id: '1',
            batch_number: 'SFF-20241211-001',
            recipe_id: '1',
            recipe_name: 'Strawberry Vanilla Blend',
            batch_type: 'production',
            planned_quantity: 10.0,
            actual_quantity: 9.8,
            unit_of_measure: 'liters',
            status: 'completed',
            priority: 'normal',
            planned_start_date: '2024-12-11T08:00:00Z',
            actual_start_date: '2024-12-11T08:15:00Z',
            planned_end_date: '2024-12-11T16:00:00Z',
            actual_end_date: '2024-12-11T15:45:00Z',
            assigned_to: 'John Smith',
            progress: 100,
            estimated_cost: 185.50,
            actual_cost: 178.25,
            yield_percentage: 98,
            quality_status: 'passed'
          }
        ]
        setBatches(mockBatches)
        generateSpreadsheetData(mockBatches)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  // Generate spreadsheet data from batches
  const generateSpreadsheetData = (batchList: ProductionBatch[]) => {
    const spreadsheetRows: BatchSpreadsheetData[] = batchList.map(batch => ({
      batch_number: batch.batch_number,
      recipe_name: batch.recipe_name,
      batch_type: batch.batch_type,
      planned_quantity: batch.planned_quantity,
      actual_quantity: batch.actual_quantity || 0,
      status: batch.status,
      priority: batch.priority,
      assigned_to: batch.assigned_to,
      planned_start: batch.planned_start_date,
      planned_end: batch.planned_end_date,
      actual_start: batch.actual_start_date || '',
      actual_end: batch.actual_end_date || '',
      estimated_cost: batch.estimated_cost,
      actual_cost: batch.actual_cost || 0,
      yield_percentage: batch.yield_percentage || 100,
      quality_status: batch.quality_status || 'pending',
      progress: batch.progress,
      materials_allocated: materialAllocations.length > 0,
      notes: batch.notes || ''
    }))
    setSpreadsheetData(spreadsheetRows)
  }

  // Calculate material requirements for selected recipe
  const calculateMaterialRequirements = (recipeId: string, quantity: number) => {
    const recipe = recipes.find(r => r.id === recipeId)
    if (!recipe) return []

    // Mock recipe ingredients - in real implementation, get from recipe_ingredients table
    const mockIngredients = [
      { inventory_item_id: '1', quantity_per_batch: 2.5 },
      { inventory_item_id: '2', quantity_per_batch: 1.0 },
      { inventory_item_id: '3', quantity_per_batch: 0.5 }
    ]

    const scaleFactor = quantity / recipe.batch_size
    const allocations: MaterialAllocation[] = []

    mockIngredients.forEach(ingredient => {
      const inventoryItem = inventoryItems.find(item => item.id === ingredient.inventory_item_id)
      if (inventoryItem) {
        const requiredQuantity = ingredient.quantity_per_batch * scaleFactor
        const totalCost = requiredQuantity * inventoryItem.unit_cost

        allocations.push({
          id: Date.now().toString() + Math.random(),
          inventory_item_id: inventoryItem.id,
          inventory_item_name: inventoryItem.name,
          required_quantity: requiredQuantity,
          allocated_quantity: Math.min(requiredQuantity, inventoryItem.current_stock),
          unit_of_measure: inventoryItem.unit_of_measure,
          unit_cost: inventoryItem.unit_cost,
          total_cost: totalCost,
          availability_status: inventoryItem.current_stock >= requiredQuantity ? 'available' :
                              inventoryItem.current_stock > 0 ? 'insufficient' : 'unavailable'
        })
      }
    })

    return allocations
  }

  // Update material allocations when recipe or quantity changes
  useEffect(() => {
    if (newBatch.recipe_id && newBatch.planned_quantity > 0) {
      const allocations = calculateMaterialRequirements(newBatch.recipe_id, newBatch.planned_quantity)
      setMaterialAllocations(allocations)

      const totalCost = allocations.reduce((sum, alloc) => sum + alloc.total_cost, 0)
      setTotalMaterialCost(totalCost)

      const allAvailable = allocations.every(alloc => alloc.availability_status === 'available')
      setAllMaterialsAvailable(allAvailable)
    }
  }, [newBatch.recipe_id, newBatch.planned_quantity, inventoryItems])

  const generateBatchNumber = () => {
    const today = new Date()
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '')
    const sequence = String(batches.length + 1).padStart(3, '0')
    return `SFF-${dateStr}-${sequence}`
  }

  const validateBatch = () => {
    const errors: string[] = []

    if (!newBatch.recipe_id) errors.push('Recipe selection is required')
    if (newBatch.planned_quantity <= 0) errors.push('Planned quantity must be greater than 0')
    if (!newBatch.planned_start_date) errors.push('Planned start date is required')
    if (!newBatch.planned_end_date) errors.push('Planned end date is required')
    if (!newBatch.assigned_to) errors.push('Operator assignment is required')

    if (new Date(newBatch.planned_end_date) <= new Date(newBatch.planned_start_date)) {
      errors.push('End date must be after start date')
    }

    if (!allMaterialsAvailable) {
      errors.push('Insufficient materials available for this batch')
    }

    setValidationErrors(errors)
    return errors.length === 0
  }

  const handleCreateBatch = async () => {
    if (!validateBatch()) return

    try {
      const selectedRecipe = recipes.find(r => r.id === newBatch.recipe_id)
      if (!selectedRecipe) return

      const batch: ProductionBatch = {
        id: (batches.length + 1).toString(),
        batch_number: generateBatchNumber(),
        recipe_id: newBatch.recipe_id,
        recipe_name: selectedRecipe.name,
        batch_type: newBatch.batch_type,
        planned_quantity: newBatch.planned_quantity,
        unit_of_measure: selectedRecipe.unit_of_measure,
        status: 'planned',
        priority: newBatch.priority,
        planned_start_date: newBatch.planned_start_date,
        planned_end_date: newBatch.planned_end_date,
        assigned_to: newBatch.assigned_to,
        progress: 0,
        estimated_cost: totalMaterialCost,
        quality_status: 'pending',
        notes: newBatch.notes
      }

      // In real implementation, save to database and allocate materials
      setBatches([...batches, batch])
      generateSpreadsheetData([...batches, batch])

      resetForm()
      setShowAddModal(false)

      alert(`Batch ${batch.batch_number} created successfully!\nEstimated Cost: $${totalMaterialCost.toFixed(2)}\nMaterials: ${materialAllocations.length} items allocated`)
    } catch (error) {
      console.error('Error creating batch:', error)
      alert('Error creating batch. Please try again.')
    }
  }

  const resetForm = () => {
    setCurrentStep(1)
    setNewBatch({
      recipe_id: recipes.length > 0 ? recipes[0].id : '',
      batch_type: 'production',
      planned_quantity: recipes.length > 0 ? recipes[0].batch_size : 0,
      priority: 'normal',
      planned_start_date: '',
      planned_end_date: '',
      assigned_to: '',
      notes: ''
    })
    setMaterialAllocations([])
    setValidationErrors([])
  }

  const nextStep = () => {
    if (currentStep < 3) setCurrentStep(currentStep + 1)
  }

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1)
  }

  // Export spreadsheet data
  const exportToSpreadsheet = () => {
    const csvContent = [
      // Headers
      Object.keys(spreadsheetData[0] || {}).join(','),
      // Data rows
      ...spreadsheetData.map(row => Object.values(row).map(val =>
        typeof val === 'string' && val.includes(',') ? `"${val}"` : val
      ).join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `production-batches-${new Date().toISOString().slice(0, 10)}.csv`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800'
      case 'planned':
        return 'bg-gray-100 text-gray-800'
      case 'on_hold':
        return 'bg-yellow-100 text-yellow-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800'
      case 'high':
        return 'bg-orange-100 text-orange-800'
      case 'normal':
        return 'bg-blue-100 text-blue-800'
      case 'low':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (!user) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Production Management</h1>
            <p className="mt-2 text-sm text-gray-700">
              Professional batch management with inventory integration and spreadsheet analytics
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex space-x-3">
            <button
              type="button"
              onClick={() => setShowSpreadsheetView(!showSpreadsheetView)}
              className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium ${
                showSpreadsheetView
                  ? 'text-white bg-green-600 hover:bg-green-700'
                  : 'text-gray-700 bg-white hover:bg-gray-50'
              }`}
            >
              <TableCellsIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
              {showSpreadsheetView ? 'Card View' : 'Spreadsheet View'}
            </button>
            <button
              type="button"
              onClick={exportToSpreadsheet}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <DocumentArrowDownIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
              Export CSV
            </button>
            <button
              type="button"
              onClick={() => { resetForm(); setShowAddModal(true); }}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
              New Batch
            </button>
          </div>
        </div>

        {/* Enhanced Stats Cards */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-6">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CogIcon className="h-6 w-6 text-blue-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">In Progress</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {batches.filter(b => b.status === 'in_progress').length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ClockIcon className="h-6 w-6 text-gray-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Planned</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {batches.filter(b => b.status === 'planned').length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CheckCircleIcon className="h-6 w-6 text-green-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Completed</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {batches.filter(b => b.status === 'completed').length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <BeakerIcon className="h-6 w-6 text-purple-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Test Batches</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {batches.filter(b => b.batch_type === 'test').length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CurrencyDollarIcon className="h-6 w-6 text-green-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Value</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      ${batches.reduce((sum, b) => sum + (b.estimated_cost || 0), 0).toFixed(0)}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-6 w-6 text-orange-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Avg Yield</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {batches.length > 0
                        ? (batches.reduce((sum, b) => sum + (b.yield_percentage || 100), 0) / batches.length).toFixed(1)
                        : '0'}%
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Production Batches - Conditional View */}
        {showSpreadsheetView ? (
          /* Professional Spreadsheet View */
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Production Spreadsheet ({spreadsheetData.length} batches)
                </h3>
                <div className="flex space-x-2">
                  <select
                    className="text-sm border-gray-300 rounded-md"
                    value={`${sortField}-${sortDirection}`}
                    onChange={(e) => {
                      const [field, direction] = e.target.value.split('-')
                      setSortField(field as keyof BatchSpreadsheetData)
                      setSortDirection(direction as 'asc' | 'desc')
                    }}
                  >
                    <option value="batch_number-desc">Batch Number (Newest)</option>
                    <option value="batch_number-asc">Batch Number (Oldest)</option>
                    <option value="status-asc">Status (A-Z)</option>
                    <option value="priority-desc">Priority (High-Low)</option>
                    <option value="estimated_cost-desc">Cost (High-Low)</option>
                    <option value="planned_start-desc">Start Date (Recent)</option>
                  </select>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 text-xs">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Batch #</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Recipe</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Type</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Planned Qty</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Actual Qty</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Assigned</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">End Date</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Est. Cost</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Actual Cost</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Yield %</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Quality</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Progress</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Materials</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {spreadsheetData
                      .sort((a, b) => {
                        const aVal = a[sortField]
                        const bVal = b[sortField]
                        if (sortDirection === 'asc') {
                          return aVal < bVal ? -1 : aVal > bVal ? 1 : 0
                        } else {
                          return aVal > bVal ? -1 : aVal < bVal ? 1 : 0
                        }
                      })
                      .map((row, index) => (
                        <tr key={index} className="hover:bg-gray-50">
                          <td className="px-2 py-2 whitespace-nowrap font-medium text-gray-900">{row.batch_number}</td>
                          <td className="px-2 py-2 whitespace-nowrap text-gray-900">{row.recipe_name}</td>
                          <td className="px-2 py-2 whitespace-nowrap">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              row.batch_type === 'test' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'
                            }`}>
                              {row.batch_type}
                            </span>
                          </td>
                          <td className="px-2 py-2 whitespace-nowrap text-gray-900">{row.planned_quantity}</td>
                          <td className="px-2 py-2 whitespace-nowrap text-gray-900">{row.actual_quantity || '-'}</td>
                          <td className="px-2 py-2 whitespace-nowrap">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(row.status)}`}>
                              {row.status.replace('_', ' ').toUpperCase()}
                            </span>
                          </td>
                          <td className="px-2 py-2 whitespace-nowrap">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(row.priority)}`}>
                              {row.priority.toUpperCase()}
                            </span>
                          </td>
                          <td className="px-2 py-2 whitespace-nowrap text-gray-900">{row.assigned_to}</td>
                          <td className="px-2 py-2 whitespace-nowrap text-gray-900">
                            {row.planned_start ? formatDate(row.planned_start) : '-'}
                          </td>
                          <td className="px-2 py-2 whitespace-nowrap text-gray-900">
                            {row.planned_end ? formatDate(row.planned_end) : '-'}
                          </td>
                          <td className="px-2 py-2 whitespace-nowrap text-gray-900">${row.estimated_cost.toFixed(2)}</td>
                          <td className="px-2 py-2 whitespace-nowrap text-gray-900">
                            {row.actual_cost > 0 ? `$${row.actual_cost.toFixed(2)}` : '-'}
                          </td>
                          <td className="px-2 py-2 whitespace-nowrap text-gray-900">{row.yield_percentage.toFixed(1)}%</td>
                          <td className="px-2 py-2 whitespace-nowrap">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              row.quality_status === 'passed' ? 'bg-green-100 text-green-800' :
                              row.quality_status === 'failed' ? 'bg-red-100 text-red-800' :
                              row.quality_status === 'requires_retest' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {row.quality_status}
                            </span>
                          </td>
                          <td className="px-2 py-2 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="w-12 bg-gray-200 rounded-full h-1.5 mr-1">
                                <div
                                  className="bg-green-600 h-1.5 rounded-full"
                                  style={{ width: `${row.progress}%` }}
                                ></div>
                              </div>
                              <span className="text-xs">{row.progress}%</span>
                            </div>
                          </td>
                          <td className="px-2 py-2 whitespace-nowrap">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              row.materials_allocated ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {row.materials_allocated ? 'Allocated' : 'Pending'}
                            </span>
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        ) : (
          /* Traditional Card View */
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Production Batches ({batches.length})
              </h3>
            
            {loading ? (
              <div className="animate-pulse">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-4 py-4">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Batch
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Recipe
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Quantity
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Priority
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Progress
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Assigned To
                      </th>
                      <th className="relative px-6 py-3">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {batches.map((batch) => (
                      <tr key={batch.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{batch.batch_number}</div>
                            <div className="text-sm text-gray-500">
                              {batch.batch_type === 'test' ? 'Test Batch' : 'Production'}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {batch.recipe_name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {batch.actual_quantity || batch.planned_quantity} {batch.unit_of_measure}
                          </div>
                          {batch.actual_quantity && (
                            <div className="text-sm text-gray-500">
                              Planned: {batch.planned_quantity}
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(batch.status)}`}>
                            {batch.status.replace('_', ' ').toUpperCase()}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(batch.priority)}`}>
                            {batch.priority.toUpperCase()}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                              <div 
                                className="bg-green-600 h-2 rounded-full" 
                                style={{ width: `${batch.progress}%` }}
                              ></div>
                            </div>
                            <span className="text-sm text-gray-900">{batch.progress}%</span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {batch.assigned_to}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button className="text-green-600 hover:text-green-900 mr-3">
                            View
                          </button>
                          <button className="text-green-600 hover:text-green-900">
                            Edit
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
        )}

        {/* Professional Batch Creation Modal */}
        <Modal
          isOpen={showAddModal}
          onClose={() => { resetForm(); setShowAddModal(false); }}
          title={`Create Production Batch - Step ${currentStep} of 3`}
          maxWidth="2xl"
        >
          <div className="space-y-6">
            {/* Progress Steps */}
            <div className="flex items-center justify-between">
              {[1, 2, 3].map((step) => (
                <div key={step} className="flex items-center">
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                    step <= currentStep
                      ? 'bg-green-600 border-green-600 text-white'
                      : 'border-gray-300 text-gray-500'
                  }`}>
                    {step < currentStep ? (
                      <CheckCircleIcon className="w-5 h-5" />
                    ) : (
                      <span className="text-sm font-medium">{step}</span>
                    )}
                  </div>
                  <div className="ml-2 text-sm font-medium text-gray-900">
                    {step === 1 && 'Recipe & Schedule'}
                    {step === 2 && 'Material Allocation'}
                    {step === 3 && 'Review & Create'}
                  </div>
                  {step < 3 && (
                    <div className={`ml-4 w-16 h-0.5 ${
                      step < currentStep ? 'bg-green-600' : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              ))}
            </div>

            {/* Validation Errors */}
            {validationErrors.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">Please fix the following errors:</h3>
                    <ul className="mt-2 text-sm text-red-700 list-disc list-inside">
                      {validationErrors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {/* Step Content */}
            <div className="min-h-96">
              {currentStep === 1 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Recipe Selection & Scheduling</h3>
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Recipe *</label>
                      <select
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newBatch.recipe_id}
                        onChange={(e) => setNewBatch({ ...newBatch, recipe_id: e.target.value })}
                      >
                        <option value="">Select a recipe...</option>
                        {recipes.map(recipe => (
                          <option key={recipe.id} value={recipe.id}>
                            {recipe.name} (Standard: {recipe.batch_size} {recipe.unit_of_measure})
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Batch Type</label>
                      <select
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newBatch.batch_type}
                        onChange={(e) => setNewBatch({ ...newBatch, batch_type: e.target.value as 'test' | 'production' })}
                      >
                        <option value="production">Production Batch</option>
                        <option value="test">Test Batch</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Planned Quantity *</label>
                      <input
                        type="number"
                        min="0.1"
                        step="0.1"
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newBatch.planned_quantity}
                        onChange={(e) => setNewBatch({ ...newBatch, planned_quantity: parseFloat(e.target.value) || 0 })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Priority</label>
                      <select
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newBatch.priority}
                        onChange={(e) => setNewBatch({ ...newBatch, priority: e.target.value as any })}
                      >
                        <option value="low">Low Priority</option>
                        <option value="normal">Normal Priority</option>
                        <option value="high">High Priority</option>
                        <option value="urgent">Urgent</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Planned Start Date *</label>
                      <input
                        type="datetime-local"
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newBatch.planned_start_date}
                        onChange={(e) => setNewBatch({ ...newBatch, planned_start_date: e.target.value })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Planned End Date *</label>
                      <input
                        type="datetime-local"
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newBatch.planned_end_date}
                        onChange={(e) => setNewBatch({ ...newBatch, planned_end_date: e.target.value })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Assigned Operator *</label>
                      <select
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newBatch.assigned_to}
                        onChange={(e) => setNewBatch({ ...newBatch, assigned_to: e.target.value })}
                      >
                        <option value="">Select operator...</option>
                        <option value="John Smith">John Smith</option>
                        <option value="Sarah Johnson">Sarah Johnson</option>
                        <option value="Mike Wilson">Mike Wilson</option>
                        <option value="Emily Davis">Emily Davis</option>
                      </select>
                    </div>
                    <div className="sm:col-span-2">
                      <label className="block text-sm font-medium text-gray-700">Production Notes</label>
                      <textarea
                        rows={3}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newBatch.notes}
                        onChange={(e) => setNewBatch({ ...newBatch, notes: e.target.value })}
                        placeholder="Special instructions, quality requirements, etc..."
                      />
                    </div>
                  </div>
                </div>
              )}

              {currentStep === 2 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Material Allocation & Inventory Check</h3>

                  {materialAllocations.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <BeakerIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <p className="mt-2">Select a recipe and quantity in Step 1 to see material requirements</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {/* Material Allocation Summary */}
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-medium text-gray-900 mb-3">Material Requirements Summary</h4>
                        <div className="grid grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="font-medium">Total Materials:</span> {materialAllocations.length}
                          </div>
                          <div>
                            <span className="font-medium">Total Cost:</span> ${totalMaterialCost.toFixed(2)}
                          </div>
                          <div>
                            <span className={`font-medium ${allMaterialsAvailable ? 'text-green-600' : 'text-red-600'}`}>
                              Status: {allMaterialsAvailable ? 'All Available' : 'Insufficient Stock'}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Material Allocation Table */}
                      <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                        <table className="min-w-full divide-y divide-gray-300">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Material</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Required</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Available</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Cost</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Cost</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {materialAllocations.map((allocation) => (
                              <tr key={allocation.id}>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                  {allocation.inventory_item_name}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {allocation.required_quantity} {allocation.unit_of_measure}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {allocation.allocated_quantity} {allocation.unit_of_measure}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  ${allocation.unit_cost.toFixed(2)}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  ${allocation.total_cost.toFixed(2)}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                    allocation.availability_status === 'available'
                                      ? 'bg-green-100 text-green-800'
                                      : allocation.availability_status === 'insufficient'
                                      ? 'bg-yellow-100 text-yellow-800'
                                      : 'bg-red-100 text-red-800'
                                  }`}>
                                    {allocation.availability_status}
                                  </span>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {currentStep === 3 && (
                <div className="space-y-6">
                  <h3 className="text-lg font-medium text-gray-900">Review & Create Batch</h3>

                  {/* Batch Summary */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-3">Batch Summary</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div><span className="font-medium">Recipe:</span> {recipes.find(r => r.id === newBatch.recipe_id)?.name}</div>
                      <div><span className="font-medium">Batch Type:</span> {newBatch.batch_type}</div>
                      <div><span className="font-medium">Quantity:</span> {newBatch.planned_quantity} {recipes.find(r => r.id === newBatch.recipe_id)?.unit_of_measure}</div>
                      <div><span className="font-medium">Priority:</span> {newBatch.priority}</div>
                      <div><span className="font-medium">Start Date:</span> {newBatch.planned_start_date ? new Date(newBatch.planned_start_date).toLocaleString() : 'Not set'}</div>
                      <div><span className="font-medium">End Date:</span> {newBatch.planned_end_date ? new Date(newBatch.planned_end_date).toLocaleString() : 'Not set'}</div>
                      <div><span className="font-medium">Assigned To:</span> {newBatch.assigned_to}</div>
                      <div><span className="font-medium">Estimated Cost:</span> ${totalMaterialCost.toFixed(2)}</div>
                    </div>
                  </div>

                  {/* Material Summary */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-3">Material Allocation ({materialAllocations.length} items)</h4>
                    <div className="space-y-2">
                      {materialAllocations.map((allocation) => (
                        <div key={allocation.id} className="flex justify-between text-sm">
                          <span>{allocation.inventory_item_name}</span>
                          <span>{allocation.required_quantity} {allocation.unit_of_measure} (${allocation.total_cost.toFixed(2)})</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Final Validation */}
                  <div className={`p-4 rounded-lg ${allMaterialsAvailable ? 'bg-green-50' : 'bg-red-50'}`}>
                    <div className="flex">
                      {allMaterialsAvailable ? (
                        <CheckCircleIcon className="h-5 w-5 text-green-400" />
                      ) : (
                        <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                      )}
                      <div className="ml-3">
                        <h3 className={`text-sm font-medium ${allMaterialsAvailable ? 'text-green-800' : 'text-red-800'}`}>
                          {allMaterialsAvailable ? 'Ready to Create Batch' : 'Material Shortage Detected'}
                        </h3>
                        <p className={`mt-1 text-sm ${allMaterialsAvailable ? 'text-green-700' : 'text-red-700'}`}>
                          {allMaterialsAvailable
                            ? 'All required materials are available. The batch can be created and materials will be allocated.'
                            : 'Some materials have insufficient stock. Please check inventory or adjust batch quantity.'
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={currentStep === 1 ? () => { resetForm(); setShowAddModal(false); } : prevStep}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                {currentStep === 1 ? 'Cancel' : 'Previous'}
              </button>

              <div className="flex space-x-3">
                {currentStep < 3 ? (
                  <button
                    type="button"
                    onClick={nextStep}
                    disabled={currentStep === 1 && (!newBatch.recipe_id || newBatch.planned_quantity <= 0)}
                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400"
                  >
                    Next Step
                  </button>
                ) : (
                  <button
                    type="button"
                    onClick={handleCreateBatch}
                    disabled={!allMaterialsAvailable}
                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400"
                  >
                    Create Batch
                  </button>
                )}
              </div>
            </div>
          </div>
        </Modal>
      </div>
    </MainLayout>
  )
}
