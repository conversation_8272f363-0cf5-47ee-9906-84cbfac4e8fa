'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { useAuth } from '@/components/providers'
import { DatabaseService } from '@/lib/database'
import { 
  DocumentTextIcon,
  CloudArrowUpIcon,
  CalendarIcon,
  UserIcon,
  BuildingOfficeIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowLeftIcon,
  DocumentDuplicateIcon,
  EyeIcon,
  PrinterIcon
} from '@heroicons/react/24/outline'
import { useRouter } from 'next/navigation'

interface DocumentFormData {
  document_type: 'msds' | 'coa' | 'tds' | 'quality_spec' | 'lab_report'
  title: string
  document_number: string
  version: string
  item_id: string
  batch_id: string
  status: 'draft' | 'review' | 'approved' | 'expired'
  valid_from: string
  valid_until: string
  description: string
  content: string
  attachments: File[]
  metadata: {
    author: string
    department: string
    reviewer: string
    approver: string
    keywords: string[]
    classification: 'public' | 'internal' | 'confidential' | 'restricted'
  }
}

export default function NewDocumentPage() {
  const { user } = useAuth()
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [isCreating, setIsCreating] = useState(false)
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const [previewMode, setPreviewMode] = useState(false)
  
  const [formData, setFormData] = useState<DocumentFormData>({
    document_type: 'quality_spec',
    title: '',
    document_number: '',
    version: '1.0',
    item_id: '',
    batch_id: '',
    status: 'draft',
    valid_from: new Date().toISOString().split('T')[0],
    valid_until: '',
    description: '',
    content: '',
    attachments: [],
    metadata: {
      author: user?.full_name || '',
      department: user?.department || '',
      reviewer: '',
      approver: '',
      keywords: [],
      classification: 'internal'
    }
  })

  // Auto-generate document number
  const generateDocumentNumber = () => {
    const typeCode = {
      'msds': 'MSDS',
      'coa': 'COA',
      'tds': 'TDS',
      'quality_spec': 'QS',
      'lab_report': 'LAB'
    }[formData.document_type]
    
    const date = new Date().toISOString().slice(0, 10).replace(/-/g, '')
    const sequence = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
    return `${typeCode}-${date}-${sequence}`
  }

  useEffect(() => {
    if (!formData.document_number) {
      setFormData(prev => ({
        ...prev,
        document_number: generateDocumentNumber()
      }))
    }
  }, [formData.document_type])

  // Validation function
  const validateForm = () => {
    const errors: string[] = []
    
    if (!formData.title.trim()) errors.push('Document title is required')
    if (!formData.document_number.trim()) errors.push('Document number is required')
    if (!formData.version.trim()) errors.push('Version is required')
    if (!formData.description.trim()) errors.push('Description is required')
    if (!formData.content.trim()) errors.push('Document content is required')
    if (!formData.valid_from) errors.push('Valid from date is required')
    if (!formData.metadata.author.trim()) errors.push('Author is required')
    if (!formData.metadata.department.trim()) errors.push('Department is required')
    
    if (formData.valid_until && new Date(formData.valid_until) <= new Date(formData.valid_from)) {
      errors.push('Valid until date must be after valid from date')
    }
    
    if (formData.title.length < 5) errors.push('Title must be at least 5 characters')
    if (formData.content.length < 50) errors.push('Content must be at least 50 characters')
    
    setValidationErrors(errors)
    return errors.length === 0
  }

  const handleSubmit = async () => {
    if (!validateForm()) return

    try {
      setIsCreating(true)
      
      const documentData = {
        ...formData,
        created_by: user?.id || 'demo_user',
        metadata: JSON.stringify(formData.metadata)
      }

      const createdDocument = await DatabaseService.createQualityDocument(documentData)

      if (createdDocument) {
        alert(`✅ Document "${formData.title}" created successfully!\n\n📋 Document Details:\n• Number: ${formData.document_number}\n• Type: ${getDocumentTypeName(formData.document_type)}\n• Version: ${formData.version}\n• Status: ${formData.status.toUpperCase()}\n• Author: ${formData.metadata.author}`)
        router.push('/documents')
      } else {
        alert('❌ Failed to create document. Please try again.')
      }
    } catch (error) {
      alert('❌ Error creating document. Please check the form and try again.')
    } finally {
      setIsCreating(false)
    }
  }

  const getDocumentTypeName = (type: string) => {
    const names = {
      'msds': 'Material Safety Data Sheet',
      'coa': 'Certificate of Analysis',
      'tds': 'Technical Data Sheet',
      'quality_spec': 'Quality Specification',
      'lab_report': 'Laboratory Report'
    }
    return names[type as keyof typeof names] || type
  }

  const handleFileUpload = (files: FileList | null) => {
    if (files) {
      const newFiles = Array.from(files)
      setFormData(prev => ({
        ...prev,
        attachments: [...prev.attachments, ...newFiles]
      }))
    }
  }

  const removeAttachment = (index: number) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index)
    }))
  }

  const addKeyword = (keyword: string) => {
    if (keyword.trim() && !formData.metadata.keywords.includes(keyword.trim())) {
      setFormData(prev => ({
        ...prev,
        metadata: {
          ...prev.metadata,
          keywords: [...prev.metadata.keywords, keyword.trim()]
        }
      }))
    }
  }

  const removeKeyword = (index: number) => {
    setFormData(prev => ({
      ...prev,
      metadata: {
        ...prev.metadata,
        keywords: prev.metadata.keywords.filter((_, i) => i !== index)
      }
    }))
  }

  if (!user) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => router.push('/documents')}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Documents
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Create New Document</h1>
              <p className="mt-2 text-sm text-gray-700">
                Professional document creation with comprehensive metadata and version control
              </p>
            </div>
          </div>
          <div className="mt-4 sm:mt-0 flex space-x-3">
            <button
              onClick={() => setPreviewMode(!previewMode)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <EyeIcon className="h-4 w-4 mr-2" />
              {previewMode ? 'Edit Mode' : 'Preview'}
            </button>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between">
            {[1, 2, 3, 4].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                  step <= currentStep 
                    ? 'bg-green-600 border-green-600 text-white' 
                    : 'border-gray-300 text-gray-500'
                }`}>
                  {step < currentStep ? (
                    <CheckCircleIcon className="w-5 h-5" />
                  ) : (
                    <span className="text-sm font-medium">{step}</span>
                  )}
                </div>
                <div className="ml-2 text-sm font-medium text-gray-900">
                  {step === 1 && 'Document Type & Basic Info'}
                  {step === 2 && 'Content & Description'}
                  {step === 3 && 'Metadata & Classification'}
                  {step === 4 && 'Review & Submit'}
                </div>
                {step < 4 && (
                  <div className={`ml-4 w-16 h-0.5 ${
                    step < currentStep ? 'bg-green-600' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Please fix the following errors:</h3>
                <ul className="mt-2 text-sm text-red-700 list-disc list-inside">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Main Form */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-8">
            {/* Step 1: Document Type & Basic Info */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">Document Type & Basic Information</h3>

                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Document Type *</label>
                    <select
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                      value={formData.document_type}
                      onChange={(e) => setFormData({ ...formData, document_type: e.target.value as any })}
                    >
                      <option value="quality_spec">Quality Specification</option>
                      <option value="msds">Material Safety Data Sheet (MSDS)</option>
                      <option value="coa">Certificate of Analysis (COA)</option>
                      <option value="tds">Technical Data Sheet (TDS)</option>
                      <option value="lab_report">Laboratory Report</option>
                    </select>
                    <p className="mt-1 text-xs text-gray-500">Select the type of document you want to create</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Document Number *</label>
                    <div className="mt-1 flex rounded-md shadow-sm">
                      <input
                        type="text"
                        className="flex-1 block w-full rounded-l-md border-gray-300 focus:border-green-500 focus:ring-green-500"
                        value={formData.document_number}
                        onChange={(e) => setFormData({ ...formData, document_number: e.target.value })}
                        placeholder="e.g., QS-20241211-001"
                      />
                      <button
                        type="button"
                        onClick={() => setFormData({ ...formData, document_number: generateDocumentNumber() })}
                        className="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 text-sm hover:bg-gray-100"
                      >
                        <DocumentDuplicateIcon className="h-4 w-4" />
                      </button>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">Unique identifier for this document</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Document Title *</label>
                    <input
                      type="text"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                      value={formData.title}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                      placeholder="e.g., Vanilla Extract Quality Specification"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Version *</label>
                    <input
                      type="text"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                      value={formData.version}
                      onChange={(e) => setFormData({ ...formData, version: e.target.value })}
                      placeholder="e.g., 1.0, 2.1, 3.0"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Status</label>
                    <select
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                      value={formData.status}
                      onChange={(e) => setFormData({ ...formData, status: e.target.value as any })}
                    >
                      <option value="draft">Draft</option>
                      <option value="review">Under Review</option>
                      <option value="approved">Approved</option>
                      <option value="expired">Expired</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Valid From *</label>
                    <input
                      type="date"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                      value={formData.valid_from}
                      onChange={(e) => setFormData({ ...formData, valid_from: e.target.value })}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Valid Until</label>
                    <input
                      type="date"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                      value={formData.valid_until}
                      onChange={(e) => setFormData({ ...formData, valid_until: e.target.value })}
                    />
                  </div>

                  <div className="sm:col-span-2">
                    <label className="block text-sm font-medium text-gray-700">Description *</label>
                    <textarea
                      rows={3}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Brief description of the document purpose and scope..."
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Content & Description */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">Document Content</h3>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Document Content *</label>
                  <div className="mt-1">
                    <textarea
                      rows={20}
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 font-mono text-sm"
                      value={formData.content}
                      onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                      placeholder={`Enter the detailed content for your ${getDocumentTypeName(formData.document_type)}...

Example content structure:
1. Purpose and Scope
2. Specifications
3. Test Methods
4. Acceptance Criteria
5. References

You can use markdown formatting for better structure.`}
                    />
                  </div>
                  <p className="mt-2 text-sm text-gray-500">
                    Enter the complete document content. You can use markdown formatting for headers, lists, and emphasis.
                  </p>
                </div>

                {/* File Attachments */}
                <div>
                  <label className="block text-sm font-medium text-gray-700">Attachments</label>
                  <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                    <div className="space-y-1 text-center">
                      <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="flex text-sm text-gray-600">
                        <label className="relative cursor-pointer bg-white rounded-md font-medium text-green-600 hover:text-green-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-green-500">
                          <span>Upload files</span>
                          <input
                            type="file"
                            className="sr-only"
                            multiple
                            onChange={(e) => handleFileUpload(e.target.files)}
                          />
                        </label>
                        <p className="pl-1">or drag and drop</p>
                      </div>
                      <p className="text-xs text-gray-500">PDF, DOC, XLS, IMG up to 10MB each</p>
                    </div>
                  </div>

                  {/* Attachment List */}
                  {formData.attachments.length > 0 && (
                    <div className="mt-4">
                      <h4 className="text-sm font-medium text-gray-900">Attached Files:</h4>
                      <ul className="mt-2 divide-y divide-gray-200">
                        {formData.attachments.map((file, index) => (
                          <li key={index} className="py-2 flex items-center justify-between">
                            <div className="flex items-center">
                              <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-2" />
                              <span className="text-sm text-gray-900">{file.name}</span>
                              <span className="text-xs text-gray-500 ml-2">({(file.size / 1024 / 1024).toFixed(2)} MB)</span>
                            </div>
                            <button
                              onClick={() => removeAttachment(index)}
                              className="text-red-600 hover:text-red-800 text-sm"
                            >
                              Remove
                            </button>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Step 3: Metadata & Classification */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">Metadata & Classification</h3>

                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Author *</label>
                    <div className="mt-1 relative">
                      <UserIcon className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <input
                        type="text"
                        className="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={formData.metadata.author}
                        onChange={(e) => setFormData({
                          ...formData,
                          metadata: { ...formData.metadata, author: e.target.value }
                        })}
                        placeholder="Document author name"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Department *</label>
                    <div className="mt-1 relative">
                      <BuildingOfficeIcon className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <input
                        type="text"
                        className="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={formData.metadata.department}
                        onChange={(e) => setFormData({
                          ...formData,
                          metadata: { ...formData.metadata, department: e.target.value }
                        })}
                        placeholder="e.g., Quality Assurance"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Reviewer</label>
                    <input
                      type="text"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                      value={formData.metadata.reviewer}
                      onChange={(e) => setFormData({
                        ...formData,
                        metadata: { ...formData.metadata, reviewer: e.target.value }
                      })}
                      placeholder="Document reviewer name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Approver</label>
                    <input
                      type="text"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                      value={formData.metadata.approver}
                      onChange={(e) => setFormData({
                        ...formData,
                        metadata: { ...formData.metadata, approver: e.target.value }
                      })}
                      placeholder="Document approver name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Classification</label>
                    <select
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                      value={formData.metadata.classification}
                      onChange={(e) => setFormData({
                        ...formData,
                        metadata: { ...formData.metadata, classification: e.target.value as any }
                      })}
                    >
                      <option value="public">Public</option>
                      <option value="internal">Internal</option>
                      <option value="confidential">Confidential</option>
                      <option value="restricted">Restricted</option>
                    </select>
                  </div>

                  <div className="sm:col-span-2">
                    <label className="block text-sm font-medium text-gray-700">Keywords</label>
                    <div className="mt-1">
                      <input
                        type="text"
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        placeholder="Type a keyword and press Enter"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault()
                            addKeyword(e.currentTarget.value)
                            e.currentTarget.value = ''
                          }
                        }}
                      />
                    </div>
                    <p className="mt-1 text-xs text-gray-500">Press Enter to add keywords for better searchability</p>

                    {/* Keywords Display */}
                    {formData.metadata.keywords.length > 0 && (
                      <div className="mt-3 flex flex-wrap gap-2">
                        {formData.metadata.keywords.map((keyword, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                          >
                            {keyword}
                            <button
                              type="button"
                              onClick={() => removeKeyword(index)}
                              className="ml-1 text-green-600 hover:text-green-800"
                            >
                              ×
                            </button>
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Step 4: Review & Submit */}
            {currentStep === 4 && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">Review & Submit</h3>

                {/* Document Summary */}
                <div className="bg-gray-50 rounded-lg p-6">
                  <h4 className="text-lg font-medium text-gray-900 mb-4">Document Summary</h4>
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <span className="text-sm font-medium text-gray-500">Type:</span>
                      <p className="text-sm text-gray-900">{getDocumentTypeName(formData.document_type)}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-500">Document Number:</span>
                      <p className="text-sm text-gray-900">{formData.document_number}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-500">Title:</span>
                      <p className="text-sm text-gray-900">{formData.title}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-500">Version:</span>
                      <p className="text-sm text-gray-900">{formData.version}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-500">Status:</span>
                      <p className="text-sm text-gray-900 capitalize">{formData.status}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-500">Author:</span>
                      <p className="text-sm text-gray-900">{formData.metadata.author}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-500">Valid From:</span>
                      <p className="text-sm text-gray-900">{formData.valid_from}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-500">Valid Until:</span>
                      <p className="text-sm text-gray-900">{formData.valid_until || 'No expiration'}</p>
                    </div>
                    <div className="sm:col-span-2">
                      <span className="text-sm font-medium text-gray-500">Description:</span>
                      <p className="text-sm text-gray-900">{formData.description}</p>
                    </div>
                    <div className="sm:col-span-2">
                      <span className="text-sm font-medium text-gray-500">Content Length:</span>
                      <p className="text-sm text-gray-900">{formData.content.length} characters</p>
                    </div>
                    <div className="sm:col-span-2">
                      <span className="text-sm font-medium text-gray-500">Attachments:</span>
                      <p className="text-sm text-gray-900">{formData.attachments.length} files</p>
                    </div>
                    {formData.metadata.keywords.length > 0 && (
                      <div className="sm:col-span-2">
                        <span className="text-sm font-medium text-gray-500">Keywords:</span>
                        <p className="text-sm text-gray-900">{formData.metadata.keywords.join(', ')}</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Final Confirmation */}
                <div className="bg-green-50 border border-green-200 rounded-md p-4">
                  <div className="flex">
                    <CheckCircleIcon className="h-5 w-5 text-green-400" />
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-green-800">Ready to Create Document</h3>
                      <div className="mt-2 text-sm text-green-700">
                        <p>
                          Your document is ready to be created. Please review all information above and click "Create Document" to proceed.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={() => {
                  if (currentStep === 1) {
                    router.push('/documents')
                  } else {
                    setCurrentStep(currentStep - 1)
                  }
                }}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                disabled={isCreating}
              >
                {currentStep === 1 ? 'Cancel' : 'Previous'}
              </button>

              <div className="flex space-x-3">
                {currentStep < 4 ? (
                  <button
                    type="button"
                    onClick={() => setCurrentStep(currentStep + 1)}
                    disabled={
                      (currentStep === 1 && (!formData.title || !formData.document_number || !formData.description)) ||
                      (currentStep === 2 && !formData.content) ||
                      (currentStep === 3 && (!formData.metadata.author || !formData.metadata.department))
                    }
                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400"
                  >
                    Next Step
                  </button>
                ) : (
                  <button
                    type="button"
                    onClick={handleSubmit}
                    disabled={isCreating}
                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400"
                  >
                    {isCreating ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Creating Document...
                      </>
                    ) : (
                      <>
                        <CheckCircleIcon className="-ml-1 mr-2 h-4 w-4" />
                        Create Document
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
