'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { useAuth } from '@/components/providers'
import { Modal } from '@/components/ui/modal'
import {
  PlusIcon,
  DocumentTextIcon,
  FolderIcon,
  MagnifyingGlassIcon,
  CloudArrowUpIcon,
  DocumentArrowDownIcon
} from '@heroicons/react/24/outline'

interface Document {
  id: string
  name: string
  type: string
  category: 'procedures' | 'policies' | 'forms' | 'certificates' | 'manuals' | 'other'
  size: string
  uploaded_by: string
  uploaded_at: string
  last_modified: string
  version: string
  status: 'active' | 'archived' | 'draft'
}

export default function DocumentsPage() {
  const { user } = useAuth()
  const [documents, setDocuments] = useState<Document[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'procedures' | 'policies' | 'forms' | 'certificates' | 'manuals' | 'other'>('all')
  const [showUploadModal, setShowUploadModal] = useState(false)
  const [newDocument, setNewDocument] = useState({
    name: '',
    category: 'procedures' as 'procedures' | 'policies' | 'forms' | 'certificates' | 'manuals' | 'other',
    version: '1.0'
  })

  useEffect(() => {
    // Mock documents data
    const mockDocuments: Document[] = [
      {
        id: '1',
        name: 'Standard Operating Procedures - Production',
        type: 'PDF',
        category: 'procedures',
        size: '2.4 MB',
        uploaded_by: 'Sarah Johnson',
        uploaded_at: '2024-12-01T10:00:00Z',
        last_modified: '2024-12-10T14:30:00Z',
        version: '3.2',
        status: 'active'
      },
      {
        id: '2',
        name: 'Quality Management Policy',
        type: 'PDF',
        category: 'policies',
        size: '1.8 MB',
        uploaded_by: 'Mike Wilson',
        uploaded_at: '2024-11-15T09:00:00Z',
        last_modified: '2024-11-20T16:45:00Z',
        version: '2.1',
        status: 'active'
      },
      {
        id: '3',
        name: 'Batch Production Record Form',
        type: 'DOCX',
        category: 'forms',
        size: '156 KB',
        uploaded_by: 'Emily Davis',
        uploaded_at: '2024-12-05T11:30:00Z',
        last_modified: '2024-12-05T11:30:00Z',
        version: '1.0',
        status: 'active'
      },
      {
        id: '4',
        name: 'ISO 9001:2015 Certificate',
        type: 'PDF',
        category: 'certificates',
        size: '892 KB',
        uploaded_by: 'John Smith',
        uploaded_at: '2024-01-15T08:00:00Z',
        last_modified: '2024-01-15T08:00:00Z',
        version: '1.0',
        status: 'active'
      },
      {
        id: '5',
        name: 'Equipment Maintenance Manual',
        type: 'PDF',
        category: 'manuals',
        size: '5.2 MB',
        uploaded_by: 'Sarah Johnson',
        uploaded_at: '2024-10-20T13:15:00Z',
        last_modified: '2024-11-05T10:20:00Z',
        version: '4.0',
        status: 'active'
      },
      {
        id: '6',
        name: 'HACCP Implementation Guide',
        type: 'PDF',
        category: 'procedures',
        size: '3.1 MB',
        uploaded_by: 'Mike Wilson',
        uploaded_at: '2024-09-10T14:00:00Z',
        last_modified: '2024-09-10T14:00:00Z',
        version: '1.5',
        status: 'active'
      },
      {
        id: '7',
        name: 'Supplier Qualification Form',
        type: 'XLSX',
        category: 'forms',
        size: '245 KB',
        uploaded_by: 'Emily Davis',
        uploaded_at: '2024-11-28T09:45:00Z',
        last_modified: '2024-12-02T15:10:00Z',
        version: '2.0',
        status: 'active'
      },
      {
        id: '8',
        name: 'Environmental Policy Statement',
        type: 'PDF',
        category: 'policies',
        size: '678 KB',
        uploaded_by: 'John Smith',
        uploaded_at: '2024-08-15T12:00:00Z',
        last_modified: '2024-08-15T12:00:00Z',
        version: '1.0',
        status: 'archived'
      }
    ]

    setTimeout(() => {
      setDocuments(mockDocuments)
      setLoading(false)
    }, 1000)
  }, [])

  const handleUploadDocument = () => {
    const doc: Document = {
      id: (documents.length + 1).toString(),
      name: newDocument.name,
      type: 'PDF',
      category: newDocument.category,
      size: `${Math.floor(Math.random() * 5000) + 100} KB`,
      uploaded_by: user?.username || 'Current User',
      uploaded_at: new Date().toISOString(),
      last_modified: new Date().toISOString(),
      version: newDocument.version,
      status: 'active'
    }

    setDocuments([...documents, doc])
    setShowUploadModal(false)
    setNewDocument({
      name: '',
      category: 'procedures',
      version: '1.0'
    })
  }

  const categories = [
    { key: 'all', name: 'All Documents', count: documents.length },
    { key: 'procedures', name: 'Procedures', count: documents.filter(d => d.category === 'procedures').length },
    { key: 'policies', name: 'Policies', count: documents.filter(d => d.category === 'policies').length },
    { key: 'forms', name: 'Forms', count: documents.filter(d => d.category === 'forms').length },
    { key: 'certificates', name: 'Certificates', count: documents.filter(d => d.category === 'certificates').length },
    { key: 'manuals', name: 'Manuals', count: documents.filter(d => d.category === 'manuals').length }
  ]

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.uploaded_by.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesCategory = selectedCategory === 'all' || doc.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  const getFileIcon = (type: string) => {
    return DocumentTextIcon // Simplified for demo
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'procedures':
        return 'bg-blue-100 text-blue-800'
      case 'policies':
        return 'bg-green-100 text-green-800'
      case 'forms':
        return 'bg-yellow-100 text-yellow-800'
      case 'certificates':
        return 'bg-purple-100 text-purple-800'
      case 'manuals':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'archived':
        return 'bg-gray-100 text-gray-800'
      case 'draft':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (!user) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Document Management</h1>
            <p className="mt-2 text-sm text-gray-700">
              Organize and manage your company documents, procedures, and policies
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button
              type="button"
              onClick={() => setShowUploadModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <CloudArrowUpIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
              Upload Document
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DocumentTextIcon className="h-6 w-6 text-blue-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Documents</dt>
                    <dd className="text-lg font-medium text-gray-900">{documents.length}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <FolderIcon className="h-6 w-6 text-green-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Categories</dt>
                    <dd className="text-lg font-medium text-gray-900">6</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CloudArrowUpIcon className="h-6 w-6 text-purple-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">This Month</dt>
                    <dd className="text-lg font-medium text-gray-900">3</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DocumentArrowDownIcon className="h-6 w-6 text-yellow-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Downloads</dt>
                    <dd className="text-lg font-medium text-gray-900">142</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div className="relative flex-1 max-w-lg">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-green-500 focus:border-green-500"
                placeholder="Search documents..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <div className="flex space-x-4">
              {categories.map((category) => (
                <button
                  key={category.key}
                  onClick={() => setSelectedCategory(category.key as any)}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    selectedCategory === category.key
                      ? 'bg-green-100 text-green-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  {category.name} ({category.count})
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Documents Table */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Documents ({filteredDocuments.length})
            </h3>
            
            {loading ? (
              <div className="animate-pulse">
                {[...Array(8)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-4 py-4">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Document
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Category
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Size
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Modified
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Uploaded By
                      </th>
                      <th className="relative px-6 py-3">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredDocuments.map((doc) => {
                      const IconComponent = getFileIcon(doc.type)
                      return (
                        <tr key={doc.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <IconComponent className="h-5 w-5 text-gray-400 mr-3" />
                              <div>
                                <div className="text-sm font-medium text-gray-900">{doc.name}</div>
                                <div className="text-sm text-gray-500">
                                  {doc.type} • v{doc.version}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${getCategoryColor(doc.category)}`}>
                              {doc.category}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {doc.size}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${getStatusColor(doc.status)}`}>
                              {doc.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatDate(doc.last_modified)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {doc.uploaded_by}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button className="text-green-600 hover:text-green-900 mr-3">
                              Download
                            </button>
                            <button className="text-green-600 hover:text-green-900">
                              View
                            </button>
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        {/* Upload Document Modal */}
        <Modal
          isOpen={showUploadModal}
          onClose={() => setShowUploadModal(false)}
          title="Upload New Document"
          maxWidth="lg"
        >
          <form onSubmit={(e) => { e.preventDefault(); handleUploadDocument(); }} className="space-y-4">
            <div className="space-y-4">
              <div>
                <label htmlFor="doc_name" className="block text-sm font-medium text-gray-700">
                  Document Name
                </label>
                <input
                  type="text"
                  id="doc_name"
                  required
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newDocument.name}
                  onChange={(e) => setNewDocument({ ...newDocument, name: e.target.value })}
                />
              </div>
              <div>
                <label htmlFor="doc_category" className="block text-sm font-medium text-gray-700">
                  Category
                </label>
                <select
                  id="doc_category"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newDocument.category}
                  onChange={(e) => setNewDocument({ ...newDocument, category: e.target.value as any })}
                >
                  <option value="procedures">Procedures</option>
                  <option value="policies">Policies</option>
                  <option value="forms">Forms</option>
                  <option value="certificates">Certificates</option>
                  <option value="manuals">Manuals</option>
                  <option value="other">Other</option>
                </select>
              </div>
              <div>
                <label htmlFor="doc_version" className="block text-sm font-medium text-gray-700">
                  Version
                </label>
                <input
                  type="text"
                  id="doc_version"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newDocument.version}
                  onChange={(e) => setNewDocument({ ...newDocument, version: e.target.value })}
                />
              </div>
              <div>
                <label htmlFor="file_upload" className="block text-sm font-medium text-gray-700">
                  File
                </label>
                <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                  <div className="space-y-1 text-center">
                    <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <div className="flex text-sm text-gray-600">
                      <label
                        htmlFor="file_upload"
                        className="relative cursor-pointer bg-white rounded-md font-medium text-green-600 hover:text-green-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-green-500"
                      >
                        <span>Upload a file</span>
                        <input id="file_upload" name="file_upload" type="file" className="sr-only" />
                      </label>
                      <p className="pl-1">or drag and drop</p>
                    </div>
                    <p className="text-xs text-gray-500">PDF, DOC, DOCX up to 10MB</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={() => setShowUploadModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700"
              >
                Upload Document
              </button>
            </div>
          </form>
        </Modal>
      </div>
    </MainLayout>
  )
}
