{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "yzIgNZxZMehAJ1xaBrSnG0hr1IqTol7cjVjPnBwrwmg=", "__NEXT_PREVIEW_MODE_ID": "f5f1e2376b00b810ff92099b657842d2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "bcce3d13efaf97d338410d03dc220a18cd1713755880b1527d6c134ce20151a1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b9d37fa19509a31232309d98061102764fb5573119a1f9edc956736bc70bc401"}}}, "sortedMiddleware": ["/"], "functions": {}}