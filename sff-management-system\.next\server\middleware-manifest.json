{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "XLOw3JS4ULmP1tflsxcO6nogTmQ8Qw/3mz+crmtUIO8=", "__NEXT_PREVIEW_MODE_ID": "92eec0ae8ad377de5d51c5ce2ec22736", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "56d2378079f375df7b8b400a536af40b943f85032a0f6119c7f1b16738e1ae18", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "af30572a5e80f47317f3f8247189c477d0efed77643c62bfcfbfd1de35900ba4"}}}, "sortedMiddleware": ["/"], "functions": {}}