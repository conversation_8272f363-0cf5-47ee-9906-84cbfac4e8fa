{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_9e474c67._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_d813fedf.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "y/UocKN9tm3wdN/XuI5uaWtL30p+cCtQC2LFILwaF1s=", "__NEXT_PREVIEW_MODE_ID": "e4c0656272ef9c75e6c2c6411584e28f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "bafdb66ccab6b2f14dc4dd44c986a742bb6dab752c0ba146b1c76786863b9ac4", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f15abae7bb6b7c89397804cdb157cbc6d472c02fd6720b239bf01b79197f476f"}}}, "sortedMiddleware": ["/"], "functions": {}}