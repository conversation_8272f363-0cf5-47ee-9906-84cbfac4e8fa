{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "XLOw3JS4ULmP1tflsxcO6nogTmQ8Qw/3mz+crmtUIO8=", "__NEXT_PREVIEW_MODE_ID": "cb4566aedc6c26c00fe6306f34a3c9d8", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "68aa80fe3f761d8ff6025beff6cecc035f392f691285bd84d3b8759108e7e727", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7e352458f8acced3ad977c48d5262f04950cc33b6e0d3a11910c571d0cd986ad"}}}, "sortedMiddleware": ["/"], "functions": {}}