{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "XLOw3JS4ULmP1tflsxcO6nogTmQ8Qw/3mz+crmtUIO8=", "__NEXT_PREVIEW_MODE_ID": "fd8a67357cd728270b9f20a309d099c2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d4e826188bb170d3ef7a055b5fe50bc851cad78b2af05417d33de01222e14e73", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "be52fc9e1b9c68f157d81a79ff20b2a1720b729317541b2959f368d0dd8cd259"}}}, "sortedMiddleware": ["/"], "functions": {}}