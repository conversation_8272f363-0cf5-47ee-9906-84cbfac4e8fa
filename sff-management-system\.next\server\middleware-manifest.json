{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "yzIgNZxZMehAJ1xaBrSnG0hr1IqTol7cjVjPnBwrwmg=", "__NEXT_PREVIEW_MODE_ID": "e847ce66e219ea497db7a19639722e34", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0c50f2be7f306cb8c59ca5e2485f60bb4b15b83a73f3f4487ee89fd80333def8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4f2635036ece6a1819a0024751571f37e00d0f94f9497952a0155288bde32dd6"}}}, "sortedMiddleware": ["/"], "functions": {}}