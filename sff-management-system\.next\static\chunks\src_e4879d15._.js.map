{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\nimport {\n  HomeIcon,\n  CubeIcon,\n  BeakerIcon,\n  CogIcon,\n  DocumentTextIcon,\n  UsersIcon,\n  ChartBarIcon,\n  ClipboardDocumentListIcon,\n} from '@heroicons/react/24/outline'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { clsx } from 'clsx'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/', icon: HomeIcon },\n  { name: 'Inventory', href: '/inventory', icon: CubeIcon },\n  { name: 'Recipes', href: '/recipes', icon: ClipboardDocumentListIcon },\n  { name: 'Production', href: '/production', icon: CogIcon },\n  { name: 'Quality', href: '/quality', icon: BeakerIcon },\n  { name: 'Reports', href: '/reports', icon: ChartBarIcon },\n  { name: 'Documents', href: '/documents', icon: DocumentTextIcon },\n  { name: 'Users', href: '/users', icon: UsersIcon },\n]\n\ninterface SidebarProps {\n  open: boolean\n  setOpen: (open: boolean) => void\n}\n\nexport function Sidebar({ open, setOpen }: SidebarProps) {\n  const pathname = usePathname()\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <Transition.Root show={open} as={Fragment}>\n        <Dialog as=\"div\" className=\"relative z-50 lg:hidden\" onClose={setOpen}>\n          <Transition.Child\n            as={Fragment}\n            enter=\"transition-opacity ease-linear duration-300\"\n            enterFrom=\"opacity-0\"\n            enterTo=\"opacity-100\"\n            leave=\"transition-opacity ease-linear duration-300\"\n            leaveFrom=\"opacity-100\"\n            leaveTo=\"opacity-0\"\n          >\n            <div className=\"fixed inset-0 bg-gray-900/80\" />\n          </Transition.Child>\n\n          <div className=\"fixed inset-0 flex\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"transition ease-in-out duration-300 transform\"\n              enterFrom=\"-translate-x-full\"\n              enterTo=\"translate-x-0\"\n              leave=\"transition ease-in-out duration-300 transform\"\n              leaveFrom=\"translate-x-0\"\n              leaveTo=\"-translate-x-full\"\n            >\n              <Dialog.Panel className=\"relative mr-16 flex w-full max-w-xs flex-1\">\n                <Transition.Child\n                  as={Fragment}\n                  enter=\"ease-in-out duration-300\"\n                  enterFrom=\"opacity-0\"\n                  enterTo=\"opacity-100\"\n                  leave=\"ease-in-out duration-300\"\n                  leaveFrom=\"opacity-100\"\n                  leaveTo=\"opacity-0\"\n                >\n                  <div className=\"absolute left-full top-0 flex w-16 justify-center pt-5\">\n                    <button type=\"button\" className=\"-m-2.5 p-2.5\" onClick={() => setOpen(false)}>\n                      <span className=\"sr-only\">Close sidebar</span>\n                      <XMarkIcon className=\"h-6 w-6 text-white\" aria-hidden=\"true\" />\n                    </button>\n                  </div>\n                </Transition.Child>\n                <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4\">\n                  <div className=\"flex h-16 shrink-0 items-center\">\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">SFF</span>\n                    </div>\n                    <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n                  </div>\n                  <nav className=\"flex flex-1 flex-col\">\n                    <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n                      <li>\n                        <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                          {navigation.map((item) => (\n                            <li key={item.name}>\n                              <Link\n                                href={item.href}\n                                className={clsx(\n                                  pathname === item.href\n                                    ? 'bg-green-50 text-green-700'\n                                    : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                                  'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                                )}\n                              >\n                                <item.icon\n                                  className={clsx(\n                                    pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                                    'h-6 w-6 shrink-0'\n                                  )}\n                                  aria-hidden=\"true\"\n                                />\n                                {item.name}\n                              </Link>\n                            </li>\n                          ))}\n                        </ul>\n                      </li>\n                    </ul>\n                  </nav>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </Dialog>\n      </Transition.Root>\n\n      {/* Static sidebar for desktop */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 bg-white px-6 pb-4\">\n          <div className=\"flex h-16 shrink-0 items-center\">\n            <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-sm\">SFF</span>\n            </div>\n            <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n          </div>\n          <nav className=\"flex flex-1 flex-col\">\n            <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n              <li>\n                <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                  {navigation.map((item) => (\n                    <li key={item.name}>\n                      <Link\n                        href={item.href}\n                        className={clsx(\n                          pathname === item.href\n                            ? 'bg-green-50 text-green-700'\n                            : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                          'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                        )}\n                      >\n                        <item.icon\n                          className={clsx(\n                            pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                            'h-6 w-6 shrink-0'\n                          )}\n                          aria-hidden=\"true\"\n                        />\n                        {item.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </li>\n            </ul>\n          </nav>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;;;AAjBA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,kNAAA,CAAA,WAAQ;IAAC;IAC/C;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,oPAAA,CAAA,4BAAyB;IAAC;IACrE;QAAE,MAAM;QAAc,MAAM;QAAe,MAAM,gNAAA,CAAA,UAAO;IAAC;IACzD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,sNAAA,CAAA,aAAU;IAAC;IACtD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,0NAAA,CAAA,eAAY;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kOAAA,CAAA,mBAAgB;IAAC;IAChE;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,oNAAA,CAAA,YAAS;IAAC;CAClD;AAOM,SAAS,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAgB;;IACrD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE;;0BAEE,6LAAC,0LAAA,CAAA,aAAU,CAAC,IAAI;gBAAC,MAAM;gBAAM,IAAI,6JAAA,CAAA,WAAQ;0BACvC,cAAA,6LAAC,kLAAA,CAAA,SAAM;oBAAC,IAAG;oBAAM,WAAU;oBAA0B,SAAS;;sCAC5D,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,6JAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;sCAGjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;gCACf,IAAI,6JAAA,CAAA,WAAQ;gCACZ,OAAM;gCACN,WAAU;gCACV,SAAQ;gCACR,OAAM;gCACN,WAAU;gCACV,SAAQ;0CAER,cAAA,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;oCAAC,WAAU;;sDACtB,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4CACf,IAAI,6JAAA,CAAA,WAAQ;4CACZ,OAAM;4CACN,WAAU;4CACV,SAAQ;4CACR,OAAM;4CACN,WAAU;4CACV,SAAQ;sDAER,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAO,MAAK;oDAAS,WAAU;oDAAe,SAAS,IAAM,QAAQ;;sEACpE,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;4DAAqB,eAAY;;;;;;;;;;;;;;;;;;;;;;sDAI5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,6LAAC;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;8DAE7D,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAG,MAAK;wDAAO,WAAU;kEACxB,cAAA,6LAAC;sEACC,cAAA,6LAAC;gEAAG,MAAK;gEAAO,WAAU;0EACvB,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;kFACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4EACH,MAAM,KAAK,IAAI;4EACf,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;8FAGF,6LAAC,KAAK,IAAI;oFACR,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;oFAEF,eAAY;;;;;;gFAEb,KAAK,IAAI;;;;;;;uEAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiCxC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAE7D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,MAAK;gCAAO,WAAU;0CACxB,cAAA,6LAAC;8CACC,cAAA,6LAAC;wCAAG,MAAK;wCAAO,WAAU;kDACvB,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;sEAGF,6LAAC,KAAK,IAAI;4DACR,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;4DAEF,eAAY;;;;;;wDAEb,KAAK,IAAI;;;;;;;+CAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BtC;GAtIgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Menu, Transition } from '@headlessui/react'\nimport { Bars3Icon, BellIcon } from '@heroicons/react/24/outline'\nimport { useAuth } from '@/components/providers'\nimport { clsx } from 'clsx'\n\ninterface HeaderProps {\n  setSidebarOpen: (open: boolean) => void\n}\n\nexport function Header({ setSidebarOpen }: HeaderProps) {\n  const { user, signOut } = useAuth()\n\n  const userNavigation = [\n    { name: 'Your profile', href: '#' },\n    { name: 'Settings', href: '#' },\n    { name: 'Sign out', href: '#', onClick: signOut },\n  ]\n\n  return (\n    <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n      <button\n        type=\"button\"\n        className=\"-m-2.5 p-2.5 text-gray-700 lg:hidden\"\n        onClick={() => setSidebarOpen(true)}\n      >\n        <span className=\"sr-only\">Open sidebar</span>\n        <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n      </button>\n\n      {/* Separator */}\n      <div className=\"h-6 w-px bg-gray-200 lg:hidden\" aria-hidden=\"true\" />\n\n      <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n        <div className=\"relative flex flex-1\">\n          {/* Search can be added here later */}\n        </div>\n        <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n          <button type=\"button\" className=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\">\n            <span className=\"sr-only\">View notifications</span>\n            <BellIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n\n          {/* Separator */}\n          <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200\" aria-hidden=\"true\" />\n\n          {/* Profile dropdown */}\n          <Menu as=\"div\" className=\"relative\">\n            <Menu.Button className=\"-m-1.5 flex items-center p-1.5\">\n              <span className=\"sr-only\">Open user menu</span>\n              <div className=\"h-8 w-8 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.username?.charAt(0).toUpperCase() || 'U'}\n                </span>\n              </div>\n              <span className=\"hidden lg:flex lg:items-center\">\n                <span className=\"ml-4 text-sm font-semibold leading-6 text-gray-900\" aria-hidden=\"true\">\n                  {user?.username || 'User'}\n                </span>\n                <span className=\"ml-2 text-xs text-gray-500 capitalize\">\n                  ({user?.role?.replace('_', ' ') || 'Role'})\n                </span>\n              </span>\n            </Menu.Button>\n            <Transition\n              as={Fragment}\n              enter=\"transition ease-out duration-100\"\n              enterFrom=\"transform opacity-0 scale-95\"\n              enterTo=\"transform opacity-100 scale-100\"\n              leave=\"transition ease-in duration-75\"\n              leaveFrom=\"transform opacity-100 scale-100\"\n              leaveTo=\"transform opacity-0 scale-95\"\n            >\n              <Menu.Items className=\"absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none\">\n                {userNavigation.map((item) => (\n                  <Menu.Item key={item.name}>\n                    {({ active }) => (\n                      <button\n                        onClick={item.onClick}\n                        className={clsx(\n                          active ? 'bg-gray-50' : '',\n                          'block w-full text-left px-3 py-1 text-sm leading-6 text-gray-900'\n                        )}\n                      >\n                        {item.name}\n                      </button>\n                    )}\n                  </Menu.Item>\n                ))}\n              </Menu.Items>\n            </Transition>\n          </Menu>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;;;AANA;;;;;;AAYO,SAAS,OAAO,EAAE,cAAc,EAAe;;IACpD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAAgB,MAAM;QAAI;QAClC;YAAE,MAAM;YAAY,MAAM;QAAI;QAC9B;YAAE,MAAM;YAAY,MAAM;YAAK,SAAS;QAAQ;KACjD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,eAAe;;kCAE9B,6LAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;wBAAU,eAAY;;;;;;;;;;;;0BAI7C,6LAAC;gBAAI,WAAU;gBAAiC,eAAY;;;;;;0BAE5D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,MAAK;gCAAS,WAAU;;kDAC9B,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;gCAAgD,eAAY;;;;;;0CAG3E,6LAAC,8KAAA,CAAA,OAAI;gCAAC,IAAG;gCAAM,WAAU;;kDACvB,6LAAC,8KAAA,CAAA,OAAI,CAAC,MAAM;wCAAC,WAAU;;0DACrB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb,MAAM,UAAU,OAAO,GAAG,iBAAiB;;;;;;;;;;;0DAGhD,6LAAC;gDAAK,WAAU;;kEACd,6LAAC;wDAAK,WAAU;wDAAqD,eAAY;kEAC9E,MAAM,YAAY;;;;;;kEAErB,6LAAC;wDAAK,WAAU;;4DAAwC;4DACpD,MAAM,MAAM,QAAQ,KAAK,QAAQ;4DAAO;;;;;;;;;;;;;;;;;;;kDAIhD,6LAAC,0LAAA,CAAA,aAAU;wCACT,IAAI,6JAAA,CAAA,WAAQ;wCACZ,OAAM;wCACN,WAAU;wCACV,SAAQ;wCACR,OAAM;wCACN,WAAU;wCACV,SAAQ;kDAER,cAAA,6LAAC,8KAAA,CAAA,OAAI,CAAC,KAAK;4CAAC,WAAU;sDACnB,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;8DACP,CAAC,EAAE,MAAM,EAAE,iBACV,6LAAC;4DACC,SAAS,KAAK,OAAO;4DACrB,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,SAAS,eAAe,IACxB;sEAGD,KAAK,IAAI;;;;;;mDATA,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqB3C;GAtFgB;;QACY,kIAAA,CAAA,UAAO;;;KADnB", "debugId": null}}, {"offset": {"line": 680, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/components/providers'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const { user } = useAuth()\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Sidebar open={sidebarOpen} setOpen={setSidebarOpen} />\n      \n      <div className=\"lg:pl-72\">\n        <Header setSidebarOpen={setSidebarOpen} />\n        \n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAWO,SAAS,WAAW,EAAE,QAAQ,EAAmB;;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAO;gBAAC,MAAM;gBAAa,SAAS;;;;;;0BAErC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,SAAM;wBAAC,gBAAgB;;;;;;kCAExB,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAnBgB;;QAEG,kIAAA,CAAA,UAAO;;;KAFV", "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createClientComponentClient } from '@supabase/auth-helpers-nextjs'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Client component client\nexport const createSupabaseClient = () => createClientComponentClient()\n\n// Admin client (server-side only)\nexport const createSupabaseAdminClient = () => {\n  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n  return createClient(supabaseUrl, serviceRoleKey, {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  })\n}\n\n// Database types\nexport interface Database {\n  public: {\n    Tables: {\n      profiles: {\n        Row: {\n          id: string\n          email: string\n          full_name: string\n          role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department: string | null\n          phone: string | null\n          is_active: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          email: string\n          full_name: string\n          role?: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department?: string | null\n          phone?: string | null\n          is_active?: boolean\n        }\n        Update: {\n          email?: string\n          full_name?: string\n          role?: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department?: string | null\n          phone?: string | null\n          is_active?: boolean\n        }\n      }\n      inventory_categories: {\n        Row: {\n          id: string\n          name: string\n          description: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          description?: string | null\n        }\n        Update: {\n          name?: string\n          description?: string | null\n        }\n      }\n      inventory_items: {\n        Row: {\n          id: string\n          name: string\n          sku: string\n          category_id: string | null\n          description: string | null\n          unit_of_measure: string\n          current_stock: number\n          minimum_stock: number\n          maximum_stock: number | null\n          unit_cost: number\n          supplier_info: any | null\n          storage_conditions: string | null\n          expiry_date: string | null\n          batch_number: string | null\n          is_active: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          sku: string\n          category_id?: string | null\n          description?: string | null\n          unit_of_measure: string\n          current_stock?: number\n          minimum_stock?: number\n          maximum_stock?: number | null\n          unit_cost?: number\n          supplier_info?: any | null\n          storage_conditions?: string | null\n          expiry_date?: string | null\n          batch_number?: string | null\n          is_active?: boolean\n        }\n        Update: {\n          name?: string\n          sku?: string\n          category_id?: string | null\n          description?: string | null\n          unit_of_measure?: string\n          current_stock?: number\n          minimum_stock?: number\n          maximum_stock?: number | null\n          unit_cost?: number\n          supplier_info?: any | null\n          storage_conditions?: string | null\n          expiry_date?: string | null\n          batch_number?: string | null\n          is_active?: boolean\n        }\n      }\n      recipes: {\n        Row: {\n          id: string\n          name: string\n          code: string\n          description: string | null\n          category: string | null\n          version: number\n          batch_size: number\n          unit_of_measure: string\n          estimated_cost: number | null\n          preparation_time: number | null\n          instructions: string | null\n          notes: string | null\n          is_active: boolean\n          created_by: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          code: string\n          description?: string | null\n          category?: string | null\n          version?: number\n          batch_size: number\n          unit_of_measure: string\n          estimated_cost?: number | null\n          preparation_time?: number | null\n          instructions?: string | null\n          notes?: string | null\n          is_active?: boolean\n          created_by?: string | null\n        }\n        Update: {\n          name?: string\n          code?: string\n          description?: string | null\n          category?: string | null\n          version?: number\n          batch_size?: number\n          unit_of_measure?: string\n          estimated_cost?: number | null\n          preparation_time?: number | null\n          instructions?: string | null\n          notes?: string | null\n          is_active?: boolean\n        }\n      }\n      production_batches: {\n        Row: {\n          id: string\n          batch_number: string\n          recipe_id: string\n          batch_type: 'test' | 'production'\n          planned_quantity: number\n          actual_quantity: number | null\n          unit_of_measure: string\n          status: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date: string | null\n          actual_start_date: string | null\n          planned_end_date: string | null\n          actual_end_date: string | null\n          production_cost: number | null\n          yield_percentage: number | null\n          quality_approved: boolean | null\n          notes: string | null\n          created_by: string | null\n          assigned_to: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          batch_number: string\n          recipe_id: string\n          batch_type: 'test' | 'production'\n          planned_quantity: number\n          actual_quantity?: number | null\n          unit_of_measure: string\n          status?: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority?: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date?: string | null\n          actual_start_date?: string | null\n          planned_end_date?: string | null\n          actual_end_date?: string | null\n          production_cost?: number | null\n          yield_percentage?: number | null\n          quality_approved?: boolean | null\n          notes?: string | null\n          created_by?: string | null\n          assigned_to?: string | null\n        }\n        Update: {\n          batch_number?: string\n          recipe_id?: string\n          batch_type?: 'test' | 'production'\n          planned_quantity?: number\n          actual_quantity?: number | null\n          unit_of_measure?: string\n          status?: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority?: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date?: string | null\n          actual_start_date?: string | null\n          planned_end_date?: string | null\n          actual_end_date?: string | null\n          production_cost?: number | null\n          yield_percentage?: number | null\n          quality_approved?: boolean | null\n          notes?: string | null\n          assigned_to?: string | null\n        }\n      }\n    }\n    Views: {\n      [_ in never]: never\n    }\n    Functions: {\n      check_recipe_availability: {\n        Args: {\n          recipe_uuid: string\n          batch_size_multiplier?: number\n        }\n        Returns: {\n          item_id: string\n          item_name: string\n          required_quantity: number\n          available_quantity: number\n          is_sufficient: boolean\n        }[]\n      }\n      calculate_recipe_cost: {\n        Args: {\n          recipe_uuid: string\n          batch_size_multiplier?: number\n        }\n        Returns: number\n      }\n    }\n    Enums: {\n      user_role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAGoB;AAHpB;AACA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,uBAAuB,IAAM,CAAA,GAAA,2KAAA,CAAA,8BAA2B,AAAD;AAG7D,MAAM,4BAA4B;IACvC,MAAM,iBAAiB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB;IAC5D,OAAO,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa,gBAAgB;QAC/C,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 797, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/dashboard/dashboard-stats.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { createSupabaseClient } from '@/lib/supabase'\nimport {\n  CubeIcon,\n  ClipboardDocumentListIcon,\n  CogIcon,\n  ExclamationTriangleIcon,\n} from '@heroicons/react/24/outline'\n\ninterface Stats {\n  totalItems: number\n  lowStockItems: number\n  activeRecipes: number\n  activeBatches: number\n}\n\nexport function DashboardStats() {\n  const [stats, setStats] = useState<Stats>({\n    totalItems: 0,\n    lowStockItems: 0,\n    activeRecipes: 0,\n    activeBatches: 0,\n  })\n  const [loading, setLoading] = useState(true)\n  const supabase = createSupabaseClient()\n\n  useEffect(() => {\n    async function fetchStats() {\n      try {\n        // For demo purposes, use mock data\n        // In production, you would fetch from your actual database\n        const mockStats = {\n          totalItems: 6,\n          lowStockItems: 3,\n          activeRecipes: 2,\n          activeBatches: 1,\n        }\n\n        // Simulate API delay\n        await new Promise(resolve => setTimeout(resolve, 1000))\n\n        setStats(mockStats)\n      } catch (error) {\n        console.error('Error fetching stats:', error)\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    fetchStats()\n  }, [])\n\n  const statItems = [\n    {\n      name: 'Total Inventory Items',\n      value: stats.totalItems,\n      icon: CubeIcon,\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-100',\n    },\n    {\n      name: 'Low Stock Alerts',\n      value: stats.lowStockItems,\n      icon: ExclamationTriangleIcon,\n      color: 'text-red-600',\n      bgColor: 'bg-red-100',\n    },\n    {\n      name: 'Active Recipes',\n      value: stats.activeRecipes,\n      icon: ClipboardDocumentListIcon,\n      color: 'text-green-600',\n      bgColor: 'bg-green-100',\n    },\n    {\n      name: 'Active Batches',\n      value: stats.activeBatches,\n      icon: CogIcon,\n      color: 'text-purple-600',\n      bgColor: 'bg-purple-100',\n    },\n  ]\n\n  if (loading) {\n    return (\n      <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n        {[...Array(4)].map((_, i) => (\n          <div key={i} className=\"bg-white overflow-hidden shadow rounded-lg animate-pulse\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"h-8 w-8 bg-gray-200 rounded-md\"></div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                  <div className=\"h-6 bg-gray-200 rounded w-1/2\"></div>\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n      {statItems.map((item) => (\n        <div key={item.name} className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className={`p-2 rounded-md ${item.bgColor}`}>\n                  <item.icon className={`h-6 w-6 ${item.color}`} aria-hidden=\"true\" />\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">{item.name}</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{item.value}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;;AAkBO,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;QACxC,YAAY;QACZ,eAAe;QACf,eAAe;QACf,eAAe;IACjB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;IAEpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,eAAe;gBACb,IAAI;oBACF,mCAAmC;oBACnC,2DAA2D;oBAC3D,MAAM,YAAY;wBAChB,YAAY;wBACZ,eAAe;wBACf,eAAe;wBACf,eAAe;oBACjB;oBAEA,qBAAqB;oBACrB,MAAM,IAAI;+DAAQ,CAAA,UAAW,WAAW,SAAS;;oBAEjD,SAAS;gBACX,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,yBAAyB;gBACzC,SAAU;oBACR,WAAW;gBACb;YACF;YAEA;QACF;mCAAG,EAAE;IAEL,MAAM,YAAY;QAChB;YACE,MAAM;YACN,OAAO,MAAM,UAAU;YACvB,MAAM,kNAAA,CAAA,WAAQ;YACd,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO,MAAM,aAAa;YAC1B,MAAM,gPAAA,CAAA,0BAAuB;YAC7B,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO,MAAM,aAAa;YAC1B,MAAM,oPAAA,CAAA,4BAAyB;YAC/B,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO,MAAM,aAAa;YAC1B,MAAM,gNAAA,CAAA,UAAO;YACb,OAAO;YACP,SAAS;QACX;KACD;IAED,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACZ;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;oBAAY,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;mBARb;;;;;;;;;;IAgBlB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC;gBAAoB,WAAU;0BAC7B,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAW,CAAC,eAAe,EAAE,KAAK,OAAO,EAAE;8CAC9C,cAAA,6LAAC,KAAK,IAAI;wCAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;wCAAE,eAAY;;;;;;;;;;;;;;;;0CAG/D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8C,KAAK,IAAI;;;;;;sDACrE,6LAAC;4CAAG,WAAU;sDAAqC,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAX7D,KAAK,IAAI;;;;;;;;;;AAoB3B;GAhHgB;KAAA", "debugId": null}}, {"offset": {"line": 1047, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/dashboard/recent-activity.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { createSupabaseClient } from '@/lib/supabase'\nimport { format } from 'date-fns'\nimport {\n  CubeIcon,\n  ClipboardDocumentListIcon,\n  CogIcon,\n  BeakerIcon,\n} from '@heroicons/react/24/outline'\n\ninterface Activity {\n  id: string\n  type: 'inventory' | 'recipe' | 'production' | 'quality'\n  title: string\n  description: string\n  timestamp: string\n  user?: string\n}\n\nexport function RecentActivity() {\n  const [activities, setActivities] = useState<Activity[]>([])\n  const [loading, setLoading] = useState(true)\n  const supabase = createSupabaseClient()\n\n  useEffect(() => {\n    async function fetchRecentActivity() {\n      try {\n        // This is a simplified version - in a real app, you'd have a proper activity log table\n        const recentActivities: Activity[] = [\n          {\n            id: '1',\n            type: 'inventory',\n            title: 'Low Stock Alert',\n            description: 'Vanilla Extract is running low (5 units remaining)',\n            timestamp: new Date().toISOString(),\n          },\n          {\n            id: '2',\n            type: 'production',\n            title: 'Batch Completed',\n            description: 'Production batch SFF-20241211-001 completed successfully',\n            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n          },\n          {\n            id: '3',\n            type: 'quality',\n            title: 'Quality Test Passed',\n            description: 'COA generated for batch SFF-20241211-001',\n            timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n          },\n          {\n            id: '4',\n            type: 'recipe',\n            title: 'Recipe Updated',\n            description: 'Strawberry Flavor v2.1 recipe modified',\n            timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n          },\n        ]\n\n        setActivities(recentActivities)\n      } catch (error) {\n        console.error('Error fetching recent activity:', error)\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    fetchRecentActivity()\n  }, [supabase])\n\n  const getActivityIcon = (type: Activity['type']) => {\n    switch (type) {\n      case 'inventory':\n        return CubeIcon\n      case 'recipe':\n        return ClipboardDocumentListIcon\n      case 'production':\n        return CogIcon\n      case 'quality':\n        return BeakerIcon\n      default:\n        return CubeIcon\n    }\n  }\n\n  const getActivityColor = (type: Activity['type']) => {\n    switch (type) {\n      case 'inventory':\n        return 'text-blue-600 bg-blue-100'\n      case 'recipe':\n        return 'text-green-600 bg-green-100'\n      case 'production':\n        return 'text-purple-600 bg-purple-100'\n      case 'quality':\n        return 'text-orange-600 bg-orange-100'\n      default:\n        return 'text-gray-600 bg-gray-100'\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">Recent Activity</h3>\n          <div className=\"space-y-4\">\n            {[...Array(4)].map((_, i) => (\n              <div key={i} className=\"flex items-start space-x-3 animate-pulse\">\n                <div className=\"h-8 w-8 bg-gray-200 rounded-full\"></div>\n                <div className=\"flex-1 space-y-2\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"bg-white shadow rounded-lg\">\n      <div className=\"px-4 py-5 sm:p-6\">\n        <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">Recent Activity</h3>\n        <div className=\"flow-root\">\n          <ul role=\"list\" className=\"-mb-8\">\n            {activities.map((activity, activityIdx) => {\n              const Icon = getActivityIcon(activity.type)\n              const colorClasses = getActivityColor(activity.type)\n              \n              return (\n                <li key={activity.id}>\n                  <div className=\"relative pb-8\">\n                    {activityIdx !== activities.length - 1 ? (\n                      <span\n                        className=\"absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200\"\n                        aria-hidden=\"true\"\n                      />\n                    ) : null}\n                    <div className=\"relative flex space-x-3\">\n                      <div>\n                        <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${colorClasses}`}>\n                          <Icon className=\"h-4 w-4\" aria-hidden=\"true\" />\n                        </span>\n                      </div>\n                      <div className=\"flex min-w-0 flex-1 justify-between space-x-4 pt-1.5\">\n                        <div>\n                          <p className=\"text-sm font-medium text-gray-900\">{activity.title}</p>\n                          <p className=\"text-sm text-gray-500\">{activity.description}</p>\n                        </div>\n                        <div className=\"whitespace-nowrap text-right text-sm text-gray-500\">\n                          <time dateTime={activity.timestamp}>\n                            {format(new Date(activity.timestamp), 'MMM d, h:mm a')}\n                          </time>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </li>\n              )\n            })}\n          </ul>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AALA;;;;;AAqBO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;IAEpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,eAAe;gBACb,IAAI;oBACF,uFAAuF;oBACvF,MAAM,mBAA+B;wBACnC;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,aAAa;4BACb,WAAW,IAAI,OAAO,WAAW;wBACnC;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,aAAa;4BACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;wBAClE;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,aAAa;4BACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;wBAClE;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,aAAa;4BACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;wBAClE;qBACD;oBAED,cAAc;gBAChB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACnD,SAAU;oBACR,WAAW;gBACb;YACF;YAEA;QACF;mCAAG;QAAC;KAAS;IAEb,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO,kNAAA,CAAA,WAAQ;YACjB,KAAK;gBACH,OAAO,oPAAA,CAAA,4BAAyB;YAClC,KAAK;gBACH,OAAO,gNAAA,CAAA,UAAO;YAChB,KAAK;gBACH,OAAO,sNAAA,CAAA,aAAU;YACnB;gBACE,OAAO,kNAAA,CAAA,WAAQ;QACnB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmD;;;;;;kCACjE,6LAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;gCAAY,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;+BAJT;;;;;;;;;;;;;;;;;;;;;IAYtB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAmD;;;;;;8BACjE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,MAAK;wBAAO,WAAU;kCACvB,WAAW,GAAG,CAAC,CAAC,UAAU;4BACzB,MAAM,OAAO,gBAAgB,SAAS,IAAI;4BAC1C,MAAM,eAAe,iBAAiB,SAAS,IAAI;4BAEnD,qBACE,6LAAC;0CACC,cAAA,6LAAC;oCAAI,WAAU;;wCACZ,gBAAgB,WAAW,MAAM,GAAG,kBACnC,6LAAC;4CACC,WAAU;4CACV,eAAY;;;;;mDAEZ;sDACJ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DACC,cAAA,6LAAC;wDAAK,WAAW,CAAC,wEAAwE,EAAE,cAAc;kEACxG,cAAA,6LAAC;4DAAK,WAAU;4DAAU,eAAY;;;;;;;;;;;;;;;;8DAG1C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAqC,SAAS,KAAK;;;;;;8EAChE,6LAAC;oEAAE,WAAU;8EAAyB,SAAS,WAAW;;;;;;;;;;;;sEAE5D,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,UAAU,SAAS,SAAS;0EAC/B,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,SAAS,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BArBzC,SAAS,EAAE;;;;;wBA6BxB;;;;;;;;;;;;;;;;;;;;;;AAMZ;GApJgB;KAAA", "debugId": null}}, {"offset": {"line": 1377, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/dashboard/quick-actions.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { Modal } from '@/components/ui/modal'\nimport {\n  PlusIcon,\n  ClipboardDocumentListIcon,\n  CogIcon,\n  BeakerIcon,\n} from '@heroicons/react/24/outline'\n\ninterface QuickAction {\n  name: string\n  description: string\n  icon: any\n  color: string\n  bgColor: string\n  action: 'navigate' | 'modal'\n  href?: string\n  modalType?: 'inventory' | 'recipe' | 'production' | 'quality'\n}\n\nconst actions: QuickAction[] = [\n  {\n    name: 'Add Inventory Item',\n    description: 'Add new raw materials or ingredients',\n    icon: PlusIcon,\n    color: 'text-blue-600',\n    bgColor: 'bg-blue-50 hover:bg-blue-100',\n    action: 'navigate',\n    href: '/inventory'\n  },\n  {\n    name: 'Create Recipe',\n    description: 'Create a new product recipe',\n    icon: ClipboardDocumentListIcon,\n    color: 'text-green-600',\n    bgColor: 'bg-green-50 hover:bg-green-100',\n    action: 'navigate',\n    href: '/recipes'\n  },\n  {\n    name: 'Start Production',\n    description: 'Begin a new production batch',\n    icon: CogIcon,\n    color: 'text-purple-600',\n    bgColor: 'bg-purple-50 hover:bg-purple-100',\n    action: 'navigate',\n    href: '/production'\n  },\n  {\n    name: 'Quality Test',\n    description: 'Record quality test results',\n    icon: BeakerIcon,\n    color: 'text-orange-600',\n    bgColor: 'bg-orange-50 hover:bg-orange-100',\n    action: 'modal',\n    modalType: 'quality'\n  },\n]\n\nexport function QuickActions() {\n  return (\n    <div className=\"bg-white shadow rounded-lg\">\n      <div className=\"px-4 py-5 sm:p-6\">\n        <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">Quick Actions</h3>\n        <div className=\"space-y-3\">\n          {actions.map((action) => (\n            <Link\n              key={action.name}\n              href={action.href}\n              className={`block p-3 rounded-lg border border-gray-200 transition-colors ${action.bgColor}`}\n            >\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <action.icon className={`h-5 w-5 ${action.color}`} aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-3 flex-1\">\n                  <p className=\"text-sm font-medium text-gray-900\">{action.name}</p>\n                  <p className=\"text-xs text-gray-500\">{action.description}</p>\n                </div>\n              </div>\n            </Link>\n          ))}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAKA;AAAA;AAAA;AAAA;AALA;;;AAuBA,MAAM,UAAyB;IAC7B;QACE,MAAM;QACN,aAAa;QACb,MAAM,kNAAA,CAAA,WAAQ;QACd,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;IACR;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,oPAAA,CAAA,4BAAyB;QAC/B,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;IACR;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,gNAAA,CAAA,UAAO;QACb,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;IACR;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,sNAAA,CAAA,aAAU;QAChB,OAAO;QACP,SAAS;QACT,QAAQ;QACR,WAAW;IACb;CACD;AAEM,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAmD;;;;;;8BACjE,6LAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;4BAEC,MAAM,OAAO,IAAI;4BACjB,WAAW,CAAC,8DAA8D,EAAE,OAAO,OAAO,EAAE;sCAE5F,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,OAAO,IAAI;4CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,KAAK,EAAE;4CAAE,eAAY;;;;;;;;;;;kDAEjE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAqC,OAAO,IAAI;;;;;;0DAC7D,6LAAC;gDAAE,WAAU;0DAAyB,OAAO,WAAW;;;;;;;;;;;;;;;;;;2BAVvD,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;AAmB9B;KA3BgB", "debugId": null}}, {"offset": {"line": 1528, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/dashboard/inventory-alerts.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { createSupabaseClient } from '@/lib/supabase'\nimport Link from 'next/link'\nimport { ExclamationTriangleIcon } from '@heroicons/react/24/outline'\n\ninterface LowStockItem {\n  id: string\n  name: string\n  sku: string\n  current_stock: number\n  minimum_stock: number\n  unit_of_measure: string\n}\n\nexport function InventoryAlerts() {\n  const [lowStockItems, setLowStockItems] = useState<LowStockItem[]>([])\n  const [loading, setLoading] = useState(true)\n  const supabase = createSupabaseClient()\n\n  useEffect(() => {\n    async function fetchLowStockItems() {\n      try {\n        // Mock low stock items for demo\n        const mockLowStockItems: LowStockItem[] = [\n          {\n            id: '1',\n            name: 'Vanilla Extract',\n            sku: 'VAN-001',\n            current_stock: 5,\n            minimum_stock: 10,\n            unit_of_measure: 'liters'\n          },\n          {\n            id: '2',\n            name: 'Citric Acid',\n            sku: 'CIT-001',\n            current_stock: 8,\n            minimum_stock: 12,\n            unit_of_measure: 'kg'\n          },\n          {\n            id: '3',\n            name: 'Glass Bottles 100ml',\n            sku: 'BOT-100',\n            current_stock: 200,\n            minimum_stock: 500,\n            unit_of_measure: 'pieces'\n          }\n        ]\n\n        // Simulate API delay\n        await new Promise(resolve => setTimeout(resolve, 800))\n\n        setLowStockItems(mockLowStockItems)\n      } catch (error) {\n        console.error('Error fetching low stock items:', error)\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    fetchLowStockItems()\n  }, [])\n\n  if (loading) {\n    return (\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">Inventory Alerts</h3>\n          <div className=\"space-y-3\">\n            {[...Array(3)].map((_, i) => (\n              <div key={i} className=\"flex items-center space-x-3 animate-pulse\">\n                <div className=\"h-5 w-5 bg-gray-200 rounded\"></div>\n                <div className=\"flex-1 space-y-2\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"bg-white shadow rounded-lg\">\n      <div className=\"px-4 py-5 sm:p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg leading-6 font-medium text-gray-900\">Inventory Alerts</h3>\n          <Link\n            href=\"/inventory\"\n            className=\"text-sm font-medium text-blue-600 hover:text-blue-500\"\n          >\n            View all\n          </Link>\n        </div>\n        \n        {lowStockItems.length === 0 ? (\n          <div className=\"text-center py-6\">\n            <div className=\"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100\">\n              <svg className=\"h-6 w-6 text-green-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n              </svg>\n            </div>\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">All good!</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">No low stock alerts at the moment.</p>\n          </div>\n        ) : (\n          <div className=\"space-y-3\">\n            {lowStockItems.map((item) => (\n              <div\n                key={item.id}\n                className=\"flex items-center space-x-3 p-3 bg-red-50 rounded-lg border border-red-200\"\n              >\n                <ExclamationTriangleIcon className=\"h-5 w-5 text-red-600 flex-shrink-0\" />\n                <div className=\"flex-1 min-w-0\">\n                  <p className=\"text-sm font-medium text-gray-900 truncate\">\n                    {item.name}\n                  </p>\n                  <p className=\"text-xs text-gray-500\">\n                    {item.current_stock} {item.unit_of_measure} remaining \n                    (min: {item.minimum_stock})\n                  </p>\n                </div>\n              </div>\n            ))}\n            \n            {lowStockItems.length > 0 && (\n              <div className=\"mt-4\">\n                <Link\n                  href=\"/inventory?filter=low_stock\"\n                  className=\"w-full flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200\"\n                >\n                  View all low stock items\n                </Link>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAgBO,SAAS;;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;IAEpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,eAAe;gBACb,IAAI;oBACF,gCAAgC;oBAChC,MAAM,oBAAoC;wBACxC;4BACE,IAAI;4BACJ,MAAM;4BACN,KAAK;4BACL,eAAe;4BACf,eAAe;4BACf,iBAAiB;wBACnB;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,KAAK;4BACL,eAAe;4BACf,eAAe;4BACf,iBAAiB;wBACnB;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,KAAK;4BACL,eAAe;4BACf,eAAe;4BACf,iBAAiB;wBACnB;qBACD;oBAED,qBAAqB;oBACrB,MAAM,IAAI;wEAAQ,CAAA,UAAW,WAAW,SAAS;;oBAEjD,iBAAiB;gBACnB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACnD,SAAU;oBACR,WAAW;gBACb;YACF;YAEA;QACF;oCAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmD;;;;;;kCACjE,6LAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;gCAAY,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;+BAJT;;;;;;;;;;;;;;;;;;;;;IAYtB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA8C;;;;;;sCAC5D,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;gBAKF,cAAc,MAAM,KAAK,kBACxB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;gCAAyB,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CAC7E,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;yCAG5C,6LAAC;oBAAI,WAAU;;wBACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC,gPAAA,CAAA,0BAAuB;wCAAC,WAAU;;;;;;kDACnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DACV,KAAK,IAAI;;;;;;0DAEZ,6LAAC;gDAAE,WAAU;;oDACV,KAAK,aAAa;oDAAC;oDAAE,KAAK,eAAe;oDAAC;oDACpC,KAAK,aAAa;oDAAC;;;;;;;;;;;;;;+BAVzB,KAAK,EAAE;;;;;wBAgBf,cAAc,MAAM,GAAG,mBACtB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAjIgB;KAAA", "debugId": null}}, {"offset": {"line": 1847, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/dashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/components/providers'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { DashboardStats } from '@/components/dashboard/dashboard-stats'\nimport { RecentActivity } from '@/components/dashboard/recent-activity'\nimport { QuickActions } from '@/components/dashboard/quick-actions'\nimport { InventoryAlerts } from '@/components/dashboard/inventory-alerts'\n\nexport function Dashboard() {\n  const { user, loading } = useAuth()\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    // Redirect to login if no user\n    if (typeof window !== 'undefined') {\n      window.location.href = '/login'\n    }\n    return null\n  }\n\n  return (\n    <MainLayout>\n      <div className=\"space-y-6\">\n        {/* Welcome Section */}\n        <div className=\"bg-white shadow rounded-lg p-6\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">\n            Welcome back to SFF Production & Quality Management\n          </h1>\n          <p className=\"mt-1 text-sm text-gray-600\">\n            Here's what's happening with your production and quality management today.\n          </p>\n        </div>\n\n        {/* Dashboard Stats */}\n        <DashboardStats />\n\n        {/* Main Content Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          {/* Left Column - 2/3 width */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            <RecentActivity />\n          </div>\n\n          {/* Right Column - 1/3 width */}\n          <div className=\"space-y-6\">\n            <QuickActions />\n            <InventoryAlerts />\n          </div>\n        </div>\n      </div>\n    </MainLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEhC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,MAAM;QACT,+BAA+B;QAC/B,wCAAmC;YACjC,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;QACA,OAAO;IACT;IAEA,qBACE,6LAAC,iJAAA,CAAA,aAAU;kBACT,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAGjD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAM5C,6LAAC,wJAAA,CAAA,iBAAc;;;;;8BAGf,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,wJAAA,CAAA,iBAAc;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,sJAAA,CAAA,eAAY;;;;;8CACb,6LAAC,yJAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B;GAnDgB;;QACY,kIAAA,CAAA,UAAO;;;KADnB", "debugId": null}}]}