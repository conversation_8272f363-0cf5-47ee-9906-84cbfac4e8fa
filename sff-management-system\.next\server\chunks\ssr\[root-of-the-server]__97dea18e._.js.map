{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\nimport {\n  HomeIcon,\n  CubeIcon,\n  BeakerIcon,\n  CogIcon,\n  DocumentTextIcon,\n  UsersIcon,\n  ChartBarIcon,\n  ClipboardDocumentListIcon,\n} from '@heroicons/react/24/outline'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { clsx } from 'clsx'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/', icon: HomeIcon },\n  { name: 'Inventory', href: '/inventory', icon: CubeIcon },\n  { name: 'Recipes', href: '/recipes', icon: ClipboardDocumentListIcon },\n  { name: 'Production', href: '/production', icon: CogIcon },\n  { name: 'Quality', href: '/quality', icon: BeakerIcon },\n  { name: 'Reports', href: '/reports', icon: ChartBarIcon },\n  { name: 'Documents', href: '/documents', icon: DocumentTextIcon },\n  { name: 'Users', href: '/users', icon: UsersIcon },\n]\n\ninterface SidebarProps {\n  open: boolean\n  setOpen: (open: boolean) => void\n}\n\nexport function Sidebar({ open, setOpen }: SidebarProps) {\n  const pathname = usePathname()\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <Transition.Root show={open} as={Fragment}>\n        <Dialog as=\"div\" className=\"relative z-50 lg:hidden\" onClose={setOpen}>\n          <Transition.Child\n            as={Fragment}\n            enter=\"transition-opacity ease-linear duration-300\"\n            enterFrom=\"opacity-0\"\n            enterTo=\"opacity-100\"\n            leave=\"transition-opacity ease-linear duration-300\"\n            leaveFrom=\"opacity-100\"\n            leaveTo=\"opacity-0\"\n          >\n            <div className=\"fixed inset-0 bg-gray-900/80\" />\n          </Transition.Child>\n\n          <div className=\"fixed inset-0 flex\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"transition ease-in-out duration-300 transform\"\n              enterFrom=\"-translate-x-full\"\n              enterTo=\"translate-x-0\"\n              leave=\"transition ease-in-out duration-300 transform\"\n              leaveFrom=\"translate-x-0\"\n              leaveTo=\"-translate-x-full\"\n            >\n              <Dialog.Panel className=\"relative mr-16 flex w-full max-w-xs flex-1\">\n                <Transition.Child\n                  as={Fragment}\n                  enter=\"ease-in-out duration-300\"\n                  enterFrom=\"opacity-0\"\n                  enterTo=\"opacity-100\"\n                  leave=\"ease-in-out duration-300\"\n                  leaveFrom=\"opacity-100\"\n                  leaveTo=\"opacity-0\"\n                >\n                  <div className=\"absolute left-full top-0 flex w-16 justify-center pt-5\">\n                    <button type=\"button\" className=\"-m-2.5 p-2.5\" onClick={() => setOpen(false)}>\n                      <span className=\"sr-only\">Close sidebar</span>\n                      <XMarkIcon className=\"h-6 w-6 text-white\" aria-hidden=\"true\" />\n                    </button>\n                  </div>\n                </Transition.Child>\n                <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4\">\n                  <div className=\"flex h-16 shrink-0 items-center\">\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">SFF</span>\n                    </div>\n                    <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n                  </div>\n                  <nav className=\"flex flex-1 flex-col\">\n                    <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n                      <li>\n                        <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                          {navigation.map((item) => (\n                            <li key={item.name}>\n                              <Link\n                                href={item.href}\n                                className={clsx(\n                                  pathname === item.href\n                                    ? 'bg-green-50 text-green-700'\n                                    : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                                  'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                                )}\n                              >\n                                <item.icon\n                                  className={clsx(\n                                    pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                                    'h-6 w-6 shrink-0'\n                                  )}\n                                  aria-hidden=\"true\"\n                                />\n                                {item.name}\n                              </Link>\n                            </li>\n                          ))}\n                        </ul>\n                      </li>\n                    </ul>\n                  </nav>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </Dialog>\n      </Transition.Root>\n\n      {/* Static sidebar for desktop */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 bg-white px-6 pb-4\">\n          <div className=\"flex h-16 shrink-0 items-center\">\n            <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-sm\">SFF</span>\n            </div>\n            <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n          </div>\n          <nav className=\"flex flex-1 flex-col\">\n            <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n              <li>\n                <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                  {navigation.map((item) => (\n                    <li key={item.name}>\n                      <Link\n                        href={item.href}\n                        className={clsx(\n                          pathname === item.href\n                            ? 'bg-green-50 text-green-700'\n                            : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                          'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                        )}\n                      >\n                        <item.icon\n                          className={clsx(\n                            pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                            'h-6 w-6 shrink-0'\n                          )}\n                          aria-hidden=\"true\"\n                        />\n                        {item.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </li>\n            </ul>\n          </nav>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AAjBA;;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,+MAAA,CAAA,WAAQ;IAAC;IAC/C;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,+MAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,iPAAA,CAAA,4BAAyB;IAAC;IACrE;QAAE,MAAM;QAAc,MAAM;QAAe,MAAM,6MAAA,CAAA,UAAO;IAAC;IACzD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,mNAAA,CAAA,aAAU;IAAC;IACtD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,uNAAA,CAAA,eAAY;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,+NAAA,CAAA,mBAAgB;IAAC;IAChE;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,iNAAA,CAAA,YAAS;IAAC;CAClD;AAOM,SAAS,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAgB;IACrD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE;;0BAEE,8OAAC,uLAAA,CAAA,aAAU,CAAC,IAAI;gBAAC,MAAM;gBAAM,IAAI,qMAAA,CAAA,WAAQ;0BACvC,cAAA,8OAAC,+KAAA,CAAA,SAAM;oBAAC,IAAG;oBAAM,WAAU;oBAA0B,SAAS;;sCAC5D,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,qMAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;gCACf,IAAI,qMAAA,CAAA,WAAQ;gCACZ,OAAM;gCACN,WAAU;gCACV,SAAQ;gCACR,OAAM;gCACN,WAAU;gCACV,SAAQ;0CAER,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;oCAAC,WAAU;;sDACtB,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;4CACf,IAAI,qMAAA,CAAA,WAAQ;4CACZ,OAAM;4CACN,WAAU;4CACV,SAAQ;4CACR,OAAM;4CACN,WAAU;4CACV,SAAQ;sDAER,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAO,MAAK;oDAAS,WAAU;oDAAe,SAAS,IAAM,QAAQ;;sEACpE,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC,iNAAA,CAAA,YAAS;4DAAC,WAAU;4DAAqB,eAAY;;;;;;;;;;;;;;;;;;;;;;sDAI5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,8OAAC;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;8DAE7D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAG,MAAK;wDAAO,WAAU;kEACxB,cAAA,8OAAC;sEACC,cAAA,8OAAC;gEAAG,MAAK;gEAAO,WAAU;0EACvB,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;kFACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4EACH,MAAM,KAAK,IAAI;4EACf,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;8FAGF,8OAAC,KAAK,IAAI;oFACR,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;oFAEF,eAAY;;;;;;gFAEb,KAAK,IAAI;;;;;;;uEAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiCxC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAE7D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,MAAK;gCAAO,WAAU;0CACxB,cAAA,8OAAC;8CACC,cAAA,8OAAC;wCAAG,MAAK;wCAAO,WAAU;kDACvB,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;sEAGF,8OAAC,KAAK,IAAI;4DACR,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;4DAEF,eAAY;;;;;;wDAEb,KAAK,IAAI;;;;;;;+CAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BtC", "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Menu, Transition } from '@headlessui/react'\nimport { Bars3Icon, BellIcon } from '@heroicons/react/24/outline'\nimport { useAuth } from '@/components/providers'\nimport { clsx } from 'clsx'\n\ninterface HeaderProps {\n  setSidebarOpen: (open: boolean) => void\n}\n\nexport function Header({ setSidebarOpen }: HeaderProps) {\n  const { user, signOut } = useAuth()\n\n  const userNavigation = [\n    { name: 'Your profile', href: '#' },\n    { name: 'Settings', href: '#' },\n    { name: 'Sign out', href: '#', onClick: signOut },\n  ]\n\n  return (\n    <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n      <button\n        type=\"button\"\n        className=\"-m-2.5 p-2.5 text-gray-700 lg:hidden\"\n        onClick={() => setSidebarOpen(true)}\n      >\n        <span className=\"sr-only\">Open sidebar</span>\n        <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n      </button>\n\n      {/* Separator */}\n      <div className=\"h-6 w-px bg-gray-200 lg:hidden\" aria-hidden=\"true\" />\n\n      <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n        <div className=\"relative flex flex-1\">\n          {/* Search can be added here later */}\n        </div>\n        <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n          <button type=\"button\" className=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\">\n            <span className=\"sr-only\">View notifications</span>\n            <BellIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n\n          {/* Separator */}\n          <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200\" aria-hidden=\"true\" />\n\n          {/* Profile dropdown */}\n          <Menu as=\"div\" className=\"relative\">\n            <Menu.Button className=\"-m-1.5 flex items-center p-1.5\">\n              <span className=\"sr-only\">Open user menu</span>\n              <div className=\"h-8 w-8 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.username?.charAt(0).toUpperCase() || 'U'}\n                </span>\n              </div>\n              <span className=\"hidden lg:flex lg:items-center\">\n                <span className=\"ml-4 text-sm font-semibold leading-6 text-gray-900\" aria-hidden=\"true\">\n                  {user?.username || 'User'}\n                </span>\n                <span className=\"ml-2 text-xs text-gray-500 capitalize\">\n                  ({user?.role?.replace('_', ' ') || 'Role'})\n                </span>\n              </span>\n            </Menu.Button>\n            <Transition\n              as={Fragment}\n              enter=\"transition ease-out duration-100\"\n              enterFrom=\"transform opacity-0 scale-95\"\n              enterTo=\"transform opacity-100 scale-100\"\n              leave=\"transition ease-in duration-75\"\n              leaveFrom=\"transform opacity-100 scale-100\"\n              leaveTo=\"transform opacity-0 scale-95\"\n            >\n              <Menu.Items className=\"absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none\">\n                {userNavigation.map((item) => (\n                  <Menu.Item key={item.name}>\n                    {({ active }) => (\n                      <button\n                        onClick={item.onClick}\n                        className={clsx(\n                          active ? 'bg-gray-50' : '',\n                          'block w-full text-left px-3 py-1 text-sm leading-6 text-gray-900'\n                        )}\n                      >\n                        {item.name}\n                      </button>\n                    )}\n                  </Menu.Item>\n                ))}\n              </Menu.Items>\n            </Transition>\n          </Menu>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;AANA;;;;;;;AAYO,SAAS,OAAO,EAAE,cAAc,EAAe;IACpD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAAgB,MAAM;QAAI;QAClC;YAAE,MAAM;YAAY,MAAM;QAAI;QAC9B;YAAE,MAAM;YAAY,MAAM;YAAK,SAAS;QAAQ;KACjD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,eAAe;;kCAE9B,8OAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,8OAAC,iNAAA,CAAA,YAAS;wBAAC,WAAU;wBAAU,eAAY;;;;;;;;;;;;0BAI7C,8OAAC;gBAAI,WAAU;gBAAiC,eAAY;;;;;;0BAE5D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,MAAK;gCAAS,WAAU;;kDAC9B,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC,+MAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;0CAI5C,8OAAC;gCAAI,WAAU;gCAAgD,eAAY;;;;;;0CAG3E,8OAAC,2KAAA,CAAA,OAAI;gCAAC,IAAG;gCAAM,WAAU;;kDACvB,8OAAC,2KAAA,CAAA,OAAI,CAAC,MAAM;wCAAC,WAAU;;0DACrB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,MAAM,UAAU,OAAO,GAAG,iBAAiB;;;;;;;;;;;0DAGhD,8OAAC;gDAAK,WAAU;;kEACd,8OAAC;wDAAK,WAAU;wDAAqD,eAAY;kEAC9E,MAAM,YAAY;;;;;;kEAErB,8OAAC;wDAAK,WAAU;;4DAAwC;4DACpD,MAAM,MAAM,QAAQ,KAAK,QAAQ;4DAAO;;;;;;;;;;;;;;;;;;;kDAIhD,8OAAC,uLAAA,CAAA,aAAU;wCACT,IAAI,qMAAA,CAAA,WAAQ;wCACZ,OAAM;wCACN,WAAU;wCACV,SAAQ;wCACR,OAAM;wCACN,WAAU;wCACV,SAAQ;kDAER,cAAA,8OAAC,2KAAA,CAAA,OAAI,CAAC,KAAK;4CAAC,WAAU;sDACnB,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,2KAAA,CAAA,OAAI,CAAC,IAAI;8DACP,CAAC,EAAE,MAAM,EAAE,iBACV,8OAAC;4DACC,SAAS,KAAK,OAAO;4DACrB,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,SAAS,eAAe,IACxB;sEAGD,KAAK,IAAI;;;;;;mDATA,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqB3C", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/components/providers'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const { user } = useAuth()\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Sidebar open={sidebarOpen} setOpen={setSidebarOpen} />\n      \n      <div className=\"lg:pl-72\">\n        <Header setSidebarOpen={setSidebarOpen} />\n        \n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAWO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAa,SAAS;;;;;;0BAErC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,SAAM;wBAAC,gBAAgB;;;;;;kCAExB,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/ui/modal.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\n\ninterface ModalProps {\n  isOpen: boolean\n  onClose: () => void\n  title: string\n  children: React.ReactNode\n  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl'\n}\n\nexport function Modal({ isOpen, onClose, title, children, maxWidth = 'lg' }: ModalProps) {\n  const maxWidthClasses = {\n    sm: 'sm:max-w-sm',\n    md: 'sm:max-w-md',\n    lg: 'sm:max-w-lg',\n    xl: 'sm:max-w-xl',\n    '2xl': 'sm:max-w-2xl'\n  }\n\n  return (\n    <Transition.Root show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={onClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-out duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in duration-200\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 z-10 overflow-y-auto\">\n          <div className=\"flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"ease-out duration-300\"\n              enterFrom=\"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95\"\n              enterTo=\"opacity-100 translate-y-0 sm:scale-100\"\n              leave=\"ease-in duration-200\"\n              leaveFrom=\"opacity-100 translate-y-0 sm:scale-100\"\n              leaveTo=\"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95\"\n            >\n              <Dialog.Panel className={`relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full ${maxWidthClasses[maxWidth]} sm:p-6`}>\n                <div className=\"absolute right-0 top-0 hidden pr-4 pt-4 sm:block\">\n                  <button\n                    type=\"button\"\n                    className=\"rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2\"\n                    onClick={onClose}\n                  >\n                    <span className=\"sr-only\">Close</span>\n                    <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                  </button>\n                </div>\n                <div className=\"sm:flex sm:items-start\">\n                  <div className=\"mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full\">\n                    <Dialog.Title as=\"h3\" className=\"text-lg font-semibold leading-6 text-gray-900 mb-4\">\n                      {title}\n                    </Dialog.Title>\n                    <div className=\"mt-2\">\n                      {children}\n                    </div>\n                  </div>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </div>\n      </Dialog>\n    </Transition.Root>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAcO,SAAS,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAc;IACrF,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;IACT;IAEA,qBACE,8OAAC,uLAAA,CAAA,aAAU,CAAC,IAAI;QAAC,MAAM;QAAQ,IAAI,qMAAA,CAAA,WAAQ;kBACzC,cAAA,8OAAC,+KAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,qMAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,qMAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;gCAAC,WAAW,CAAC,2HAA2H,EAAE,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC;;kDACvL,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS;;8DAET,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;;;;;;kDAG/C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;oDAAC,IAAG;oDAAK,WAAU;8DAC7B;;;;;;8DAEH,8OAAC;oDAAI,WAAU;8DACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvB", "debugId": null}}, {"offset": {"line": 920, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/app/documents/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { useAuth } from '@/components/providers'\nimport { Modal } from '@/components/ui/modal'\nimport {\n  PlusIcon,\n  DocumentTextIcon,\n  FolderIcon,\n  MagnifyingGlassIcon,\n  CloudArrowUpIcon,\n  DocumentArrowDownIcon\n} from '@heroicons/react/24/outline'\n\ninterface Document {\n  id: string\n  name: string\n  type: string\n  category: 'procedures' | 'policies' | 'forms' | 'certificates' | 'manuals' | 'other'\n  size: string\n  uploaded_by: string\n  uploaded_at: string\n  last_modified: string\n  version: string\n  status: 'active' | 'archived' | 'draft'\n}\n\nexport default function DocumentsPage() {\n  const { user } = useAuth()\n  const [documents, setDocuments] = useState<Document[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedCategory, setSelectedCategory] = useState<'all' | 'procedures' | 'policies' | 'forms' | 'certificates' | 'manuals' | 'other'>('all')\n  const [showUploadModal, setShowUploadModal] = useState(false)\n  const [newDocument, setNewDocument] = useState({\n    name: '',\n    category: 'procedures' as 'procedures' | 'policies' | 'forms' | 'certificates' | 'manuals' | 'other',\n    version: '1.0'\n  })\n\n  useEffect(() => {\n    // Mock documents data\n    const mockDocuments: Document[] = [\n      {\n        id: '1',\n        name: 'Standard Operating Procedures - Production',\n        type: 'PDF',\n        category: 'procedures',\n        size: '2.4 MB',\n        uploaded_by: 'Sarah Johnson',\n        uploaded_at: '2024-12-01T10:00:00Z',\n        last_modified: '2024-12-10T14:30:00Z',\n        version: '3.2',\n        status: 'active'\n      },\n      {\n        id: '2',\n        name: 'Quality Management Policy',\n        type: 'PDF',\n        category: 'policies',\n        size: '1.8 MB',\n        uploaded_by: 'Mike Wilson',\n        uploaded_at: '2024-11-15T09:00:00Z',\n        last_modified: '2024-11-20T16:45:00Z',\n        version: '2.1',\n        status: 'active'\n      },\n      {\n        id: '3',\n        name: 'Batch Production Record Form',\n        type: 'DOCX',\n        category: 'forms',\n        size: '156 KB',\n        uploaded_by: 'Emily Davis',\n        uploaded_at: '2024-12-05T11:30:00Z',\n        last_modified: '2024-12-05T11:30:00Z',\n        version: '1.0',\n        status: 'active'\n      },\n      {\n        id: '4',\n        name: 'ISO 9001:2015 Certificate',\n        type: 'PDF',\n        category: 'certificates',\n        size: '892 KB',\n        uploaded_by: 'John Smith',\n        uploaded_at: '2024-01-15T08:00:00Z',\n        last_modified: '2024-01-15T08:00:00Z',\n        version: '1.0',\n        status: 'active'\n      },\n      {\n        id: '5',\n        name: 'Equipment Maintenance Manual',\n        type: 'PDF',\n        category: 'manuals',\n        size: '5.2 MB',\n        uploaded_by: 'Sarah Johnson',\n        uploaded_at: '2024-10-20T13:15:00Z',\n        last_modified: '2024-11-05T10:20:00Z',\n        version: '4.0',\n        status: 'active'\n      },\n      {\n        id: '6',\n        name: 'HACCP Implementation Guide',\n        type: 'PDF',\n        category: 'procedures',\n        size: '3.1 MB',\n        uploaded_by: 'Mike Wilson',\n        uploaded_at: '2024-09-10T14:00:00Z',\n        last_modified: '2024-09-10T14:00:00Z',\n        version: '1.5',\n        status: 'active'\n      },\n      {\n        id: '7',\n        name: 'Supplier Qualification Form',\n        type: 'XLSX',\n        category: 'forms',\n        size: '245 KB',\n        uploaded_by: 'Emily Davis',\n        uploaded_at: '2024-11-28T09:45:00Z',\n        last_modified: '2024-12-02T15:10:00Z',\n        version: '2.0',\n        status: 'active'\n      },\n      {\n        id: '8',\n        name: 'Environmental Policy Statement',\n        type: 'PDF',\n        category: 'policies',\n        size: '678 KB',\n        uploaded_by: 'John Smith',\n        uploaded_at: '2024-08-15T12:00:00Z',\n        last_modified: '2024-08-15T12:00:00Z',\n        version: '1.0',\n        status: 'archived'\n      }\n    ]\n\n    setTimeout(() => {\n      setDocuments(mockDocuments)\n      setLoading(false)\n    }, 1000)\n  }, [])\n\n  const handleUploadDocument = () => {\n    const doc: Document = {\n      id: (documents.length + 1).toString(),\n      name: newDocument.name,\n      type: 'PDF',\n      category: newDocument.category,\n      size: `${Math.floor(Math.random() * 5000) + 100} KB`,\n      uploaded_by: user?.username || 'Current User',\n      uploaded_at: new Date().toISOString(),\n      last_modified: new Date().toISOString(),\n      version: newDocument.version,\n      status: 'active'\n    }\n\n    setDocuments([...documents, doc])\n    setShowUploadModal(false)\n    setNewDocument({\n      name: '',\n      category: 'procedures',\n      version: '1.0'\n    })\n  }\n\n  const categories = [\n    { key: 'all', name: 'All Documents', count: documents.length },\n    { key: 'procedures', name: 'Procedures', count: documents.filter(d => d.category === 'procedures').length },\n    { key: 'policies', name: 'Policies', count: documents.filter(d => d.category === 'policies').length },\n    { key: 'forms', name: 'Forms', count: documents.filter(d => d.category === 'forms').length },\n    { key: 'certificates', name: 'Certificates', count: documents.filter(d => d.category === 'certificates').length },\n    { key: 'manuals', name: 'Manuals', count: documents.filter(d => d.category === 'manuals').length }\n  ]\n\n  const filteredDocuments = documents.filter(doc => {\n    const matchesSearch = doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         doc.type.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         doc.uploaded_by.toLowerCase().includes(searchTerm.toLowerCase())\n    \n    const matchesCategory = selectedCategory === 'all' || doc.category === selectedCategory\n    \n    return matchesSearch && matchesCategory\n  })\n\n  const getFileIcon = (type: string) => {\n    return DocumentTextIcon // Simplified for demo\n  }\n\n  const getCategoryColor = (category: string) => {\n    switch (category) {\n      case 'procedures':\n        return 'bg-blue-100 text-blue-800'\n      case 'policies':\n        return 'bg-green-100 text-green-800'\n      case 'forms':\n        return 'bg-yellow-100 text-yellow-800'\n      case 'certificates':\n        return 'bg-purple-100 text-purple-800'\n      case 'manuals':\n        return 'bg-red-100 text-red-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'active':\n        return 'bg-green-100 text-green-800'\n      case 'archived':\n        return 'bg-gray-100 text-gray-800'\n      case 'draft':\n        return 'bg-yellow-100 text-yellow-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    })\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return (\n    <MainLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"sm:flex sm:items-center sm:justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Document Management</h1>\n            <p className=\"mt-2 text-sm text-gray-700\">\n              Organize and manage your company documents, procedures, and policies\n            </p>\n          </div>\n          <div className=\"mt-4 sm:mt-0\">\n            <button\n              type=\"button\"\n              onClick={() => setShowUploadModal(true)}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n            >\n              <CloudArrowUpIcon className=\"-ml-1 mr-2 h-5 w-5\" aria-hidden=\"true\" />\n              Upload Document\n            </button>\n          </div>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-4\">\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <DocumentTextIcon className=\"h-6 w-6 text-blue-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Documents</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">{documents.length}</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <FolderIcon className=\"h-6 w-6 text-green-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Categories</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">6</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <CloudArrowUpIcon className=\"h-6 w-6 text-purple-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">This Month</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">3</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <DocumentArrowDownIcon className=\"h-6 w-6 text-yellow-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Downloads</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">142</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Search and Filters */}\n        <div className=\"bg-white shadow rounded-lg p-6\">\n          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\n            <div className=\"relative flex-1 max-w-lg\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\n              </div>\n              <input\n                type=\"text\"\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-green-500 focus:border-green-500\"\n                placeholder=\"Search documents...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n              />\n            </div>\n            \n            <div className=\"flex space-x-4\">\n              {categories.map((category) => (\n                <button\n                  key={category.key}\n                  onClick={() => setSelectedCategory(category.key as any)}\n                  className={`px-3 py-2 rounded-md text-sm font-medium ${\n                    selectedCategory === category.key\n                      ? 'bg-green-100 text-green-700'\n                      : 'text-gray-500 hover:text-gray-700'\n                  }`}\n                >\n                  {category.name} ({category.count})\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Documents Table */}\n        <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n              Documents ({filteredDocuments.length})\n            </h3>\n            \n            {loading ? (\n              <div className=\"animate-pulse\">\n                {[...Array(8)].map((_, i) => (\n                  <div key={i} className=\"flex items-center space-x-4 py-4\">\n                    <div className=\"h-4 bg-gray-200 rounded w-1/4\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full divide-y divide-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Document\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Category\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Size\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Modified\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Uploaded By\n                      </th>\n                      <th className=\"relative px-6 py-3\">\n                        <span className=\"sr-only\">Actions</span>\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {filteredDocuments.map((doc) => {\n                      const IconComponent = getFileIcon(doc.type)\n                      return (\n                        <tr key={doc.id} className=\"hover:bg-gray-50\">\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\n                            <div className=\"flex items-center\">\n                              <IconComponent className=\"h-5 w-5 text-gray-400 mr-3\" />\n                              <div>\n                                <div className=\"text-sm font-medium text-gray-900\">{doc.name}</div>\n                                <div className=\"text-sm text-gray-500\">\n                                  {doc.type} • v{doc.version}\n                                </div>\n                              </div>\n                            </div>\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\n                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${getCategoryColor(doc.category)}`}>\n                              {doc.category}\n                            </span>\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {doc.size}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\n                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${getStatusColor(doc.status)}`}>\n                              {doc.status}\n                            </span>\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            {formatDate(doc.last_modified)}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {doc.uploaded_by}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                            <button className=\"text-green-600 hover:text-green-900 mr-3\">\n                              Download\n                            </button>\n                            <button className=\"text-green-600 hover:text-green-900\">\n                              View\n                            </button>\n                          </td>\n                        </tr>\n                      )\n                    })}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Upload Document Modal */}\n        <Modal\n          isOpen={showUploadModal}\n          onClose={() => setShowUploadModal(false)}\n          title=\"Upload New Document\"\n          maxWidth=\"lg\"\n        >\n          <form onSubmit={(e) => { e.preventDefault(); handleUploadDocument(); }} className=\"space-y-4\">\n            <div className=\"space-y-4\">\n              <div>\n                <label htmlFor=\"doc_name\" className=\"block text-sm font-medium text-gray-700\">\n                  Document Name\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"doc_name\"\n                  required\n                  className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                  value={newDocument.name}\n                  onChange={(e) => setNewDocument({ ...newDocument, name: e.target.value })}\n                />\n              </div>\n              <div>\n                <label htmlFor=\"doc_category\" className=\"block text-sm font-medium text-gray-700\">\n                  Category\n                </label>\n                <select\n                  id=\"doc_category\"\n                  className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                  value={newDocument.category}\n                  onChange={(e) => setNewDocument({ ...newDocument, category: e.target.value as any })}\n                >\n                  <option value=\"procedures\">Procedures</option>\n                  <option value=\"policies\">Policies</option>\n                  <option value=\"forms\">Forms</option>\n                  <option value=\"certificates\">Certificates</option>\n                  <option value=\"manuals\">Manuals</option>\n                  <option value=\"other\">Other</option>\n                </select>\n              </div>\n              <div>\n                <label htmlFor=\"doc_version\" className=\"block text-sm font-medium text-gray-700\">\n                  Version\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"doc_version\"\n                  className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                  value={newDocument.version}\n                  onChange={(e) => setNewDocument({ ...newDocument, version: e.target.value })}\n                />\n              </div>\n              <div>\n                <label htmlFor=\"file_upload\" className=\"block text-sm font-medium text-gray-700\">\n                  File\n                </label>\n                <div className=\"mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md\">\n                  <div className=\"space-y-1 text-center\">\n                    <CloudArrowUpIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                    <div className=\"flex text-sm text-gray-600\">\n                      <label\n                        htmlFor=\"file_upload\"\n                        className=\"relative cursor-pointer bg-white rounded-md font-medium text-green-600 hover:text-green-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-green-500\"\n                      >\n                        <span>Upload a file</span>\n                        <input id=\"file_upload\" name=\"file_upload\" type=\"file\" className=\"sr-only\" />\n                      </label>\n                      <p className=\"pl-1\">or drag and drop</p>\n                    </div>\n                    <p className=\"text-xs text-gray-500\">PDF, DOC, DOCX up to 10MB</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"flex justify-end space-x-3 pt-4\">\n              <button\n                type=\"button\"\n                onClick={() => setShowUploadModal(false)}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700\"\n              >\n                Upload Document\n              </button>\n            </div>\n          </form>\n        </Modal>\n      </div>\n    </MainLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AA4Be,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsF;IAC7I,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,MAAM;QACN,UAAU;QACV,SAAS;IACX;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sBAAsB;QACtB,MAAM,gBAA4B;YAChC;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,aAAa;gBACb,aAAa;gBACb,eAAe;gBACf,SAAS;gBACT,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,aAAa;gBACb,aAAa;gBACb,eAAe;gBACf,SAAS;gBACT,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,aAAa;gBACb,aAAa;gBACb,eAAe;gBACf,SAAS;gBACT,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,aAAa;gBACb,aAAa;gBACb,eAAe;gBACf,SAAS;gBACT,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,aAAa;gBACb,aAAa;gBACb,eAAe;gBACf,SAAS;gBACT,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,aAAa;gBACb,aAAa;gBACb,eAAe;gBACf,SAAS;gBACT,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,aAAa;gBACb,aAAa;gBACb,eAAe;gBACf,SAAS;gBACT,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,aAAa;gBACb,aAAa;gBACb,eAAe;gBACf,SAAS;gBACT,QAAQ;YACV;SACD;QAED,WAAW;YACT,aAAa;YACb,WAAW;QACb,GAAG;IACL,GAAG,EAAE;IAEL,MAAM,uBAAuB;QAC3B,MAAM,MAAgB;YACpB,IAAI,CAAC,UAAU,MAAM,GAAG,CAAC,EAAE,QAAQ;YACnC,MAAM,YAAY,IAAI;YACtB,MAAM;YACN,UAAU,YAAY,QAAQ;YAC9B,MAAM,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,IAAI,GAAG,CAAC;YACpD,aAAa,MAAM,YAAY;YAC/B,aAAa,IAAI,OAAO,WAAW;YACnC,eAAe,IAAI,OAAO,WAAW;YACrC,SAAS,YAAY,OAAO;YAC5B,QAAQ;QACV;QAEA,aAAa;eAAI;YAAW;SAAI;QAChC,mBAAmB;QACnB,eAAe;YACb,MAAM;YACN,UAAU;YACV,SAAS;QACX;IACF;IAEA,MAAM,aAAa;QACjB;YAAE,KAAK;YAAO,MAAM;YAAiB,OAAO,UAAU,MAAM;QAAC;QAC7D;YAAE,KAAK;YAAc,MAAM;YAAc,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,cAAc,MAAM;QAAC;QAC1G;YAAE,KAAK;YAAY,MAAM;YAAY,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,YAAY,MAAM;QAAC;QACpG;YAAE,KAAK;YAAS,MAAM;YAAS,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,SAAS,MAAM;QAAC;QAC3F;YAAE,KAAK;YAAgB,MAAM;YAAgB,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,gBAAgB,MAAM;QAAC;QAChH;YAAE,KAAK;YAAW,MAAM;YAAW,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,WAAW,MAAM;QAAC;KAClG;IAED,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA;QACzC,MAAM,gBAAgB,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACtD,IAAI,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAElF,MAAM,kBAAkB,qBAAqB,SAAS,IAAI,QAAQ,KAAK;QAEvE,OAAO,iBAAiB;IAC1B;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,gOAAiB,sBAAsB;QAAvC,CAAA,mBAAgB;IACzB;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC,8IAAA,CAAA,aAAU;kBACT,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,SAAS,IAAM,mBAAmB;gCAClC,WAAU;;kDAEV,8OAAC,+NAAA,CAAA,mBAAgB;wCAAC,WAAU;wCAAqB,eAAY;;;;;;oCAAS;;;;;;;;;;;;;;;;;;8BAO5E,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,+NAAA,CAAA,mBAAgB;gDAAC,WAAU;gDAAwB,eAAY;;;;;;;;;;;sDAElE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAAqC,UAAU,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO7E,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,mNAAA,CAAA,aAAU;gDAAC,WAAU;gDAAyB,eAAY;;;;;;;;;;;sDAE7D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,+NAAA,CAAA,mBAAgB;gDAAC,WAAU;gDAA0B,eAAY;;;;;;;;;;;sDAEpE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,yOAAA,CAAA,wBAAqB;gDAAC,WAAU;gDAA0B,eAAY;;;;;;;;;;;sDAEzE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS9D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;4CAAC,WAAU;4CAAwB,eAAY;;;;;;;;;;;kDAErE,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;0CAIjD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;wCAEC,SAAS,IAAM,oBAAoB,SAAS,GAAG;wCAC/C,WAAW,CAAC,yCAAyC,EACnD,qBAAqB,SAAS,GAAG,GAC7B,gCACA,qCACJ;;4CAED,SAAS,IAAI;4CAAC;4CAAG,SAAS,KAAK;4CAAC;;uCAR5B,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;8BAgB3B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAmD;oCACnD,kBAAkB,MAAM;oCAAC;;;;;;;4BAGtC,wBACC,8OAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;wCAAY,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;uCALP;;;;;;;;;qDAUd,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;sDAIhC,8OAAC;4CAAM,WAAU;sDACd,kBAAkB,GAAG,CAAC,CAAC;gDACtB,MAAM,gBAAgB,YAAY,IAAI,IAAI;gDAC1C,qBACE,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAc,WAAU;;;;;;kFACzB,8OAAC;;0FACC,8OAAC;gFAAI,WAAU;0FAAqC,IAAI,IAAI;;;;;;0FAC5D,8OAAC;gFAAI,WAAU;;oFACZ,IAAI,IAAI;oFAAC;oFAAK,IAAI,OAAO;;;;;;;;;;;;;;;;;;;;;;;;sEAKlC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAW,CAAC,mFAAmF,EAAE,iBAAiB,IAAI,QAAQ,GAAG;0EACpI,IAAI,QAAQ;;;;;;;;;;;sEAGjB,8OAAC;4DAAG,WAAU;sEACX,IAAI,IAAI;;;;;;sEAEX,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAW,CAAC,mFAAmF,EAAE,eAAe,IAAI,MAAM,GAAG;0EAChI,IAAI,MAAM;;;;;;;;;;;sEAGf,8OAAC;4DAAG,WAAU;sEACX,WAAW,IAAI,aAAa;;;;;;sEAE/B,8OAAC;4DAAG,WAAU;sEACX,IAAI,WAAW;;;;;;sEAElB,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAO,WAAU;8EAA2C;;;;;;8EAG7D,8OAAC;oEAAO,WAAU;8EAAsC;;;;;;;;;;;;;mDAnCnD,IAAI,EAAE;;;;;4CAyCnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASZ,8OAAC,iIAAA,CAAA,QAAK;oBACJ,QAAQ;oBACR,SAAS,IAAM,mBAAmB;oBAClC,OAAM;oBACN,UAAS;8BAET,cAAA,8OAAC;wBAAK,UAAU,CAAC;4BAAQ,EAAE,cAAc;4BAAI;wBAAwB;wBAAG,WAAU;;0CAChF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAW,WAAU;0DAA0C;;;;;;0DAG9E,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,QAAQ;gDACR,WAAU;gDACV,OAAO,YAAY,IAAI;gDACvB,UAAU,CAAC,IAAM,eAAe;wDAAE,GAAG,WAAW;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;kDAG3E,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAe,WAAU;0DAA0C;;;;;;0DAGlF,8OAAC;gDACC,IAAG;gDACH,WAAU;gDACV,OAAO,YAAY,QAAQ;gDAC3B,UAAU,CAAC,IAAM,eAAe;wDAAE,GAAG,WAAW;wDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oDAAQ;;kEAElF,8OAAC;wDAAO,OAAM;kEAAa;;;;;;kEAC3B,8OAAC;wDAAO,OAAM;kEAAW;;;;;;kEACzB,8OAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,8OAAC;wDAAO,OAAM;kEAAe;;;;;;kEAC7B,8OAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,8OAAC;wDAAO,OAAM;kEAAQ;;;;;;;;;;;;;;;;;;kDAG1B,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAc,WAAU;0DAA0C;;;;;;0DAGjF,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,WAAU;gDACV,OAAO,YAAY,OAAO;gDAC1B,UAAU,CAAC,IAAM,eAAe;wDAAE,GAAG,WAAW;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;kDAG9E,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAc,WAAU;0DAA0C;;;;;;0DAGjF,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,+NAAA,CAAA,mBAAgB;4DAAC,WAAU;;;;;;sEAC5B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,SAAQ;oEACR,WAAU;;sFAEV,8OAAC;sFAAK;;;;;;sFACN,8OAAC;4EAAM,IAAG;4EAAc,MAAK;4EAAc,MAAK;4EAAO,WAAU;;;;;;;;;;;;8EAEnE,8OAAC;oEAAE,WAAU;8EAAO;;;;;;;;;;;;sEAEtB,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAK7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,mBAAmB;wCAClC,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}