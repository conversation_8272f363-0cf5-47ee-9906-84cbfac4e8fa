# 🚀 دليل البدء السريع - SFF Management System Desktop

## تشغيل التطبيق بسرعة

### الطريقة الأسهل (Windows):
```bash
# انقر نقراً مزدوجاً على الملف
start-desktop.bat
```

### أو استخدم سطر الأوامر:
```bash
# التشغيل السريع
npm run quick-start

# أو
npm run desktop

# أو
node run-desktop.js
```

## بناء التطبيق للتوزيع

### بناء شامل:
```bash
npm run build-desktop
```

### بناء يدوي:
```bash
# بناء للنظام الحالي
npm run dist

# بناء ملف محمول فقط
npm run pack
```

## متطلبات سريعة

- ✅ Node.js 18+
- ✅ npm أو yarn
- ✅ 4GB RAM
- ✅ 500MB مساحة فارغة

## مشاكل شائعة

### التطبيق لا يبدأ:
```bash
# حذف وإعادة تثبيت
rm -rf node_modules
npm install
npm run quick-start
```

### خطأ في البناء:
```bash
# تنظيف وإعادة البناء
npm run clean
npm run build-desktop
```

## الدعم السريع

- 📧 <EMAIL>
- 📱 +966-XX-XXX-XXXX
- 🌐 https://sff-management.com

---

**استمتع بتطبيق SFF Management System! 🎉**
