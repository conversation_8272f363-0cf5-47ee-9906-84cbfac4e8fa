{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createClientComponentClient } from '@supabase/auth-helpers-nextjs'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Client component client\nexport const createSupabaseClient = () => createClientComponentClient()\n\n// Admin client (server-side only)\nexport const createSupabaseAdminClient = () => {\n  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n  return createClient(supabaseUrl, serviceRoleKey, {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  })\n}\n\n// Database types\nexport interface Database {\n  public: {\n    Tables: {\n      profiles: {\n        Row: {\n          id: string\n          email: string\n          full_name: string\n          role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department: string | null\n          phone: string | null\n          is_active: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          email: string\n          full_name: string\n          role?: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department?: string | null\n          phone?: string | null\n          is_active?: boolean\n        }\n        Update: {\n          email?: string\n          full_name?: string\n          role?: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department?: string | null\n          phone?: string | null\n          is_active?: boolean\n        }\n      }\n      inventory_categories: {\n        Row: {\n          id: string\n          name: string\n          description: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          description?: string | null\n        }\n        Update: {\n          name?: string\n          description?: string | null\n        }\n      }\n      inventory_items: {\n        Row: {\n          id: string\n          name: string\n          sku: string\n          category_id: string | null\n          description: string | null\n          unit_of_measure: string\n          current_stock: number\n          minimum_stock: number\n          maximum_stock: number | null\n          unit_cost: number\n          supplier_info: any | null\n          storage_conditions: string | null\n          expiry_date: string | null\n          batch_number: string | null\n          is_active: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          sku: string\n          category_id?: string | null\n          description?: string | null\n          unit_of_measure: string\n          current_stock?: number\n          minimum_stock?: number\n          maximum_stock?: number | null\n          unit_cost?: number\n          supplier_info?: any | null\n          storage_conditions?: string | null\n          expiry_date?: string | null\n          batch_number?: string | null\n          is_active?: boolean\n        }\n        Update: {\n          name?: string\n          sku?: string\n          category_id?: string | null\n          description?: string | null\n          unit_of_measure?: string\n          current_stock?: number\n          minimum_stock?: number\n          maximum_stock?: number | null\n          unit_cost?: number\n          supplier_info?: any | null\n          storage_conditions?: string | null\n          expiry_date?: string | null\n          batch_number?: string | null\n          is_active?: boolean\n        }\n      }\n      recipes: {\n        Row: {\n          id: string\n          name: string\n          code: string\n          description: string | null\n          category: string | null\n          version: number\n          batch_size: number\n          unit_of_measure: string\n          estimated_cost: number | null\n          preparation_time: number | null\n          instructions: string | null\n          notes: string | null\n          is_active: boolean\n          created_by: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          code: string\n          description?: string | null\n          category?: string | null\n          version?: number\n          batch_size: number\n          unit_of_measure: string\n          estimated_cost?: number | null\n          preparation_time?: number | null\n          instructions?: string | null\n          notes?: string | null\n          is_active?: boolean\n          created_by?: string | null\n        }\n        Update: {\n          name?: string\n          code?: string\n          description?: string | null\n          category?: string | null\n          version?: number\n          batch_size?: number\n          unit_of_measure?: string\n          estimated_cost?: number | null\n          preparation_time?: number | null\n          instructions?: string | null\n          notes?: string | null\n          is_active?: boolean\n        }\n      }\n      production_batches: {\n        Row: {\n          id: string\n          batch_number: string\n          recipe_id: string\n          batch_type: 'test' | 'production'\n          planned_quantity: number\n          actual_quantity: number | null\n          unit_of_measure: string\n          status: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date: string | null\n          actual_start_date: string | null\n          planned_end_date: string | null\n          actual_end_date: string | null\n          production_cost: number | null\n          yield_percentage: number | null\n          quality_approved: boolean | null\n          notes: string | null\n          created_by: string | null\n          assigned_to: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          batch_number: string\n          recipe_id: string\n          batch_type: 'test' | 'production'\n          planned_quantity: number\n          actual_quantity?: number | null\n          unit_of_measure: string\n          status?: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority?: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date?: string | null\n          actual_start_date?: string | null\n          planned_end_date?: string | null\n          actual_end_date?: string | null\n          production_cost?: number | null\n          yield_percentage?: number | null\n          quality_approved?: boolean | null\n          notes?: string | null\n          created_by?: string | null\n          assigned_to?: string | null\n        }\n        Update: {\n          batch_number?: string\n          recipe_id?: string\n          batch_type?: 'test' | 'production'\n          planned_quantity?: number\n          actual_quantity?: number | null\n          unit_of_measure?: string\n          status?: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority?: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date?: string | null\n          actual_start_date?: string | null\n          planned_end_date?: string | null\n          actual_end_date?: string | null\n          production_cost?: number | null\n          yield_percentage?: number | null\n          quality_approved?: boolean | null\n          notes?: string | null\n          assigned_to?: string | null\n        }\n      }\n    }\n    Views: {\n      [_ in never]: never\n    }\n    Functions: {\n      check_recipe_availability: {\n        Args: {\n          recipe_uuid: string\n          batch_size_multiplier?: number\n        }\n        Returns: {\n          item_id: string\n          item_name: string\n          required_quantity: number\n          available_quantity: number\n          is_sufficient: boolean\n        }[]\n      }\n      calculate_recipe_cost: {\n        Args: {\n          recipe_uuid: string\n          batch_size_multiplier?: number\n        }\n        Returns: number\n      }\n    }\n    Enums: {\n      user_role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAGoB;AAHpB;AACA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,uBAAuB,IAAM,CAAA,GAAA,2KAAA,CAAA,8BAA2B,AAAD;AAG7D,MAAM,4BAA4B;IACvC,MAAM,iBAAiB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB;IAC5D,OAAO,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa,gBAAgB;QAC/C,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/providers.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { createSupabaseClient } from '@/lib/supabase'\nimport { User } from '@supabase/supabase-js'\n\ninterface AuthContextType {\n  user: User | null\n  loading: boolean\n  signOut: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\nexport function Providers({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n  const supabase = createSupabaseClient()\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session } } = await supabase.auth.getSession()\n      setUser(session?.user ?? null)\n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setUser(session?.user ?? null)\n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [supabase.auth])\n\n  const signOut = async () => {\n    await supabase.auth.signOut()\n  }\n\n  const value = {\n    user,\n    loading,\n    signOut,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAYA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAQT,SAAS,UAAU,EAAE,QAAQ,EAAiC;;IACnE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;IAEpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,sBAAsB;YACtB,MAAM;yDAAoB;oBACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;oBAC5D,QAAQ,SAAS,QAAQ;oBACzB,WAAW;gBACb;;YAEA;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB;uCAChE,OAAO,OAAO;oBACZ,QAAQ,SAAS,QAAQ;oBACzB,WAAW;gBACb;;YAGF;uCAAO,IAAM,aAAa,WAAW;;QACvC;8BAAG;QAAC,SAAS,IAAI;KAAC;IAElB,MAAM,UAAU;QACd,MAAM,SAAS,IAAI,CAAC,OAAO;IAC7B;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IAzCgB;KAAA", "debugId": null}}]}