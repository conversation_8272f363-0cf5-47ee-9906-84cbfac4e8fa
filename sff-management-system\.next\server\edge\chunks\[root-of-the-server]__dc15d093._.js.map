{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'\nimport { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\n\nexport async function middleware(req: NextRequest) {\n  const res = NextResponse.next()\n  const supabase = createMiddlewareClient({ req, res })\n\n  const {\n    data: { session },\n  } = await supabase.auth.getSession()\n\n  // If user is not signed in and the current path is not /login, redirect to /login\n  if (!session && !req.nextUrl.pathname.startsWith('/login')) {\n    return NextResponse.redirect(new URL('/login', req.url))\n  }\n\n  // If user is signed in and the current path is /login, redirect to dashboard\n  if (session && req.nextUrl.pathname.startsWith('/login')) {\n    return NextResponse.redirect(new URL('/', req.url))\n  }\n\n  return res\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAGO,eAAe,WAAW,GAAgB;IAC/C,MAAM,MAAM,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC7B,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,yBAAsB,AAAD,EAAE;QAAE;QAAK;IAAI;IAEnD,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;IAElC,kFAAkF;IAClF,IAAI,CAAC,WAAW,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW;QAC1D,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,IAAI,GAAG;IACxD;IAEA,6EAA6E;IAC7E,IAAI,WAAW,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW;QACxD,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,IAAI,GAAG;IACnD;IAEA,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}