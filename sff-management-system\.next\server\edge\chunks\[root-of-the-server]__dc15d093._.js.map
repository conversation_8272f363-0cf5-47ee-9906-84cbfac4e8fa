{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\n\nexport async function middleware(req: NextRequest) {\n  // For demo purposes, we'll handle authentication on the client side\n  // In production, you'd validate the session server-side\n\n  // Allow all requests to pass through\n  // Authentication will be handled by the client-side providers\n  return NextResponse.next()\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGO,eAAe,WAAW,GAAgB;IAC/C,oEAAoE;IACpE,wDAAwD;IAExD,qCAAqC;IACrC,8DAA8D;IAC9D,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}