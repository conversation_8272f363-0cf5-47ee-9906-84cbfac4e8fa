# SFF System Setup Guide

## 🚀 Quick Start

### 1. First Time Setup

The application is now running at [http://localhost:3000](http://localhost:3000)

### 2. Login with Demo Credentials

The system now uses **username and password** authentication with beautiful white and green design!

**Available Demo Accounts:**

| Username | Password | Role | Access Level |
|----------|----------|------|--------------|
| `admin` | `admin123` | Administrator | Full system access |
| `quality` | `quality123` | Quality Manager | Quality documents & testing |
| `production` | `production123` | Production Manager | Inventory & production |
| `employee` | `employee123` | Employee | Limited read access |

### 3. Access the System

1. **Visit the login page**: [http://localhost:3000/login](http://localhost:3000/login)
2. **Choose your role**: Use any of the demo credentials above
3. **Explore the system**: Each role has different permissions and access levels

### 4. Access the System

Once you have admin access, you can:
- **Dashboard** - View real-time statistics and alerts
- **Inventory** - Manage items, track stock levels, view alerts
- **Recipes** - Create formulations, manage ingredients, calculate costs
- **Production** - Track batches, monitor progress, manage test runs
- **Quality** - Handle MSDS, COA, TDS documents, lab reports
- **Reports** - Generate analytics and export data
- **Documents** - Manage procedures, policies, and certificates
- **Users** - Manage user accounts and permissions (Admin only)

## 📊 Sample Data Available

The system comes with sample data including:

### Inventory Categories
- Flavors
- Base Materials  
- Chemicals
- Packaging Materials
- Raw Materials
- Finished Products
- Laboratory Supplies
- Cleaning Supplies

### Sample Inventory Items
- Vanilla Extract (5L - Low Stock Alert)
- Strawberry Flavor (15L)
- Propylene Glycol (50L)
- Ethyl Alcohol (25L)
- Citric Acid (8kg - Low Stock Alert)
- Glass Bottles 100ml (200 pieces - Low Stock Alert)

### Sample Recipes
- **Strawberry Vanilla Blend** (10L batch)
  - 30% Strawberry Flavor
  - 20% Vanilla Extract
  - 50% Propylene Glycol

- **Classic Vanilla Extract** (5L batch)
  - 40% Vanilla Extract
  - 60% Ethyl Alcohol

## 🔧 System Configuration

### Default Settings
- Company Name: "SFF Production & Quality Management"
- Default Currency: USD
- Low Stock Threshold: 10%
- Batch Number Format: SFF-{YYYY}{MM}{DD}-{###}

### User Roles Available
1. **Admin** - Full system access
2. **Quality Manager** - Quality documents and testing
3. **Production Manager** - Inventory and production
4. **Employee** - Limited read access

## 🎯 Next Steps

1. **Explore the Dashboard** - Check out the statistics and alerts
2. **Review Inventory** - See the low stock alerts in action
3. **Test Recipe Functionality** - Try the recipe availability checker
4. **Create Test Users** - Add users with different roles
5. **Start a Test Production** - Create a sample batch

## 🔍 Testing Features

### Test Inventory Alerts
- Several items are set with low stock to demonstrate alerts
- Check the dashboard for red alert notifications

### Test Recipe Costing
- Recipes automatically calculate costs based on current inventory prices
- Try the `check_recipe_availability` function

### Test Production Flow
1. Select a recipe
2. Check ingredient availability
3. Start a test batch
4. Record quality tests
5. Approve for full production

## 🛠 Troubleshooting

### Common Issues

1. **Can't log in** - Make sure you've confirmed your email
2. **No admin access** - Run the SQL command to set admin role
3. **Missing data** - Sample data should be automatically loaded
4. **Permission errors** - Check your user role in the profiles table

### Database Access
- Supabase Dashboard: [https://supabase.com/dashboard](https://supabase.com/dashboard)
- Project ID: `hqupqoehnoyxzaqbneog`
- Region: `eu-north-1`

## 📞 Support

If you encounter any issues:
1. Check the browser console for errors
2. Verify your environment variables
3. Ensure Supabase project is active
4. Check the terminal for server errors

---

**Welcome to SFF Production & Quality Management System! 🎉**
