{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\nimport {\n  HomeIcon,\n  CubeIcon,\n  BeakerIcon,\n  CogIcon,\n  DocumentTextIcon,\n  UsersIcon,\n  ChartBarIcon,\n  ClipboardDocumentListIcon,\n} from '@heroicons/react/24/outline'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { clsx } from 'clsx'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/', icon: HomeIcon },\n  { name: 'Inventory', href: '/inventory', icon: CubeIcon },\n  { name: 'Recipes', href: '/recipes', icon: ClipboardDocumentListIcon },\n  { name: 'Production', href: '/production', icon: CogIcon },\n  { name: 'Quality', href: '/quality', icon: BeakerIcon },\n  { name: 'Reports', href: '/reports', icon: ChartBarIcon },\n  { name: 'Documents', href: '/documents', icon: DocumentTextIcon },\n  { name: 'Users', href: '/users', icon: UsersIcon },\n]\n\ninterface SidebarProps {\n  open: boolean\n  setOpen: (open: boolean) => void\n}\n\nexport function Sidebar({ open, setOpen }: SidebarProps) {\n  const pathname = usePathname()\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <Transition.Root show={open} as={Fragment}>\n        <Dialog as=\"div\" className=\"relative z-50 lg:hidden\" onClose={setOpen}>\n          <Transition.Child\n            as={Fragment}\n            enter=\"transition-opacity ease-linear duration-300\"\n            enterFrom=\"opacity-0\"\n            enterTo=\"opacity-100\"\n            leave=\"transition-opacity ease-linear duration-300\"\n            leaveFrom=\"opacity-100\"\n            leaveTo=\"opacity-0\"\n          >\n            <div className=\"fixed inset-0 bg-gray-900/80\" />\n          </Transition.Child>\n\n          <div className=\"fixed inset-0 flex\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"transition ease-in-out duration-300 transform\"\n              enterFrom=\"-translate-x-full\"\n              enterTo=\"translate-x-0\"\n              leave=\"transition ease-in-out duration-300 transform\"\n              leaveFrom=\"translate-x-0\"\n              leaveTo=\"-translate-x-full\"\n            >\n              <Dialog.Panel className=\"relative mr-16 flex w-full max-w-xs flex-1\">\n                <Transition.Child\n                  as={Fragment}\n                  enter=\"ease-in-out duration-300\"\n                  enterFrom=\"opacity-0\"\n                  enterTo=\"opacity-100\"\n                  leave=\"ease-in-out duration-300\"\n                  leaveFrom=\"opacity-100\"\n                  leaveTo=\"opacity-0\"\n                >\n                  <div className=\"absolute left-full top-0 flex w-16 justify-center pt-5\">\n                    <button type=\"button\" className=\"-m-2.5 p-2.5\" onClick={() => setOpen(false)}>\n                      <span className=\"sr-only\">Close sidebar</span>\n                      <XMarkIcon className=\"h-6 w-6 text-white\" aria-hidden=\"true\" />\n                    </button>\n                  </div>\n                </Transition.Child>\n                <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4\">\n                  <div className=\"flex h-16 shrink-0 items-center\">\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">SFF</span>\n                    </div>\n                    <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n                  </div>\n                  <nav className=\"flex flex-1 flex-col\">\n                    <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n                      <li>\n                        <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                          {navigation.map((item) => (\n                            <li key={item.name}>\n                              <Link\n                                href={item.href}\n                                className={clsx(\n                                  pathname === item.href\n                                    ? 'bg-green-50 text-green-700'\n                                    : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                                  'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                                )}\n                              >\n                                <item.icon\n                                  className={clsx(\n                                    pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                                    'h-6 w-6 shrink-0'\n                                  )}\n                                  aria-hidden=\"true\"\n                                />\n                                {item.name}\n                              </Link>\n                            </li>\n                          ))}\n                        </ul>\n                      </li>\n                    </ul>\n                  </nav>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </Dialog>\n      </Transition.Root>\n\n      {/* Static sidebar for desktop */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 bg-white px-6 pb-4\">\n          <div className=\"flex h-16 shrink-0 items-center\">\n            <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-sm\">SFF</span>\n            </div>\n            <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n          </div>\n          <nav className=\"flex flex-1 flex-col\">\n            <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n              <li>\n                <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                  {navigation.map((item) => (\n                    <li key={item.name}>\n                      <Link\n                        href={item.href}\n                        className={clsx(\n                          pathname === item.href\n                            ? 'bg-green-50 text-green-700'\n                            : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                          'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                        )}\n                      >\n                        <item.icon\n                          className={clsx(\n                            pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                            'h-6 w-6 shrink-0'\n                          )}\n                          aria-hidden=\"true\"\n                        />\n                        {item.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </li>\n            </ul>\n          </nav>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;;;AAjBA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,kNAAA,CAAA,WAAQ;IAAC;IAC/C;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,oPAAA,CAAA,4BAAyB;IAAC;IACrE;QAAE,MAAM;QAAc,MAAM;QAAe,MAAM,gNAAA,CAAA,UAAO;IAAC;IACzD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,sNAAA,CAAA,aAAU;IAAC;IACtD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,0NAAA,CAAA,eAAY;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kOAAA,CAAA,mBAAgB;IAAC;IAChE;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,oNAAA,CAAA,YAAS;IAAC;CAClD;AAOM,SAAS,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAgB;;IACrD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE;;0BAEE,6LAAC,0LAAA,CAAA,aAAU,CAAC,IAAI;gBAAC,MAAM;gBAAM,IAAI,6JAAA,CAAA,WAAQ;0BACvC,cAAA,6LAAC,kLAAA,CAAA,SAAM;oBAAC,IAAG;oBAAM,WAAU;oBAA0B,SAAS;;sCAC5D,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,6JAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;sCAGjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;gCACf,IAAI,6JAAA,CAAA,WAAQ;gCACZ,OAAM;gCACN,WAAU;gCACV,SAAQ;gCACR,OAAM;gCACN,WAAU;gCACV,SAAQ;0CAER,cAAA,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;oCAAC,WAAU;;sDACtB,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4CACf,IAAI,6JAAA,CAAA,WAAQ;4CACZ,OAAM;4CACN,WAAU;4CACV,SAAQ;4CACR,OAAM;4CACN,WAAU;4CACV,SAAQ;sDAER,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAO,MAAK;oDAAS,WAAU;oDAAe,SAAS,IAAM,QAAQ;;sEACpE,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;4DAAqB,eAAY;;;;;;;;;;;;;;;;;;;;;;sDAI5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,6LAAC;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;8DAE7D,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAG,MAAK;wDAAO,WAAU;kEACxB,cAAA,6LAAC;sEACC,cAAA,6LAAC;gEAAG,MAAK;gEAAO,WAAU;0EACvB,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;kFACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4EACH,MAAM,KAAK,IAAI;4EACf,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;8FAGF,6LAAC,KAAK,IAAI;oFACR,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;oFAEF,eAAY;;;;;;gFAEb,KAAK,IAAI;;;;;;;uEAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiCxC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAE7D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,MAAK;gCAAO,WAAU;0CACxB,cAAA,6LAAC;8CACC,cAAA,6LAAC;wCAAG,MAAK;wCAAO,WAAU;kDACvB,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;sEAGF,6LAAC,KAAK,IAAI;4DACR,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;4DAEF,eAAY;;;;;;wDAEb,KAAK,IAAI;;;;;;;+CAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BtC;GAtIgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Menu, Transition } from '@headlessui/react'\nimport { Bars3Icon, BellIcon } from '@heroicons/react/24/outline'\nimport { useAuth } from '@/components/providers'\nimport { clsx } from 'clsx'\n\ninterface HeaderProps {\n  setSidebarOpen: (open: boolean) => void\n}\n\nexport function Header({ setSidebarOpen }: HeaderProps) {\n  const { user, signOut } = useAuth()\n\n  const userNavigation = [\n    { name: 'Your profile', href: '#' },\n    { name: 'Settings', href: '#' },\n    { name: 'Sign out', href: '#', onClick: signOut },\n  ]\n\n  return (\n    <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n      <button\n        type=\"button\"\n        className=\"-m-2.5 p-2.5 text-gray-700 lg:hidden\"\n        onClick={() => setSidebarOpen(true)}\n      >\n        <span className=\"sr-only\">Open sidebar</span>\n        <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n      </button>\n\n      {/* Separator */}\n      <div className=\"h-6 w-px bg-gray-200 lg:hidden\" aria-hidden=\"true\" />\n\n      <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n        <div className=\"relative flex flex-1\">\n          {/* Search can be added here later */}\n        </div>\n        <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n          <button type=\"button\" className=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\">\n            <span className=\"sr-only\">View notifications</span>\n            <BellIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n\n          {/* Separator */}\n          <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200\" aria-hidden=\"true\" />\n\n          {/* Profile dropdown */}\n          <Menu as=\"div\" className=\"relative\">\n            <Menu.Button className=\"-m-1.5 flex items-center p-1.5\">\n              <span className=\"sr-only\">Open user menu</span>\n              <div className=\"h-8 w-8 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.username?.charAt(0).toUpperCase() || 'U'}\n                </span>\n              </div>\n              <span className=\"hidden lg:flex lg:items-center\">\n                <span className=\"ml-4 text-sm font-semibold leading-6 text-gray-900\" aria-hidden=\"true\">\n                  {user?.username || 'User'}\n                </span>\n                <span className=\"ml-2 text-xs text-gray-500 capitalize\">\n                  ({user?.role?.replace('_', ' ') || 'Role'})\n                </span>\n              </span>\n            </Menu.Button>\n            <Transition\n              as={Fragment}\n              enter=\"transition ease-out duration-100\"\n              enterFrom=\"transform opacity-0 scale-95\"\n              enterTo=\"transform opacity-100 scale-100\"\n              leave=\"transition ease-in duration-75\"\n              leaveFrom=\"transform opacity-100 scale-100\"\n              leaveTo=\"transform opacity-0 scale-95\"\n            >\n              <Menu.Items className=\"absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none\">\n                {userNavigation.map((item) => (\n                  <Menu.Item key={item.name}>\n                    {({ active }) => (\n                      <button\n                        onClick={item.onClick}\n                        className={clsx(\n                          active ? 'bg-gray-50' : '',\n                          'block w-full text-left px-3 py-1 text-sm leading-6 text-gray-900'\n                        )}\n                      >\n                        {item.name}\n                      </button>\n                    )}\n                  </Menu.Item>\n                ))}\n              </Menu.Items>\n            </Transition>\n          </Menu>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;;;AANA;;;;;;AAYO,SAAS,OAAO,EAAE,cAAc,EAAe;;IACpD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAAgB,MAAM;QAAI;QAClC;YAAE,MAAM;YAAY,MAAM;QAAI;QAC9B;YAAE,MAAM;YAAY,MAAM;YAAK,SAAS;QAAQ;KACjD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,eAAe;;kCAE9B,6LAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;wBAAU,eAAY;;;;;;;;;;;;0BAI7C,6LAAC;gBAAI,WAAU;gBAAiC,eAAY;;;;;;0BAE5D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,MAAK;gCAAS,WAAU;;kDAC9B,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;gCAAgD,eAAY;;;;;;0CAG3E,6LAAC,8KAAA,CAAA,OAAI;gCAAC,IAAG;gCAAM,WAAU;;kDACvB,6LAAC,8KAAA,CAAA,OAAI,CAAC,MAAM;wCAAC,WAAU;;0DACrB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb,MAAM,UAAU,OAAO,GAAG,iBAAiB;;;;;;;;;;;0DAGhD,6LAAC;gDAAK,WAAU;;kEACd,6LAAC;wDAAK,WAAU;wDAAqD,eAAY;kEAC9E,MAAM,YAAY;;;;;;kEAErB,6LAAC;wDAAK,WAAU;;4DAAwC;4DACpD,MAAM,MAAM,QAAQ,KAAK,QAAQ;4DAAO;;;;;;;;;;;;;;;;;;;kDAIhD,6LAAC,0LAAA,CAAA,aAAU;wCACT,IAAI,6JAAA,CAAA,WAAQ;wCACZ,OAAM;wCACN,WAAU;wCACV,SAAQ;wCACR,OAAM;wCACN,WAAU;wCACV,SAAQ;kDAER,cAAA,6LAAC,8KAAA,CAAA,OAAI,CAAC,KAAK;4CAAC,WAAU;sDACnB,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;8DACP,CAAC,EAAE,MAAM,EAAE,iBACV,6LAAC;4DACC,SAAS,KAAK,OAAO;4DACrB,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,SAAS,eAAe,IACxB;sEAGD,KAAK,IAAI;;;;;;mDATA,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqB3C;GAtFgB;;QACY,kIAAA,CAAA,UAAO;;;KADnB", "debugId": null}}, {"offset": {"line": 680, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/components/providers'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const { user } = useAuth()\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Sidebar open={sidebarOpen} setOpen={setSidebarOpen} />\n      \n      <div className=\"lg:pl-72\">\n        <Header setSidebarOpen={setSidebarOpen} />\n        \n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAWO,SAAS,WAAW,EAAE,QAAQ,EAAmB;;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAO;gBAAC,MAAM;gBAAa,SAAS;;;;;;0BAErC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,SAAM;wBAAC,gBAAgB;;;;;;kCAExB,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAnBgB;;QAEG,kIAAA,CAAA,UAAO;;;KAFV", "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/ui/modal.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\n\ninterface ModalProps {\n  isOpen: boolean\n  onClose: () => void\n  title: string\n  children: React.ReactNode\n  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl'\n}\n\nexport function Modal({ isOpen, onClose, title, children, maxWidth = 'lg' }: ModalProps) {\n  const maxWidthClasses = {\n    sm: 'sm:max-w-sm',\n    md: 'sm:max-w-md',\n    lg: 'sm:max-w-lg',\n    xl: 'sm:max-w-xl',\n    '2xl': 'sm:max-w-2xl'\n  }\n\n  return (\n    <Transition.Root show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={onClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-out duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in duration-200\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 z-10 overflow-y-auto\">\n          <div className=\"flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"ease-out duration-300\"\n              enterFrom=\"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95\"\n              enterTo=\"opacity-100 translate-y-0 sm:scale-100\"\n              leave=\"ease-in duration-200\"\n              leaveFrom=\"opacity-100 translate-y-0 sm:scale-100\"\n              leaveTo=\"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95\"\n            >\n              <Dialog.Panel className={`relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full ${maxWidthClasses[maxWidth]} sm:p-6`}>\n                <div className=\"absolute right-0 top-0 hidden pr-4 pt-4 sm:block\">\n                  <button\n                    type=\"button\"\n                    className=\"rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2\"\n                    onClick={onClose}\n                  >\n                    <span className=\"sr-only\">Close</span>\n                    <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                  </button>\n                </div>\n                <div className=\"sm:flex sm:items-start\">\n                  <div className=\"mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full\">\n                    <Dialog.Title as=\"h3\" className=\"text-lg font-semibold leading-6 text-gray-900 mb-4\">\n                      {title}\n                    </Dialog.Title>\n                    <div className=\"mt-2\">\n                      {children}\n                    </div>\n                  </div>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </div>\n      </Dialog>\n    </Transition.Root>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAcO,SAAS,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAc;IACrF,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;IACT;IAEA,qBACE,6LAAC,0LAAA,CAAA,aAAU,CAAC,IAAI;QAAC,MAAM;QAAQ,IAAI,6JAAA,CAAA,WAAQ;kBACzC,cAAA,6LAAC,kLAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,6JAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,6JAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;gCAAC,WAAW,CAAC,2HAA2H,EAAE,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC;;kDACvL,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS;;8DAET,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;;;;;;kDAG/C,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;oDAAC,IAAG;oDAAK,WAAU;8DAC7B;;;;;;8DAEH,6LAAC;oDAAI,WAAU;8DACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvB;KA/DgB", "debugId": null}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/lib/database.ts"], "sourcesContent": ["import { createSupabaseClient } from './supabase'\n\nconst supabase = createSupabaseClient()\n\n// Inventory Items\nexport interface InventoryItem {\n  id: string\n  name: string\n  sku: string\n  category_id: string\n  current_stock: number\n  minimum_stock: number\n  unit_of_measure: string\n  unit_cost: number\n  supplier?: string\n  description?: string\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface InventoryCategory {\n  id: string\n  name: string\n  description?: string\n  is_active: boolean\n}\n\n// Recipes\nexport interface Recipe {\n  id: string\n  name: string\n  code: string\n  category: string\n  batch_size: number\n  unit_of_measure: string\n  instructions?: string\n  is_active: boolean\n  version: number\n  created_at: string\n  updated_at: string\n}\n\n// Production Batches\nexport interface ProductionBatch {\n  id: string\n  batch_number: string\n  recipe_id: string\n  batch_type: 'test' | 'production'\n  planned_quantity: number\n  actual_quantity?: number\n  unit_of_measure: string\n  status: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n  priority: 'low' | 'normal' | 'high' | 'urgent'\n  planned_start_date?: string\n  planned_end_date?: string\n  actual_start_date?: string\n  actual_end_date?: string\n  assigned_to?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\n// Quality Documents\nexport interface QualityDocument {\n  id: string\n  document_type: 'msds' | 'coa' | 'tds' | 'quality_spec' | 'lab_report'\n  title: string\n  document_number: string\n  version: string\n  item_id?: string\n  batch_id?: string\n  status: 'draft' | 'review' | 'approved' | 'expired'\n  valid_from?: string\n  valid_until?: string\n  file_path?: string\n  created_by: string\n  created_at: string\n  updated_at: string\n}\n\n// Database service functions\nexport class DatabaseService {\n  // Inventory Items\n  static async getInventoryItems(): Promise<InventoryItem[]> {\n    const { data, error } = await supabase\n      .from('inventory_items')\n      .select(`\n        *,\n        inventory_categories(name)\n      `)\n      .eq('is_active', true)\n      .order('name')\n\n    if (error) {\n      console.error('Error fetching inventory items:', error)\n      return []\n    }\n\n    return data || []\n  }\n\n  static async createInventoryItem(item: Omit<InventoryItem, 'id' | 'created_at' | 'updated_at'>): Promise<InventoryItem | null> {\n    const { data, error } = await supabase\n      .from('inventory_items')\n      .insert([item])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error creating inventory item:', error)\n      return null\n    }\n\n    return data\n  }\n\n  static async updateInventoryItem(id: string, updates: Partial<InventoryItem>): Promise<InventoryItem | null> {\n    const { data, error } = await supabase\n      .from('inventory_items')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error updating inventory item:', error)\n      return null\n    }\n\n    return data\n  }\n\n  // Inventory Categories\n  static async getInventoryCategories(): Promise<InventoryCategory[]> {\n    const { data, error } = await supabase\n      .from('inventory_categories')\n      .select('*')\n      .eq('is_active', true)\n      .order('name')\n\n    if (error) {\n      console.error('Error fetching inventory categories:', error)\n      return []\n    }\n\n    return data || []\n  }\n\n  // Recipes\n  static async getRecipes(): Promise<Recipe[]> {\n    const { data, error } = await supabase\n      .from('recipes')\n      .select('*')\n      .eq('is_active', true)\n      .order('name')\n\n    if (error) {\n      console.error('Error fetching recipes:', error)\n      return []\n    }\n\n    return data || []\n  }\n\n  static async createRecipe(recipe: Omit<Recipe, 'id' | 'created_at' | 'updated_at'>): Promise<Recipe | null> {\n    const { data, error } = await supabase\n      .from('recipes')\n      .insert([recipe])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error creating recipe:', error)\n      return null\n    }\n\n    return data\n  }\n\n  // Production Batches\n  static async getProductionBatches(): Promise<ProductionBatch[]> {\n    const { data, error } = await supabase\n      .from('production_batches')\n      .select(`\n        *,\n        recipes(name, code)\n      `)\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      console.error('Error fetching production batches:', error)\n      return []\n    }\n\n    return data || []\n  }\n\n  static async createProductionBatch(batch: Omit<ProductionBatch, 'id' | 'created_at' | 'updated_at'>): Promise<ProductionBatch | null> {\n    const { data, error } = await supabase\n      .from('production_batches')\n      .insert([batch])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error creating production batch:', error)\n      return null\n    }\n\n    return data\n  }\n\n  // Quality Documents\n  static async getQualityDocuments(): Promise<QualityDocument[]> {\n    const { data, error } = await supabase\n      .from('quality_documents')\n      .select('*')\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      console.error('Error fetching quality documents:', error)\n      return []\n    }\n\n    return data || []\n  }\n\n  static async createQualityDocument(doc: Omit<QualityDocument, 'id' | 'created_at' | 'updated_at'>): Promise<QualityDocument | null> {\n    const { data, error } = await supabase\n      .from('quality_documents')\n      .insert([doc])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error creating quality document:', error)\n      return null\n    }\n\n    return data\n  }\n\n  // User Management\n  static async getUsers(): Promise<any[]> {\n    const { data, error } = await supabase\n      .from('profiles')\n      .select('*')\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      console.error('Error fetching users:', error)\n      return []\n    }\n\n    return data || []\n  }\n\n  static async createUser(userData: {\n    username: string\n    email: string\n    password_hash: string\n    role: string\n    full_name: string\n    department: string\n    phone?: string\n    is_active: boolean\n  }): Promise<any | null> {\n    const { data, error } = await supabase\n      .from('profiles')\n      .insert([userData])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error creating user:', error)\n      return null\n    }\n\n    return data\n  }\n\n  static async updateUser(id: string, updates: any): Promise<any | null> {\n    const { data, error } = await supabase\n      .from('profiles')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error updating user:', error)\n      return null\n    }\n\n    return data\n  }\n\n  static async toggleUserStatus(id: string, isActive: boolean): Promise<boolean> {\n    const { error } = await supabase\n      .from('profiles')\n      .update({ is_active: isActive, updated_at: new Date().toISOString() })\n      .eq('id', id)\n\n    if (error) {\n      console.error('Error toggling user status:', error)\n      return false\n    }\n\n    return true\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;AAiF7B,MAAM;IACX,kBAAkB;IAClB,aAAa,oBAA8C;QACzD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,mBACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO,EAAE;QACX;QAEA,OAAO,QAAQ,EAAE;IACnB;IAEA,aAAa,oBAAoB,IAA6D,EAAiC;QAC7H,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,mBACL,MAAM,CAAC;YAAC;SAAK,EACb,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,aAAa,oBAAoB,EAAU,EAAE,OAA+B,EAAiC;QAC3G,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,mBACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,uBAAuB;IACvB,aAAa,yBAAuD;QAClE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,wBACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO,EAAE;QACX;QAEA,OAAO,QAAQ,EAAE;IACnB;IAEA,UAAU;IACV,aAAa,aAAgC;QAC3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,EAAE;QACX;QAEA,OAAO,QAAQ,EAAE;IACnB;IAEA,aAAa,aAAa,MAAwD,EAA0B;QAC1G,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,WACL,MAAM,CAAC;YAAC;SAAO,EACf,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,qBAAqB;IACrB,aAAa,uBAAmD;QAC9D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,sBACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO,EAAE;QACX;QAEA,OAAO,QAAQ,EAAE;IACnB;IAEA,aAAa,sBAAsB,KAAgE,EAAmC;QACpI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,sBACL,MAAM,CAAC;YAAC;SAAM,EACd,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,oBAAoB;IACpB,aAAa,sBAAkD;QAC7D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO,EAAE;QACX;QAEA,OAAO,QAAQ,EAAE;IACnB;IAEA,aAAa,sBAAsB,GAA8D,EAAmC;QAClI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC;YAAC;SAAI,EACZ,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,kBAAkB;IAClB,aAAa,WAA2B;QACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO,EAAE;QACX;QAEA,OAAO,QAAQ,EAAE;IACnB;IAEA,aAAa,WAAW,QASvB,EAAuB;QACtB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;YAAC;SAAS,EACjB,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,aAAa,WAAW,EAAU,EAAE,OAAY,EAAuB;QACrE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,aAAa,iBAAiB,EAAU,EAAE,QAAiB,EAAoB;QAC7E,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,YACL,MAAM,CAAC;YAAE,WAAW;YAAU,YAAY,IAAI,OAAO,WAAW;QAAG,GACnE,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;QACT;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/app/production/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { useAuth } from '@/components/providers'\nimport { Modal } from '@/components/ui/modal'\nimport { DatabaseService } from '@/lib/database'\nimport {\n  PlusIcon,\n  CogIcon,\n  ClockIcon,\n  CheckCircleIcon,\n  TableCellsIcon,\n  ExclamationTriangleIcon,\n  DocumentArrowDownIcon,\n  ChartBarIcon,\n  BeakerIcon,\n  CalendarIcon,\n  UserIcon,\n  CurrencyDollarIcon\n} from '@heroicons/react/24/outline'\n\ninterface ProductionBatch {\n  id: string\n  batch_number: string\n  recipe_id: string\n  recipe_name: string\n  batch_type: 'test' | 'production'\n  planned_quantity: number\n  actual_quantity?: number\n  unit_of_measure: string\n  status: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n  priority: 'low' | 'normal' | 'high' | 'urgent'\n  planned_start_date: string\n  actual_start_date?: string\n  planned_end_date: string\n  actual_end_date?: string\n  assigned_to: string\n  progress: number\n  estimated_cost: number\n  actual_cost?: number\n  yield_percentage?: number\n  quality_status?: 'pending' | 'passed' | 'failed' | 'requires_retest'\n  notes?: string\n}\n\ninterface Recipe {\n  id: string\n  name: string\n  code: string\n  category: string\n  batch_size: number\n  unit_of_measure: string\n  estimated_cost: number\n  ingredients_count: number\n  version: number\n  is_active: boolean\n}\n\ninterface InventoryItem {\n  id: string\n  name: string\n  sku: string\n  current_stock: number\n  minimum_stock: number\n  unit_of_measure: string\n  unit_cost: number\n  category_name?: string\n}\n\ninterface MaterialAllocation {\n  id: string\n  inventory_item_id: string\n  inventory_item_name: string\n  required_quantity: number\n  allocated_quantity: number\n  unit_of_measure: string\n  unit_cost: number\n  total_cost: number\n  availability_status: 'available' | 'insufficient' | 'unavailable'\n}\n\ninterface BatchSpreadsheetData {\n  batch_number: string\n  recipe_name: string\n  batch_type: string\n  planned_quantity: number\n  actual_quantity: number\n  status: string\n  priority: string\n  assigned_to: string\n  planned_start: string\n  planned_end: string\n  actual_start: string\n  actual_end: string\n  estimated_cost: number\n  actual_cost: number\n  yield_percentage: number\n  quality_status: string\n  progress: number\n  materials_allocated: boolean\n  notes: string\n}\n\nexport default function ProductionPage() {\n  const { user } = useAuth()\n  const [batches, setBatches] = useState<ProductionBatch[]>([])\n  const [recipes, setRecipes] = useState<Recipe[]>([])\n  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showAddModal, setShowAddModal] = useState(false)\n  const [showSpreadsheetView, setShowSpreadsheetView] = useState(false)\n  const [currentStep, setCurrentStep] = useState(1)\n\n  // Batch creation state\n  const [newBatch, setNewBatch] = useState({\n    recipe_id: '',\n    batch_type: 'production' as 'test' | 'production',\n    planned_quantity: 0,\n    priority: 'normal' as 'low' | 'normal' | 'high' | 'urgent',\n    planned_start_date: '',\n    planned_end_date: '',\n    assigned_to: '',\n    notes: ''\n  })\n\n  // Material allocation state\n  const [materialAllocations, setMaterialAllocations] = useState<MaterialAllocation[]>([])\n  const [totalMaterialCost, setTotalMaterialCost] = useState(0)\n  const [allMaterialsAvailable, setAllMaterialsAvailable] = useState(false)\n  const [validationErrors, setValidationErrors] = useState<string[]>([])\n\n  // Spreadsheet data\n  const [spreadsheetData, setSpreadsheetData] = useState<BatchSpreadsheetData[]>([])\n  const [selectedBatches, setSelectedBatches] = useState<string[]>([])\n  const [sortField, setSortField] = useState<keyof BatchSpreadsheetData>('batch_number')\n  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')\n\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setLoading(true)\n\n        // Load production batches, recipes, and inventory from database\n        const [batchData, recipeData, inventoryData] = await Promise.all([\n          DatabaseService.getProductionBatches(),\n          DatabaseService.getRecipes(),\n          DatabaseService.getInventoryItems()\n        ])\n\n        // Process data\n        const processedBatches = batchData.map(batch => ({\n          ...batch,\n          estimated_cost: parseFloat(batch.estimated_cost?.toString() || '0'),\n          actual_cost: parseFloat(batch.actual_cost?.toString() || '0'),\n          yield_percentage: parseFloat(batch.yield_percentage?.toString() || '100')\n        }))\n\n        const processedInventory = inventoryData.map(item => ({\n          ...item,\n          current_stock: parseFloat(item.current_stock.toString()),\n          unit_cost: parseFloat(item.unit_cost.toString())\n        }))\n\n        setBatches(processedBatches)\n        setRecipes(recipeData)\n        setInventoryItems(processedInventory)\n\n        // Generate spreadsheet data\n        generateSpreadsheetData(processedBatches)\n\n        // Set default values for new batch\n        if (recipeData.length > 0 && !newBatch.recipe_id) {\n          const firstRecipe = recipeData[0]\n          setNewBatch(prev => ({\n            ...prev,\n            recipe_id: firstRecipe.id,\n            planned_quantity: firstRecipe.batch_size\n          }))\n        }\n      } catch (error) {\n        console.error('Error loading production data:', error)\n        // Fallback to mock data\n        const mockBatches: ProductionBatch[] = [\n          {\n            id: '1',\n            batch_number: 'SFF-20241211-001',\n            recipe_id: '1',\n            recipe_name: 'Strawberry Vanilla Blend',\n            batch_type: 'production',\n            planned_quantity: 10.0,\n            actual_quantity: 9.8,\n            unit_of_measure: 'liters',\n            status: 'completed',\n            priority: 'normal',\n            planned_start_date: '2024-12-11T08:00:00Z',\n            actual_start_date: '2024-12-11T08:15:00Z',\n            planned_end_date: '2024-12-11T16:00:00Z',\n            actual_end_date: '2024-12-11T15:45:00Z',\n            assigned_to: 'John Smith',\n            progress: 100,\n            estimated_cost: 185.50,\n            actual_cost: 178.25,\n            yield_percentage: 98,\n            quality_status: 'passed'\n          }\n        ]\n        setBatches(mockBatches)\n        generateSpreadsheetData(mockBatches)\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    loadData()\n  }, [])\n\n  // Generate spreadsheet data from batches\n  const generateSpreadsheetData = (batchList: ProductionBatch[]) => {\n    const spreadsheetRows: BatchSpreadsheetData[] = batchList.map(batch => ({\n      batch_number: batch.batch_number,\n      recipe_name: batch.recipe_name,\n      batch_type: batch.batch_type,\n      planned_quantity: batch.planned_quantity,\n      actual_quantity: batch.actual_quantity || 0,\n      status: batch.status,\n      priority: batch.priority,\n      assigned_to: batch.assigned_to,\n      planned_start: batch.planned_start_date,\n      planned_end: batch.planned_end_date,\n      actual_start: batch.actual_start_date || '',\n      actual_end: batch.actual_end_date || '',\n      estimated_cost: batch.estimated_cost,\n      actual_cost: batch.actual_cost || 0,\n      yield_percentage: batch.yield_percentage || 100,\n      quality_status: batch.quality_status || 'pending',\n      progress: batch.progress,\n      materials_allocated: materialAllocations.length > 0,\n      notes: batch.notes || ''\n    }))\n    setSpreadsheetData(spreadsheetRows)\n  }\n\n  // Calculate material requirements for selected recipe\n  const calculateMaterialRequirements = (recipeId: string, quantity: number) => {\n    const recipe = recipes.find(r => r.id === recipeId)\n    if (!recipe) return []\n\n    // Mock recipe ingredients - in real implementation, get from recipe_ingredients table\n    const mockIngredients = [\n      { inventory_item_id: '1', quantity_per_batch: 2.5 },\n      { inventory_item_id: '2', quantity_per_batch: 1.0 },\n      { inventory_item_id: '3', quantity_per_batch: 0.5 }\n    ]\n\n    const scaleFactor = quantity / recipe.batch_size\n    const allocations: MaterialAllocation[] = []\n\n    mockIngredients.forEach(ingredient => {\n      const inventoryItem = inventoryItems.find(item => item.id === ingredient.inventory_item_id)\n      if (inventoryItem) {\n        const requiredQuantity = ingredient.quantity_per_batch * scaleFactor\n        const totalCost = requiredQuantity * inventoryItem.unit_cost\n\n        allocations.push({\n          id: Date.now().toString() + Math.random(),\n          inventory_item_id: inventoryItem.id,\n          inventory_item_name: inventoryItem.name,\n          required_quantity: requiredQuantity,\n          allocated_quantity: Math.min(requiredQuantity, inventoryItem.current_stock),\n          unit_of_measure: inventoryItem.unit_of_measure,\n          unit_cost: inventoryItem.unit_cost,\n          total_cost: totalCost,\n          availability_status: inventoryItem.current_stock >= requiredQuantity ? 'available' :\n                              inventoryItem.current_stock > 0 ? 'insufficient' : 'unavailable'\n        })\n      }\n    })\n\n    return allocations\n  }\n\n  // Update material allocations when recipe or quantity changes\n  useEffect(() => {\n    if (newBatch.recipe_id && newBatch.planned_quantity > 0) {\n      const allocations = calculateMaterialRequirements(newBatch.recipe_id, newBatch.planned_quantity)\n      setMaterialAllocations(allocations)\n\n      const totalCost = allocations.reduce((sum, alloc) => sum + alloc.total_cost, 0)\n      setTotalMaterialCost(totalCost)\n\n      const allAvailable = allocations.every(alloc => alloc.availability_status === 'available')\n      setAllMaterialsAvailable(allAvailable)\n    }\n  }, [newBatch.recipe_id, newBatch.planned_quantity, inventoryItems])\n\n  const generateBatchNumber = () => {\n    const today = new Date()\n    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '')\n    const sequence = String(batches.length + 1).padStart(3, '0')\n    return `SFF-${dateStr}-${sequence}`\n  }\n\n  const validateBatch = () => {\n    const errors: string[] = []\n\n    if (!newBatch.recipe_id) errors.push('Recipe selection is required')\n    if (newBatch.planned_quantity <= 0) errors.push('Planned quantity must be greater than 0')\n    if (!newBatch.planned_start_date) errors.push('Planned start date is required')\n    if (!newBatch.planned_end_date) errors.push('Planned end date is required')\n    if (!newBatch.assigned_to) errors.push('Operator assignment is required')\n\n    if (new Date(newBatch.planned_end_date) <= new Date(newBatch.planned_start_date)) {\n      errors.push('End date must be after start date')\n    }\n\n    if (!allMaterialsAvailable) {\n      errors.push('Insufficient materials available for this batch')\n    }\n\n    setValidationErrors(errors)\n    return errors.length === 0\n  }\n\n  const handleCreateBatch = async () => {\n    if (!validateBatch()) return\n\n    try {\n      const selectedRecipe = recipes.find(r => r.id === newBatch.recipe_id)\n      if (!selectedRecipe) return\n\n      const batch: ProductionBatch = {\n        id: (batches.length + 1).toString(),\n        batch_number: generateBatchNumber(),\n        recipe_id: newBatch.recipe_id,\n        recipe_name: selectedRecipe.name,\n        batch_type: newBatch.batch_type,\n        planned_quantity: newBatch.planned_quantity,\n        unit_of_measure: selectedRecipe.unit_of_measure,\n        status: 'planned',\n        priority: newBatch.priority,\n        planned_start_date: newBatch.planned_start_date,\n        planned_end_date: newBatch.planned_end_date,\n        assigned_to: newBatch.assigned_to,\n        progress: 0,\n        estimated_cost: totalMaterialCost,\n        quality_status: 'pending',\n        notes: newBatch.notes\n      }\n\n      // In real implementation, save to database and allocate materials\n      setBatches([...batches, batch])\n      generateSpreadsheetData([...batches, batch])\n\n      resetForm()\n      setShowAddModal(false)\n\n      alert(`Batch ${batch.batch_number} created successfully!\\nEstimated Cost: $${totalMaterialCost.toFixed(2)}\\nMaterials: ${materialAllocations.length} items allocated`)\n    } catch (error) {\n      console.error('Error creating batch:', error)\n      alert('Error creating batch. Please try again.')\n    }\n  }\n\n  const resetForm = () => {\n    setCurrentStep(1)\n    setNewBatch({\n      recipe_id: recipes.length > 0 ? recipes[0].id : '',\n      batch_type: 'production',\n      planned_quantity: recipes.length > 0 ? recipes[0].batch_size : 0,\n      priority: 'normal',\n      planned_start_date: '',\n      planned_end_date: '',\n      assigned_to: '',\n      notes: ''\n    })\n    setMaterialAllocations([])\n    setValidationErrors([])\n  }\n\n  const nextStep = () => {\n    if (currentStep < 3) setCurrentStep(currentStep + 1)\n  }\n\n  const prevStep = () => {\n    if (currentStep > 1) setCurrentStep(currentStep - 1)\n  }\n\n  // Export spreadsheet data\n  const exportToSpreadsheet = () => {\n    const csvContent = [\n      // Headers\n      Object.keys(spreadsheetData[0] || {}).join(','),\n      // Data rows\n      ...spreadsheetData.map(row => Object.values(row).map(val =>\n        typeof val === 'string' && val.includes(',') ? `\"${val}\"` : val\n      ).join(','))\n    ].join('\\n')\n\n    const blob = new Blob([csvContent], { type: 'text/csv' })\n    const url = URL.createObjectURL(blob)\n    const link = document.createElement('a')\n    link.href = url\n    link.download = `production-batches-${new Date().toISOString().slice(0, 10)}.csv`\n    document.body.appendChild(link)\n    link.click()\n    document.body.removeChild(link)\n    URL.revokeObjectURL(url)\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'bg-green-100 text-green-800'\n      case 'in_progress':\n        return 'bg-blue-100 text-blue-800'\n      case 'planned':\n        return 'bg-gray-100 text-gray-800'\n      case 'on_hold':\n        return 'bg-yellow-100 text-yellow-800'\n      case 'cancelled':\n        return 'bg-red-100 text-red-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'urgent':\n        return 'bg-red-100 text-red-800'\n      case 'high':\n        return 'bg-orange-100 text-orange-800'\n      case 'normal':\n        return 'bg-blue-100 text-blue-800'\n      case 'low':\n        return 'bg-gray-100 text-gray-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    })\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return (\n    <MainLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"sm:flex sm:items-center sm:justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Production Management</h1>\n            <p className=\"mt-2 text-sm text-gray-700\">\n              Professional batch management with inventory integration and spreadsheet analytics\n            </p>\n          </div>\n          <div className=\"mt-4 sm:mt-0 flex space-x-3\">\n            <button\n              type=\"button\"\n              onClick={() => setShowSpreadsheetView(!showSpreadsheetView)}\n              className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium ${\n                showSpreadsheetView\n                  ? 'text-white bg-green-600 hover:bg-green-700'\n                  : 'text-gray-700 bg-white hover:bg-gray-50'\n              }`}\n            >\n              <TableCellsIcon className=\"-ml-1 mr-2 h-5 w-5\" aria-hidden=\"true\" />\n              {showSpreadsheetView ? 'Card View' : 'Spreadsheet View'}\n            </button>\n            <button\n              type=\"button\"\n              onClick={exportToSpreadsheet}\n              className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n            >\n              <DocumentArrowDownIcon className=\"-ml-1 mr-2 h-5 w-5\" aria-hidden=\"true\" />\n              Export CSV\n            </button>\n            <button\n              type=\"button\"\n              onClick={() => { resetForm(); setShowAddModal(true); }}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n            >\n              <PlusIcon className=\"-ml-1 mr-2 h-5 w-5\" aria-hidden=\"true\" />\n              New Batch\n            </button>\n          </div>\n        </div>\n\n        {/* Enhanced Stats Cards */}\n        <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-6\">\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <CogIcon className=\"h-6 w-6 text-blue-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">In Progress</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {batches.filter(b => b.status === 'in_progress').length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <ClockIcon className=\"h-6 w-6 text-gray-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Planned</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {batches.filter(b => b.status === 'planned').length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <CheckCircleIcon className=\"h-6 w-6 text-green-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Completed</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {batches.filter(b => b.status === 'completed').length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <BeakerIcon className=\"h-6 w-6 text-purple-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Test Batches</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {batches.filter(b => b.batch_type === 'test').length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <CurrencyDollarIcon className=\"h-6 w-6 text-green-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Value</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      ${batches.reduce((sum, b) => sum + (b.estimated_cost || 0), 0).toFixed(0)}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <ChartBarIcon className=\"h-6 w-6 text-orange-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Avg Yield</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {batches.length > 0\n                        ? (batches.reduce((sum, b) => sum + (b.yield_percentage || 100), 0) / batches.length).toFixed(1)\n                        : '0'}%\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Production Batches - Conditional View */}\n        {showSpreadsheetView ? (\n          /* Professional Spreadsheet View */\n          <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n            <div className=\"px-4 py-5 sm:p-6\">\n              <div className=\"flex justify-between items-center mb-4\">\n                <h3 className=\"text-lg leading-6 font-medium text-gray-900\">\n                  Production Spreadsheet ({spreadsheetData.length} batches)\n                </h3>\n                <div className=\"flex space-x-2\">\n                  <select\n                    className=\"text-sm border-gray-300 rounded-md\"\n                    value={`${sortField}-${sortDirection}`}\n                    onChange={(e) => {\n                      const [field, direction] = e.target.value.split('-')\n                      setSortField(field as keyof BatchSpreadsheetData)\n                      setSortDirection(direction as 'asc' | 'desc')\n                    }}\n                  >\n                    <option value=\"batch_number-desc\">Batch Number (Newest)</option>\n                    <option value=\"batch_number-asc\">Batch Number (Oldest)</option>\n                    <option value=\"status-asc\">Status (A-Z)</option>\n                    <option value=\"priority-desc\">Priority (High-Low)</option>\n                    <option value=\"estimated_cost-desc\">Cost (High-Low)</option>\n                    <option value=\"planned_start-desc\">Start Date (Recent)</option>\n                  </select>\n                </div>\n              </div>\n\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full divide-y divide-gray-200 text-xs\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider\">Batch #</th>\n                      <th className=\"px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider\">Recipe</th>\n                      <th className=\"px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider\">Type</th>\n                      <th className=\"px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider\">Planned Qty</th>\n                      <th className=\"px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider\">Actual Qty</th>\n                      <th className=\"px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider\">Status</th>\n                      <th className=\"px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider\">Priority</th>\n                      <th className=\"px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider\">Assigned</th>\n                      <th className=\"px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider\">Start Date</th>\n                      <th className=\"px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider\">End Date</th>\n                      <th className=\"px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider\">Est. Cost</th>\n                      <th className=\"px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider\">Actual Cost</th>\n                      <th className=\"px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider\">Yield %</th>\n                      <th className=\"px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider\">Quality</th>\n                      <th className=\"px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider\">Progress</th>\n                      <th className=\"px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider\">Materials</th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {spreadsheetData\n                      .sort((a, b) => {\n                        const aVal = a[sortField]\n                        const bVal = b[sortField]\n                        if (sortDirection === 'asc') {\n                          return aVal < bVal ? -1 : aVal > bVal ? 1 : 0\n                        } else {\n                          return aVal > bVal ? -1 : aVal < bVal ? 1 : 0\n                        }\n                      })\n                      .map((row, index) => (\n                        <tr key={index} className=\"hover:bg-gray-50\">\n                          <td className=\"px-2 py-2 whitespace-nowrap font-medium text-gray-900\">{row.batch_number}</td>\n                          <td className=\"px-2 py-2 whitespace-nowrap text-gray-900\">{row.recipe_name}</td>\n                          <td className=\"px-2 py-2 whitespace-nowrap\">\n                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                              row.batch_type === 'test' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'\n                            }`}>\n                              {row.batch_type}\n                            </span>\n                          </td>\n                          <td className=\"px-2 py-2 whitespace-nowrap text-gray-900\">{row.planned_quantity}</td>\n                          <td className=\"px-2 py-2 whitespace-nowrap text-gray-900\">{row.actual_quantity || '-'}</td>\n                          <td className=\"px-2 py-2 whitespace-nowrap\">\n                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(row.status)}`}>\n                              {row.status.replace('_', ' ').toUpperCase()}\n                            </span>\n                          </td>\n                          <td className=\"px-2 py-2 whitespace-nowrap\">\n                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(row.priority)}`}>\n                              {row.priority.toUpperCase()}\n                            </span>\n                          </td>\n                          <td className=\"px-2 py-2 whitespace-nowrap text-gray-900\">{row.assigned_to}</td>\n                          <td className=\"px-2 py-2 whitespace-nowrap text-gray-900\">\n                            {row.planned_start ? formatDate(row.planned_start) : '-'}\n                          </td>\n                          <td className=\"px-2 py-2 whitespace-nowrap text-gray-900\">\n                            {row.planned_end ? formatDate(row.planned_end) : '-'}\n                          </td>\n                          <td className=\"px-2 py-2 whitespace-nowrap text-gray-900\">${row.estimated_cost.toFixed(2)}</td>\n                          <td className=\"px-2 py-2 whitespace-nowrap text-gray-900\">\n                            {row.actual_cost > 0 ? `$${row.actual_cost.toFixed(2)}` : '-'}\n                          </td>\n                          <td className=\"px-2 py-2 whitespace-nowrap text-gray-900\">{row.yield_percentage.toFixed(1)}%</td>\n                          <td className=\"px-2 py-2 whitespace-nowrap\">\n                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                              row.quality_status === 'passed' ? 'bg-green-100 text-green-800' :\n                              row.quality_status === 'failed' ? 'bg-red-100 text-red-800' :\n                              row.quality_status === 'requires_retest' ? 'bg-yellow-100 text-yellow-800' :\n                              'bg-gray-100 text-gray-800'\n                            }`}>\n                              {row.quality_status}\n                            </span>\n                          </td>\n                          <td className=\"px-2 py-2 whitespace-nowrap\">\n                            <div className=\"flex items-center\">\n                              <div className=\"w-12 bg-gray-200 rounded-full h-1.5 mr-1\">\n                                <div\n                                  className=\"bg-green-600 h-1.5 rounded-full\"\n                                  style={{ width: `${row.progress}%` }}\n                                ></div>\n                              </div>\n                              <span className=\"text-xs\">{row.progress}%</span>\n                            </div>\n                          </td>\n                          <td className=\"px-2 py-2 whitespace-nowrap\">\n                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                              row.materials_allocated ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n                            }`}>\n                              {row.materials_allocated ? 'Allocated' : 'Pending'}\n                            </span>\n                          </td>\n                        </tr>\n                      ))}\n                  </tbody>\n                </table>\n              </div>\n            </div>\n          </div>\n        ) : (\n          /* Traditional Card View */\n          <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n            <div className=\"px-4 py-5 sm:p-6\">\n              <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n                Production Batches ({batches.length})\n              </h3>\n            \n            {loading ? (\n              <div className=\"animate-pulse\">\n                {[...Array(4)].map((_, i) => (\n                  <div key={i} className=\"flex items-center space-x-4 py-4\">\n                    <div className=\"h-4 bg-gray-200 rounded w-1/4\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full divide-y divide-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Batch\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Recipe\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Quantity\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Priority\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Progress\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Assigned To\n                      </th>\n                      <th className=\"relative px-6 py-3\">\n                        <span className=\"sr-only\">Actions</span>\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {batches.map((batch) => (\n                      <tr key={batch.id} className=\"hover:bg-gray-50\">\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div>\n                            <div className=\"text-sm font-medium text-gray-900\">{batch.batch_number}</div>\n                            <div className=\"text-sm text-gray-500\">\n                              {batch.batch_type === 'test' ? 'Test Batch' : 'Production'}\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {batch.recipe_name}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"text-sm text-gray-900\">\n                            {batch.actual_quantity || batch.planned_quantity} {batch.unit_of_measure}\n                          </div>\n                          {batch.actual_quantity && (\n                            <div className=\"text-sm text-gray-500\">\n                              Planned: {batch.planned_quantity}\n                            </div>\n                          )}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(batch.status)}`}>\n                            {batch.status.replace('_', ' ').toUpperCase()}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(batch.priority)}`}>\n                            {batch.priority.toUpperCase()}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"flex items-center\">\n                            <div className=\"w-16 bg-gray-200 rounded-full h-2 mr-2\">\n                              <div \n                                className=\"bg-green-600 h-2 rounded-full\" \n                                style={{ width: `${batch.progress}%` }}\n                              ></div>\n                            </div>\n                            <span className=\"text-sm text-gray-900\">{batch.progress}%</span>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {batch.assigned_to}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                          <button className=\"text-green-600 hover:text-green-900 mr-3\">\n                            View\n                          </button>\n                          <button className=\"text-green-600 hover:text-green-900\">\n                            Edit\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n        </div>\n        )}\n\n        {/* Professional Batch Creation Modal */}\n        <Modal\n          isOpen={showAddModal}\n          onClose={() => { resetForm(); setShowAddModal(false); }}\n          title={`Create Production Batch - Step ${currentStep} of 3`}\n          maxWidth=\"2xl\"\n        >\n          <div className=\"space-y-6\">\n            {/* Progress Steps */}\n            <div className=\"flex items-center justify-between\">\n              {[1, 2, 3].map((step) => (\n                <div key={step} className=\"flex items-center\">\n                  <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${\n                    step <= currentStep\n                      ? 'bg-green-600 border-green-600 text-white'\n                      : 'border-gray-300 text-gray-500'\n                  }`}>\n                    {step < currentStep ? (\n                      <CheckCircleIcon className=\"w-5 h-5\" />\n                    ) : (\n                      <span className=\"text-sm font-medium\">{step}</span>\n                    )}\n                  </div>\n                  <div className=\"ml-2 text-sm font-medium text-gray-900\">\n                    {step === 1 && 'Recipe & Schedule'}\n                    {step === 2 && 'Material Allocation'}\n                    {step === 3 && 'Review & Create'}\n                  </div>\n                  {step < 3 && (\n                    <div className={`ml-4 w-16 h-0.5 ${\n                      step < currentStep ? 'bg-green-600' : 'bg-gray-300'\n                    }`} />\n                  )}\n                </div>\n              ))}\n            </div>\n\n            {/* Validation Errors */}\n            {validationErrors.length > 0 && (\n              <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n                <div className=\"flex\">\n                  <ExclamationTriangleIcon className=\"h-5 w-5 text-red-400\" />\n                  <div className=\"ml-3\">\n                    <h3 className=\"text-sm font-medium text-red-800\">Please fix the following errors:</h3>\n                    <ul className=\"mt-2 text-sm text-red-700 list-disc list-inside\">\n                      {validationErrors.map((error, index) => (\n                        <li key={index}>{error}</li>\n                      ))}\n                    </ul>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Step Content */}\n            <div className=\"min-h-96\">\n              {currentStep === 1 && (\n                <div className=\"space-y-4\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">Recipe Selection & Scheduling</h3>\n                  <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Recipe *</label>\n                      <select\n                        required\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newBatch.recipe_id}\n                        onChange={(e) => setNewBatch({ ...newBatch, recipe_id: e.target.value })}\n                      >\n                        <option value=\"\">Select a recipe...</option>\n                        {recipes.map(recipe => (\n                          <option key={recipe.id} value={recipe.id}>\n                            {recipe.name} (Standard: {recipe.batch_size} {recipe.unit_of_measure})\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Batch Type</label>\n                      <select\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newBatch.batch_type}\n                        onChange={(e) => setNewBatch({ ...newBatch, batch_type: e.target.value as 'test' | 'production' })}\n                      >\n                        <option value=\"production\">Production Batch</option>\n                        <option value=\"test\">Test Batch</option>\n                      </select>\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Planned Quantity *</label>\n                      <input\n                        type=\"number\"\n                        min=\"0.1\"\n                        step=\"0.1\"\n                        required\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newBatch.planned_quantity}\n                        onChange={(e) => setNewBatch({ ...newBatch, planned_quantity: parseFloat(e.target.value) || 0 })}\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Priority</label>\n                      <select\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newBatch.priority}\n                        onChange={(e) => setNewBatch({ ...newBatch, priority: e.target.value as any })}\n                      >\n                        <option value=\"low\">Low Priority</option>\n                        <option value=\"normal\">Normal Priority</option>\n                        <option value=\"high\">High Priority</option>\n                        <option value=\"urgent\">Urgent</option>\n                      </select>\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Planned Start Date *</label>\n                      <input\n                        type=\"datetime-local\"\n                        required\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newBatch.planned_start_date}\n                        onChange={(e) => setNewBatch({ ...newBatch, planned_start_date: e.target.value })}\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Planned End Date *</label>\n                      <input\n                        type=\"datetime-local\"\n                        required\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newBatch.planned_end_date}\n                        onChange={(e) => setNewBatch({ ...newBatch, planned_end_date: e.target.value })}\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Assigned Operator *</label>\n                      <select\n                        required\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newBatch.assigned_to}\n                        onChange={(e) => setNewBatch({ ...newBatch, assigned_to: e.target.value })}\n                      >\n                        <option value=\"\">Select operator...</option>\n                        <option value=\"John Smith\">John Smith</option>\n                        <option value=\"Sarah Johnson\">Sarah Johnson</option>\n                        <option value=\"Mike Wilson\">Mike Wilson</option>\n                        <option value=\"Emily Davis\">Emily Davis</option>\n                      </select>\n                    </div>\n                    <div className=\"sm:col-span-2\">\n                      <label className=\"block text-sm font-medium text-gray-700\">Production Notes</label>\n                      <textarea\n                        rows={3}\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                        value={newBatch.notes}\n                        onChange={(e) => setNewBatch({ ...newBatch, notes: e.target.value })}\n                        placeholder=\"Special instructions, quality requirements, etc...\"\n                      />\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {currentStep === 2 && (\n                <div className=\"space-y-4\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">Material Allocation & Inventory Check</h3>\n\n                  {materialAllocations.length === 0 ? (\n                    <div className=\"text-center py-8 text-gray-500\">\n                      <BeakerIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                      <p className=\"mt-2\">Select a recipe and quantity in Step 1 to see material requirements</p>\n                    </div>\n                  ) : (\n                    <div className=\"space-y-4\">\n                      {/* Material Allocation Summary */}\n                      <div className=\"bg-gray-50 p-4 rounded-lg\">\n                        <h4 className=\"font-medium text-gray-900 mb-3\">Material Requirements Summary</h4>\n                        <div className=\"grid grid-cols-3 gap-4 text-sm\">\n                          <div>\n                            <span className=\"font-medium\">Total Materials:</span> {materialAllocations.length}\n                          </div>\n                          <div>\n                            <span className=\"font-medium\">Total Cost:</span> ${totalMaterialCost.toFixed(2)}\n                          </div>\n                          <div>\n                            <span className={`font-medium ${allMaterialsAvailable ? 'text-green-600' : 'text-red-600'}`}>\n                              Status: {allMaterialsAvailable ? 'All Available' : 'Insufficient Stock'}\n                            </span>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Material Allocation Table */}\n                      <div className=\"overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg\">\n                        <table className=\"min-w-full divide-y divide-gray-300\">\n                          <thead className=\"bg-gray-50\">\n                            <tr>\n                              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Material</th>\n                              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Required</th>\n                              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Available</th>\n                              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Unit Cost</th>\n                              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Total Cost</th>\n                              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Status</th>\n                            </tr>\n                          </thead>\n                          <tbody className=\"bg-white divide-y divide-gray-200\">\n                            {materialAllocations.map((allocation) => (\n                              <tr key={allocation.id}>\n                                <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                                  {allocation.inventory_item_name}\n                                </td>\n                                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                                  {allocation.required_quantity} {allocation.unit_of_measure}\n                                </td>\n                                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                                  {allocation.allocated_quantity} {allocation.unit_of_measure}\n                                </td>\n                                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                                  ${allocation.unit_cost.toFixed(2)}\n                                </td>\n                                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                                  ${allocation.total_cost.toFixed(2)}\n                                </td>\n                                <td className=\"px-6 py-4 whitespace-nowrap\">\n                                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                                    allocation.availability_status === 'available'\n                                      ? 'bg-green-100 text-green-800'\n                                      : allocation.availability_status === 'insufficient'\n                                      ? 'bg-yellow-100 text-yellow-800'\n                                      : 'bg-red-100 text-red-800'\n                                  }`}>\n                                    {allocation.availability_status}\n                                  </span>\n                                </td>\n                              </tr>\n                            ))}\n                          </tbody>\n                        </table>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              )}\n\n              {currentStep === 3 && (\n                <div className=\"space-y-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">Review & Create Batch</h3>\n\n                  {/* Batch Summary */}\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\n                    <h4 className=\"font-medium text-gray-900 mb-3\">Batch Summary</h4>\n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div><span className=\"font-medium\">Recipe:</span> {recipes.find(r => r.id === newBatch.recipe_id)?.name}</div>\n                      <div><span className=\"font-medium\">Batch Type:</span> {newBatch.batch_type}</div>\n                      <div><span className=\"font-medium\">Quantity:</span> {newBatch.planned_quantity} {recipes.find(r => r.id === newBatch.recipe_id)?.unit_of_measure}</div>\n                      <div><span className=\"font-medium\">Priority:</span> {newBatch.priority}</div>\n                      <div><span className=\"font-medium\">Start Date:</span> {newBatch.planned_start_date ? new Date(newBatch.planned_start_date).toLocaleString() : 'Not set'}</div>\n                      <div><span className=\"font-medium\">End Date:</span> {newBatch.planned_end_date ? new Date(newBatch.planned_end_date).toLocaleString() : 'Not set'}</div>\n                      <div><span className=\"font-medium\">Assigned To:</span> {newBatch.assigned_to}</div>\n                      <div><span className=\"font-medium\">Estimated Cost:</span> ${totalMaterialCost.toFixed(2)}</div>\n                    </div>\n                  </div>\n\n                  {/* Material Summary */}\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\n                    <h4 className=\"font-medium text-gray-900 mb-3\">Material Allocation ({materialAllocations.length} items)</h4>\n                    <div className=\"space-y-2\">\n                      {materialAllocations.map((allocation) => (\n                        <div key={allocation.id} className=\"flex justify-between text-sm\">\n                          <span>{allocation.inventory_item_name}</span>\n                          <span>{allocation.required_quantity} {allocation.unit_of_measure} (${allocation.total_cost.toFixed(2)})</span>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n\n                  {/* Final Validation */}\n                  <div className={`p-4 rounded-lg ${allMaterialsAvailable ? 'bg-green-50' : 'bg-red-50'}`}>\n                    <div className=\"flex\">\n                      {allMaterialsAvailable ? (\n                        <CheckCircleIcon className=\"h-5 w-5 text-green-400\" />\n                      ) : (\n                        <ExclamationTriangleIcon className=\"h-5 w-5 text-red-400\" />\n                      )}\n                      <div className=\"ml-3\">\n                        <h3 className={`text-sm font-medium ${allMaterialsAvailable ? 'text-green-800' : 'text-red-800'}`}>\n                          {allMaterialsAvailable ? 'Ready to Create Batch' : 'Material Shortage Detected'}\n                        </h3>\n                        <p className={`mt-1 text-sm ${allMaterialsAvailable ? 'text-green-700' : 'text-red-700'}`}>\n                          {allMaterialsAvailable\n                            ? 'All required materials are available. The batch can be created and materials will be allocated.'\n                            : 'Some materials have insufficient stock. Please check inventory or adjust batch quantity.'\n                          }\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Navigation Buttons */}\n            <div className=\"flex justify-between pt-6 border-t border-gray-200\">\n              <button\n                type=\"button\"\n                onClick={currentStep === 1 ? () => { resetForm(); setShowAddModal(false); } : prevStep}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n              >\n                {currentStep === 1 ? 'Cancel' : 'Previous'}\n              </button>\n\n              <div className=\"flex space-x-3\">\n                {currentStep < 3 ? (\n                  <button\n                    type=\"button\"\n                    onClick={nextStep}\n                    disabled={currentStep === 1 && (!newBatch.recipe_id || newBatch.planned_quantity <= 0)}\n                    className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400\"\n                  >\n                    Next Step\n                  </button>\n                ) : (\n                  <button\n                    type=\"button\"\n                    onClick={handleCreateBatch}\n                    disabled={!allMaterialsAvailable}\n                    className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400\"\n                  >\n                    Create Batch\n                  </button>\n                )}\n              </div>\n            </div>\n          </div>\n        </Modal>\n      </div>\n    </MainLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAwGe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,uBAAuB;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,YAAY;QACZ,kBAAkB;QAClB,UAAU;QACV,oBAAoB;QACpB,kBAAkB;QAClB,aAAa;QACb,OAAO;IACT;IAEA,4BAA4B;IAC5B,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB,EAAE;IACvF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAErE,mBAAmB;IACnB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,EAAE;IACjF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACvE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAEnE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;qDAAW;oBACf,IAAI;wBACF,WAAW;wBAEX,gEAAgE;wBAChE,MAAM,CAAC,WAAW,YAAY,cAAc,GAAG,MAAM,QAAQ,GAAG,CAAC;4BAC/D,yHAAA,CAAA,kBAAe,CAAC,oBAAoB;4BACpC,yHAAA,CAAA,kBAAe,CAAC,UAAU;4BAC1B,yHAAA,CAAA,kBAAe,CAAC,iBAAiB;yBAClC;wBAED,eAAe;wBACf,MAAM,mBAAmB,UAAU,GAAG;kFAAC,CAAA,QAAS,CAAC;oCAC/C,GAAG,KAAK;oCACR,gBAAgB,WAAW,MAAM,cAAc,EAAE,cAAc;oCAC/D,aAAa,WAAW,MAAM,WAAW,EAAE,cAAc;oCACzD,kBAAkB,WAAW,MAAM,gBAAgB,EAAE,cAAc;gCACrE,CAAC;;wBAED,MAAM,qBAAqB,cAAc,GAAG;oFAAC,CAAA,OAAQ,CAAC;oCACpD,GAAG,IAAI;oCACP,eAAe,WAAW,KAAK,aAAa,CAAC,QAAQ;oCACrD,WAAW,WAAW,KAAK,SAAS,CAAC,QAAQ;gCAC/C,CAAC;;wBAED,WAAW;wBACX,WAAW;wBACX,kBAAkB;wBAElB,4BAA4B;wBAC5B,wBAAwB;wBAExB,mCAAmC;wBACnC,IAAI,WAAW,MAAM,GAAG,KAAK,CAAC,SAAS,SAAS,EAAE;4BAChD,MAAM,cAAc,UAAU,CAAC,EAAE;4BACjC;qEAAY,CAAA,OAAQ,CAAC;wCACnB,GAAG,IAAI;wCACP,WAAW,YAAY,EAAE;wCACzB,kBAAkB,YAAY,UAAU;oCAC1C,CAAC;;wBACH;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,kCAAkC;wBAChD,wBAAwB;wBACxB,MAAM,cAAiC;4BACrC;gCACE,IAAI;gCACJ,cAAc;gCACd,WAAW;gCACX,aAAa;gCACb,YAAY;gCACZ,kBAAkB;gCAClB,iBAAiB;gCACjB,iBAAiB;gCACjB,QAAQ;gCACR,UAAU;gCACV,oBAAoB;gCACpB,mBAAmB;gCACnB,kBAAkB;gCAClB,iBAAiB;gCACjB,aAAa;gCACb,UAAU;gCACV,gBAAgB;gCAChB,aAAa;gCACb,kBAAkB;gCAClB,gBAAgB;4BAClB;yBACD;wBACD,WAAW;wBACX,wBAAwB;oBAC1B,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;mCAAG,EAAE;IAEL,yCAAyC;IACzC,MAAM,0BAA0B,CAAC;QAC/B,MAAM,kBAA0C,UAAU,GAAG,CAAC,CAAA,QAAS,CAAC;gBACtE,cAAc,MAAM,YAAY;gBAChC,aAAa,MAAM,WAAW;gBAC9B,YAAY,MAAM,UAAU;gBAC5B,kBAAkB,MAAM,gBAAgB;gBACxC,iBAAiB,MAAM,eAAe,IAAI;gBAC1C,QAAQ,MAAM,MAAM;gBACpB,UAAU,MAAM,QAAQ;gBACxB,aAAa,MAAM,WAAW;gBAC9B,eAAe,MAAM,kBAAkB;gBACvC,aAAa,MAAM,gBAAgB;gBACnC,cAAc,MAAM,iBAAiB,IAAI;gBACzC,YAAY,MAAM,eAAe,IAAI;gBACrC,gBAAgB,MAAM,cAAc;gBACpC,aAAa,MAAM,WAAW,IAAI;gBAClC,kBAAkB,MAAM,gBAAgB,IAAI;gBAC5C,gBAAgB,MAAM,cAAc,IAAI;gBACxC,UAAU,MAAM,QAAQ;gBACxB,qBAAqB,oBAAoB,MAAM,GAAG;gBAClD,OAAO,MAAM,KAAK,IAAI;YACxB,CAAC;QACD,mBAAmB;IACrB;IAEA,sDAAsD;IACtD,MAAM,gCAAgC,CAAC,UAAkB;QACvD,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,IAAI,CAAC,QAAQ,OAAO,EAAE;QAEtB,sFAAsF;QACtF,MAAM,kBAAkB;YACtB;gBAAE,mBAAmB;gBAAK,oBAAoB;YAAI;YAClD;gBAAE,mBAAmB;gBAAK,oBAAoB;YAAI;YAClD;gBAAE,mBAAmB;gBAAK,oBAAoB;YAAI;SACnD;QAED,MAAM,cAAc,WAAW,OAAO,UAAU;QAChD,MAAM,cAAoC,EAAE;QAE5C,gBAAgB,OAAO,CAAC,CAAA;YACtB,MAAM,gBAAgB,eAAe,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,WAAW,iBAAiB;YAC1F,IAAI,eAAe;gBACjB,MAAM,mBAAmB,WAAW,kBAAkB,GAAG;gBACzD,MAAM,YAAY,mBAAmB,cAAc,SAAS;gBAE5D,YAAY,IAAI,CAAC;oBACf,IAAI,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM;oBACvC,mBAAmB,cAAc,EAAE;oBACnC,qBAAqB,cAAc,IAAI;oBACvC,mBAAmB;oBACnB,oBAAoB,KAAK,GAAG,CAAC,kBAAkB,cAAc,aAAa;oBAC1E,iBAAiB,cAAc,eAAe;oBAC9C,WAAW,cAAc,SAAS;oBAClC,YAAY;oBACZ,qBAAqB,cAAc,aAAa,IAAI,mBAAmB,cACnD,cAAc,aAAa,GAAG,IAAI,iBAAiB;gBACzE;YACF;QACF;QAEA,OAAO;IACT;IAEA,8DAA8D;IAC9D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,SAAS,SAAS,IAAI,SAAS,gBAAgB,GAAG,GAAG;gBACvD,MAAM,cAAc,8BAA8B,SAAS,SAAS,EAAE,SAAS,gBAAgB;gBAC/F,uBAAuB;gBAEvB,MAAM,YAAY,YAAY,MAAM;0DAAC,CAAC,KAAK,QAAU,MAAM,MAAM,UAAU;yDAAE;gBAC7E,qBAAqB;gBAErB,MAAM,eAAe,YAAY,KAAK;6DAAC,CAAA,QAAS,MAAM,mBAAmB,KAAK;;gBAC9E,yBAAyB;YAC3B;QACF;mCAAG;QAAC,SAAS,SAAS;QAAE,SAAS,gBAAgB;QAAE;KAAe;IAElE,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,IAAI;QAClB,MAAM,UAAU,MAAM,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;QAC/D,MAAM,WAAW,OAAO,QAAQ,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG;QACxD,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,UAAU;IACrC;IAEA,MAAM,gBAAgB;QACpB,MAAM,SAAmB,EAAE;QAE3B,IAAI,CAAC,SAAS,SAAS,EAAE,OAAO,IAAI,CAAC;QACrC,IAAI,SAAS,gBAAgB,IAAI,GAAG,OAAO,IAAI,CAAC;QAChD,IAAI,CAAC,SAAS,kBAAkB,EAAE,OAAO,IAAI,CAAC;QAC9C,IAAI,CAAC,SAAS,gBAAgB,EAAE,OAAO,IAAI,CAAC;QAC5C,IAAI,CAAC,SAAS,WAAW,EAAE,OAAO,IAAI,CAAC;QAEvC,IAAI,IAAI,KAAK,SAAS,gBAAgB,KAAK,IAAI,KAAK,SAAS,kBAAkB,GAAG;YAChF,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,CAAC,uBAAuB;YAC1B,OAAO,IAAI,CAAC;QACd;QAEA,oBAAoB;QACpB,OAAO,OAAO,MAAM,KAAK;IAC3B;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,iBAAiB;QAEtB,IAAI;YACF,MAAM,iBAAiB,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,SAAS;YACpE,IAAI,CAAC,gBAAgB;YAErB,MAAM,QAAyB;gBAC7B,IAAI,CAAC,QAAQ,MAAM,GAAG,CAAC,EAAE,QAAQ;gBACjC,cAAc;gBACd,WAAW,SAAS,SAAS;gBAC7B,aAAa,eAAe,IAAI;gBAChC,YAAY,SAAS,UAAU;gBAC/B,kBAAkB,SAAS,gBAAgB;gBAC3C,iBAAiB,eAAe,eAAe;gBAC/C,QAAQ;gBACR,UAAU,SAAS,QAAQ;gBAC3B,oBAAoB,SAAS,kBAAkB;gBAC/C,kBAAkB,SAAS,gBAAgB;gBAC3C,aAAa,SAAS,WAAW;gBACjC,UAAU;gBACV,gBAAgB;gBAChB,gBAAgB;gBAChB,OAAO,SAAS,KAAK;YACvB;YAEA,kEAAkE;YAClE,WAAW;mBAAI;gBAAS;aAAM;YAC9B,wBAAwB;mBAAI;gBAAS;aAAM;YAE3C;YACA,gBAAgB;YAEhB,MAAM,CAAC,MAAM,EAAE,MAAM,YAAY,CAAC,yCAAyC,EAAE,kBAAkB,OAAO,CAAC,GAAG,aAAa,EAAE,oBAAoB,MAAM,CAAC,gBAAgB,CAAC;QACvK,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA,MAAM,YAAY;QAChB,eAAe;QACf,YAAY;YACV,WAAW,QAAQ,MAAM,GAAG,IAAI,OAAO,CAAC,EAAE,CAAC,EAAE,GAAG;YAChD,YAAY;YACZ,kBAAkB,QAAQ,MAAM,GAAG,IAAI,OAAO,CAAC,EAAE,CAAC,UAAU,GAAG;YAC/D,UAAU;YACV,oBAAoB;YACpB,kBAAkB;YAClB,aAAa;YACb,OAAO;QACT;QACA,uBAAuB,EAAE;QACzB,oBAAoB,EAAE;IACxB;IAEA,MAAM,WAAW;QACf,IAAI,cAAc,GAAG,eAAe,cAAc;IACpD;IAEA,MAAM,WAAW;QACf,IAAI,cAAc,GAAG,eAAe,cAAc;IACpD;IAEA,0BAA0B;IAC1B,MAAM,sBAAsB;QAC1B,MAAM,aAAa;YACjB,UAAU;YACV,OAAO,IAAI,CAAC,eAAe,CAAC,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC;YAC3C,YAAY;eACT,gBAAgB,GAAG,CAAC,CAAA,MAAO,OAAO,MAAM,CAAC,KAAK,GAAG,CAAC,CAAA,MACnD,OAAO,QAAQ,YAAY,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,KAC5D,IAAI,CAAC;SACR,CAAC,IAAI,CAAC;QAEP,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAW;QACvD,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG,CAAC,mBAAmB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC;QACjF,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC,iJAAA,CAAA,aAAU;kBACT,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,uBAAuB,CAAC;oCACvC,WAAW,CAAC,mGAAmG,EAC7G,sBACI,+CACA,2CACJ;;sDAEF,6LAAC,8NAAA,CAAA,iBAAc;4CAAC,WAAU;4CAAqB,eAAY;;;;;;wCAC1D,sBAAsB,cAAc;;;;;;;8CAEvC,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;;sDAEV,6LAAC,4OAAA,CAAA,wBAAqB;4CAAC,WAAU;4CAAqB,eAAY;;;;;;wCAAS;;;;;;;8CAG7E,6LAAC;oCACC,MAAK;oCACL,SAAS;wCAAQ;wCAAa,gBAAgB;oCAAO;oCACrD,WAAU;;sDAEV,6LAAC,kNAAA,CAAA,WAAQ;4CAAC,WAAU;4CAAqB,eAAY;;;;;;wCAAS;;;;;;;;;;;;;;;;;;;8BAOpE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gNAAA,CAAA,UAAO;gDAAC,WAAU;gDAAwB,eAAY;;;;;;;;;;;sDAEzD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEACX,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQnE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAwB,eAAY;;;;;;;;;;;sDAE3D,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEACX,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ/D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gOAAA,CAAA,kBAAe;gDAAC,WAAU;gDAAyB,eAAY;;;;;;;;;;;sDAElE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEACX,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQjE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,sNAAA,CAAA,aAAU;gDAAC,WAAU;gDAA0B,eAAY;;;;;;;;;;;sDAE9D,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEACX,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQhE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,sOAAA,CAAA,qBAAkB;gDAAC,WAAU;gDAAyB,eAAY;;;;;;;;;;;sDAErE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;;4DAAoC;4DAC9C,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,cAAc,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQnF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,0NAAA,CAAA,eAAY;gDAAC,WAAU;gDAA0B,eAAY;;;;;;;;;;;sDAEhE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;;4DACX,QAAQ,MAAM,GAAG,IACd,CAAC,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,gBAAgB,IAAI,GAAG,GAAG,KAAK,QAAQ,MAAM,EAAE,OAAO,CAAC,KAC5F;4DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAUrB,sBACC,iCAAiC,iBACjC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;4CAA8C;4CACjC,gBAAgB,MAAM;4CAAC;;;;;;;kDAElD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,OAAO,GAAG,UAAU,CAAC,EAAE,eAAe;4CACtC,UAAU,CAAC;gDACT,MAAM,CAAC,OAAO,UAAU,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;gDAChD,aAAa;gDACb,iBAAiB;4CACnB;;8DAEA,6LAAC;oDAAO,OAAM;8DAAoB;;;;;;8DAClC,6LAAC;oDAAO,OAAM;8DAAmB;;;;;;8DACjC,6LAAC;oDAAO,OAAM;8DAAa;;;;;;8DAC3B,6LAAC;oDAAO,OAAM;8DAAgB;;;;;;8DAC9B,6LAAC;oDAAO,OAAM;8DAAsB;;;;;;8DACpC,6LAAC;oDAAO,OAAM;8DAAqB;;;;;;;;;;;;;;;;;;;;;;;0CAKzC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;kEACvF,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;kEACvF,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;kEACvF,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;kEACvF,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;kEACvF,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;kEACvF,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;kEACvF,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;kEACvF,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;kEACvF,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;kEACvF,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;kEACvF,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;kEACvF,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;kEACvF,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;kEACvF,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;kEACvF,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;;;;;;;;;;;;sDAG3F,6LAAC;4CAAM,WAAU;sDACd,gBACE,IAAI,CAAC,CAAC,GAAG;gDACR,MAAM,OAAO,CAAC,CAAC,UAAU;gDACzB,MAAM,OAAO,CAAC,CAAC,UAAU;gDACzB,IAAI,kBAAkB,OAAO;oDAC3B,OAAO,OAAO,OAAO,CAAC,IAAI,OAAO,OAAO,IAAI;gDAC9C,OAAO;oDACL,OAAO,OAAO,OAAO,CAAC,IAAI,OAAO,OAAO,IAAI;gDAC9C;4CACF,GACC,GAAG,CAAC,CAAC,KAAK,sBACT,6LAAC;oDAAe,WAAU;;sEACxB,6LAAC;4DAAG,WAAU;sEAAyD,IAAI,YAAY;;;;;;sEACvF,6LAAC;4DAAG,WAAU;sEAA6C,IAAI,WAAW;;;;;;sEAC1E,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,CAAC,2CAA2C,EAC3D,IAAI,UAAU,KAAK,SAAS,kCAAkC,6BAC9D;0EACC,IAAI,UAAU;;;;;;;;;;;sEAGnB,6LAAC;4DAAG,WAAU;sEAA6C,IAAI,gBAAgB;;;;;;sEAC/E,6LAAC;4DAAG,WAAU;sEAA6C,IAAI,eAAe,IAAI;;;;;;sEAClF,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,IAAI,MAAM,GAAG;0EACxF,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;;;;;;sEAG7C,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,CAAC,2CAA2C,EAAE,iBAAiB,IAAI,QAAQ,GAAG;0EAC5F,IAAI,QAAQ,CAAC,WAAW;;;;;;;;;;;sEAG7B,6LAAC;4DAAG,WAAU;sEAA6C,IAAI,WAAW;;;;;;sEAC1E,6LAAC;4DAAG,WAAU;sEACX,IAAI,aAAa,GAAG,WAAW,IAAI,aAAa,IAAI;;;;;;sEAEvD,6LAAC;4DAAG,WAAU;sEACX,IAAI,WAAW,GAAG,WAAW,IAAI,WAAW,IAAI;;;;;;sEAEnD,6LAAC;4DAAG,WAAU;;gEAA4C;gEAAE,IAAI,cAAc,CAAC,OAAO,CAAC;;;;;;;sEACvF,6LAAC;4DAAG,WAAU;sEACX,IAAI,WAAW,GAAG,IAAI,CAAC,CAAC,EAAE,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,GAAG;;;;;;sEAE5D,6LAAC;4DAAG,WAAU;;gEAA6C,IAAI,gBAAgB,CAAC,OAAO,CAAC;gEAAG;;;;;;;sEAC3F,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,CAAC,2CAA2C,EAC3D,IAAI,cAAc,KAAK,WAAW,gCAClC,IAAI,cAAc,KAAK,WAAW,4BAClC,IAAI,cAAc,KAAK,oBAAoB,kCAC3C,6BACA;0EACC,IAAI,cAAc;;;;;;;;;;;sEAGvB,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EACC,WAAU;4EACV,OAAO;gFAAE,OAAO,GAAG,IAAI,QAAQ,CAAC,CAAC,CAAC;4EAAC;;;;;;;;;;;kFAGvC,6LAAC;wEAAK,WAAU;;4EAAW,IAAI,QAAQ;4EAAC;;;;;;;;;;;;;;;;;;sEAG5C,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,CAAC,2CAA2C,EAC3D,IAAI,mBAAmB,GAAG,gCAAgC,2BAC1D;0EACC,IAAI,mBAAmB,GAAG,cAAc;;;;;;;;;;;;mDA3DtC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAsEvB,yBAAyB,iBACzB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAmD;oCAC1C,QAAQ,MAAM;oCAAC;;;;;;;4BAGvC,wBACC,6LAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wCAAY,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;uCALP;;;;;;;;;qDAUd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;sDAIhC,6LAAC;4CAAM,WAAU;sDACd,QAAQ,GAAG,CAAC,CAAC,sBACZ,6LAAC;oDAAkB,WAAU;;sEAC3B,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;kFAAqC,MAAM,YAAY;;;;;;kFACtE,6LAAC;wEAAI,WAAU;kFACZ,MAAM,UAAU,KAAK,SAAS,eAAe;;;;;;;;;;;;;;;;;sEAIpD,6LAAC;4DAAG,WAAU;sEACX,MAAM,WAAW;;;;;;sEAEpB,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAI,WAAU;;wEACZ,MAAM,eAAe,IAAI,MAAM,gBAAgB;wEAAC;wEAAE,MAAM,eAAe;;;;;;;gEAEzE,MAAM,eAAe,kBACpB,6LAAC;oEAAI,WAAU;;wEAAwB;wEAC3B,MAAM,gBAAgB;;;;;;;;;;;;;sEAItC,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,CAAC,wEAAwE,EAAE,eAAe,MAAM,MAAM,GAAG;0EACvH,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;;;;;;sEAG/C,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,CAAC,wEAAwE,EAAE,iBAAiB,MAAM,QAAQ,GAAG;0EAC3H,MAAM,QAAQ,CAAC,WAAW;;;;;;;;;;;sEAG/B,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EACC,WAAU;4EACV,OAAO;gFAAE,OAAO,GAAG,MAAM,QAAQ,CAAC,CAAC,CAAC;4EAAC;;;;;;;;;;;kFAGzC,6LAAC;wEAAK,WAAU;;4EAAyB,MAAM,QAAQ;4EAAC;;;;;;;;;;;;;;;;;;sEAG5D,6LAAC;4DAAG,WAAU;sEACX,MAAM,WAAW;;;;;;sEAEpB,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAO,WAAU;8EAA2C;;;;;;8EAG7D,6LAAC;oEAAO,WAAU;8EAAsC;;;;;;;;;;;;;mDAlDnD,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAiE/B,6LAAC,oIAAA,CAAA,QAAK;oBACJ,QAAQ;oBACR,SAAS;wBAAQ;wBAAa,gBAAgB;oBAAQ;oBACtD,OAAO,CAAC,+BAA+B,EAAE,YAAY,KAAK,CAAC;oBAC3D,UAAS;8BAET,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAG;oCAAG;iCAAE,CAAC,GAAG,CAAC,CAAC,qBACd,6LAAC;wCAAe,WAAU;;0DACxB,6LAAC;gDAAI,WAAW,CAAC,+DAA+D,EAC9E,QAAQ,cACJ,6CACA,iCACJ;0DACC,OAAO,4BACN,6LAAC,gOAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;yEAE3B,6LAAC;oDAAK,WAAU;8DAAuB;;;;;;;;;;;0DAG3C,6LAAC;gDAAI,WAAU;;oDACZ,SAAS,KAAK;oDACd,SAAS,KAAK;oDACd,SAAS,KAAK;;;;;;;4CAEhB,OAAO,mBACN,6LAAC;gDAAI,WAAW,CAAC,gBAAgB,EAC/B,OAAO,cAAc,iBAAiB,eACtC;;;;;;;uCApBI;;;;;;;;;;4BA2Bb,iBAAiB,MAAM,GAAG,mBACzB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gPAAA,CAAA,0BAAuB;4CAAC,WAAU;;;;;;sDACnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,6LAAC;oDAAG,WAAU;8DACX,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,6LAAC;sEAAgB;2DAAR;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASrB,6LAAC;gCAAI,WAAU;;oCACZ,gBAAgB,mBACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,QAAQ;gEACR,WAAU;gEACV,OAAO,SAAS,SAAS;gEACzB,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;oEAAC;;kFAEtE,6LAAC;wEAAO,OAAM;kFAAG;;;;;;oEAChB,QAAQ,GAAG,CAAC,CAAA,uBACX,6LAAC;4EAAuB,OAAO,OAAO,EAAE;;gFACrC,OAAO,IAAI;gFAAC;gFAAa,OAAO,UAAU;gFAAC;gFAAE,OAAO,eAAe;gFAAC;;2EAD1D,OAAO,EAAE;;;;;;;;;;;;;;;;;kEAM5B,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,WAAU;gEACV,OAAO,SAAS,UAAU;gEAC1B,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,YAAY,EAAE,MAAM,CAAC,KAAK;oEAA0B;;kFAEhG,6LAAC;wEAAO,OAAM;kFAAa;;;;;;kFAC3B,6LAAC;wEAAO,OAAM;kFAAO;;;;;;;;;;;;;;;;;;kEAGzB,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,MAAK;gEACL,QAAQ;gEACR,WAAU;gEACV,OAAO,SAAS,gBAAgB;gEAChC,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oEAAE;;;;;;;;;;;;kEAGlG,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,WAAU;gEACV,OAAO,SAAS,QAAQ;gEACxB,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oEAAQ;;kFAE5E,6LAAC;wEAAO,OAAM;kFAAM;;;;;;kFACpB,6LAAC;wEAAO,OAAM;kFAAS;;;;;;kFACvB,6LAAC;wEAAO,OAAM;kFAAO;;;;;;kFACrB,6LAAC;wEAAO,OAAM;kFAAS;;;;;;;;;;;;;;;;;;kEAG3B,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,MAAK;gEACL,QAAQ;gEACR,WAAU;gEACV,OAAO,SAAS,kBAAkB;gEAClC,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,oBAAoB,EAAE,MAAM,CAAC,KAAK;oEAAC;;;;;;;;;;;;kEAGnF,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,MAAK;gEACL,QAAQ;gEACR,WAAU;gEACV,OAAO,SAAS,gBAAgB;gEAChC,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,kBAAkB,EAAE,MAAM,CAAC,KAAK;oEAAC;;;;;;;;;;;;kEAGjF,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,QAAQ;gEACR,WAAU;gEACV,OAAO,SAAS,WAAW;gEAC3B,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oEAAC;;kFAExE,6LAAC;wEAAO,OAAM;kFAAG;;;;;;kFACjB,6LAAC;wEAAO,OAAM;kFAAa;;;;;;kFAC3B,6LAAC;wEAAO,OAAM;kFAAgB;;;;;;kFAC9B,6LAAC;wEAAO,OAAM;kFAAc;;;;;;kFAC5B,6LAAC;wEAAO,OAAM;kFAAc;;;;;;;;;;;;;;;;;;kEAGhC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACC,MAAM;gEACN,WAAU;gEACV,OAAO,SAAS,KAAK;gEACrB,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAC;gEAClE,aAAY;;;;;;;;;;;;;;;;;;;;;;;;oCAOrB,gBAAgB,mBACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;4CAEjD,oBAAoB,MAAM,KAAK,kBAC9B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,sNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,6LAAC;wDAAE,WAAU;kEAAO;;;;;;;;;;;qEAGtB,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAiC;;;;;;0EAC/C,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;0FACC,6LAAC;gFAAK,WAAU;0FAAc;;;;;;4EAAuB;4EAAE,oBAAoB,MAAM;;;;;;;kFAEnF,6LAAC;;0FACC,6LAAC;gFAAK,WAAU;0FAAc;;;;;;4EAAkB;4EAAG,kBAAkB,OAAO,CAAC;;;;;;;kFAE/E,6LAAC;kFACC,cAAA,6LAAC;4EAAK,WAAW,CAAC,YAAY,EAAE,wBAAwB,mBAAmB,gBAAgB;;gFAAE;gFAClF,wBAAwB,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;kEAO3D,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAM,WAAU;;8EACf,6LAAC;oEAAM,WAAU;8EACf,cAAA,6LAAC;;0FACC,6LAAC;gFAAG,WAAU;0FAAiF;;;;;;0FAC/F,6LAAC;gFAAG,WAAU;0FAAiF;;;;;;0FAC/F,6LAAC;gFAAG,WAAU;0FAAiF;;;;;;0FAC/F,6LAAC;gFAAG,WAAU;0FAAiF;;;;;;0FAC/F,6LAAC;gFAAG,WAAU;0FAAiF;;;;;;0FAC/F,6LAAC;gFAAG,WAAU;0FAAiF;;;;;;;;;;;;;;;;;8EAGnG,6LAAC;oEAAM,WAAU;8EACd,oBAAoB,GAAG,CAAC,CAAC,2BACxB,6LAAC;;8FACC,6LAAC;oFAAG,WAAU;8FACX,WAAW,mBAAmB;;;;;;8FAEjC,6LAAC;oFAAG,WAAU;;wFACX,WAAW,iBAAiB;wFAAC;wFAAE,WAAW,eAAe;;;;;;;8FAE5D,6LAAC;oFAAG,WAAU;;wFACX,WAAW,kBAAkB;wFAAC;wFAAE,WAAW,eAAe;;;;;;;8FAE7D,6LAAC;oFAAG,WAAU;;wFAAoD;wFAC9D,WAAW,SAAS,CAAC,OAAO,CAAC;;;;;;;8FAEjC,6LAAC;oFAAG,WAAU;;wFAAoD;wFAC9D,WAAW,UAAU,CAAC,OAAO,CAAC;;;;;;;8FAElC,6LAAC;oFAAG,WAAU;8FACZ,cAAA,6LAAC;wFAAK,WAAW,CAAC,yDAAyD,EACzE,WAAW,mBAAmB,KAAK,cAC/B,gCACA,WAAW,mBAAmB,KAAK,iBACnC,kCACA,2BACJ;kGACC,WAAW,mBAAmB;;;;;;;;;;;;2EAxB5B,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAqCrC,gBAAgB,mBACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAGlD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFAAI,6LAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAc;oEAAE,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,SAAS,GAAG;;;;;;;0EACnG,6LAAC;;kFAAI,6LAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAkB;oEAAE,SAAS,UAAU;;;;;;;0EAC1E,6LAAC;;kFAAI,6LAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAgB;oEAAE,SAAS,gBAAgB;oEAAC;oEAAE,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,SAAS,GAAG;;;;;;;0EACjI,6LAAC;;kFAAI,6LAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAgB;oEAAE,SAAS,QAAQ;;;;;;;0EACtE,6LAAC;;kFAAI,6LAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAkB;oEAAE,SAAS,kBAAkB,GAAG,IAAI,KAAK,SAAS,kBAAkB,EAAE,cAAc,KAAK;;;;;;;0EAC9I,6LAAC;;kFAAI,6LAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAgB;oEAAE,SAAS,gBAAgB,GAAG,IAAI,KAAK,SAAS,gBAAgB,EAAE,cAAc,KAAK;;;;;;;0EACxI,6LAAC;;kFAAI,6LAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAmB;oEAAE,SAAS,WAAW;;;;;;;0EAC5E,6LAAC;;kFAAI,6LAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAsB;oEAAG,kBAAkB,OAAO,CAAC;;;;;;;;;;;;;;;;;;;0DAK1F,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;;4DAAiC;4DAAsB,oBAAoB,MAAM;4DAAC;;;;;;;kEAChG,6LAAC;wDAAI,WAAU;kEACZ,oBAAoB,GAAG,CAAC,CAAC,2BACxB,6LAAC;gEAAwB,WAAU;;kFACjC,6LAAC;kFAAM,WAAW,mBAAmB;;;;;;kFACrC,6LAAC;;4EAAM,WAAW,iBAAiB;4EAAC;4EAAE,WAAW,eAAe;4EAAC;4EAAI,WAAW,UAAU,CAAC,OAAO,CAAC;4EAAG;;;;;;;;+DAF9F,WAAW,EAAE;;;;;;;;;;;;;;;;0DAS7B,6LAAC;gDAAI,WAAW,CAAC,eAAe,EAAE,wBAAwB,gBAAgB,aAAa;0DACrF,cAAA,6LAAC;oDAAI,WAAU;;wDACZ,sCACC,6LAAC,gOAAA,CAAA,kBAAe;4DAAC,WAAU;;;;;iFAE3B,6LAAC,gPAAA,CAAA,0BAAuB;4DAAC,WAAU;;;;;;sEAErC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAW,CAAC,oBAAoB,EAAE,wBAAwB,mBAAmB,gBAAgB;8EAC9F,wBAAwB,0BAA0B;;;;;;8EAErD,6LAAC;oEAAE,WAAW,CAAC,aAAa,EAAE,wBAAwB,mBAAmB,gBAAgB;8EACtF,wBACG,oGACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS,gBAAgB,IAAI;4CAAQ;4CAAa,gBAAgB;wCAAQ,IAAI;wCAC9E,WAAU;kDAET,gBAAgB,IAAI,WAAW;;;;;;kDAGlC,6LAAC;wCAAI,WAAU;kDACZ,cAAc,kBACb,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,gBAAgB,KAAK,CAAC,CAAC,SAAS,SAAS,IAAI,SAAS,gBAAgB,IAAI,CAAC;4CACrF,WAAU;sDACX;;;;;iEAID,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,CAAC;4CACX,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GApkCwB;;QACL,kIAAA,CAAA,UAAO;;;KADF", "debugId": null}}]}