{"version": 3, "sources": [], "sections": [{"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createClientComponentClient } from '@supabase/auth-helpers-nextjs'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Client component client\nexport const createSupabaseClient = () => createClientComponentClient()\n\n// Admin client (server-side only)\nexport const createSupabaseAdminClient = () => {\n  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n  return createClient(supabaseUrl, serviceRoleKey, {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  })\n}\n\n// Database types\nexport interface Database {\n  public: {\n    Tables: {\n      profiles: {\n        Row: {\n          id: string\n          email: string\n          full_name: string\n          role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department: string | null\n          phone: string | null\n          is_active: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          email: string\n          full_name: string\n          role?: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department?: string | null\n          phone?: string | null\n          is_active?: boolean\n        }\n        Update: {\n          email?: string\n          full_name?: string\n          role?: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department?: string | null\n          phone?: string | null\n          is_active?: boolean\n        }\n      }\n      inventory_categories: {\n        Row: {\n          id: string\n          name: string\n          description: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          description?: string | null\n        }\n        Update: {\n          name?: string\n          description?: string | null\n        }\n      }\n      inventory_items: {\n        Row: {\n          id: string\n          name: string\n          sku: string\n          category_id: string | null\n          description: string | null\n          unit_of_measure: string\n          current_stock: number\n          minimum_stock: number\n          maximum_stock: number | null\n          unit_cost: number\n          supplier_info: any | null\n          storage_conditions: string | null\n          expiry_date: string | null\n          batch_number: string | null\n          is_active: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          sku: string\n          category_id?: string | null\n          description?: string | null\n          unit_of_measure: string\n          current_stock?: number\n          minimum_stock?: number\n          maximum_stock?: number | null\n          unit_cost?: number\n          supplier_info?: any | null\n          storage_conditions?: string | null\n          expiry_date?: string | null\n          batch_number?: string | null\n          is_active?: boolean\n        }\n        Update: {\n          name?: string\n          sku?: string\n          category_id?: string | null\n          description?: string | null\n          unit_of_measure?: string\n          current_stock?: number\n          minimum_stock?: number\n          maximum_stock?: number | null\n          unit_cost?: number\n          supplier_info?: any | null\n          storage_conditions?: string | null\n          expiry_date?: string | null\n          batch_number?: string | null\n          is_active?: boolean\n        }\n      }\n      recipes: {\n        Row: {\n          id: string\n          name: string\n          code: string\n          description: string | null\n          category: string | null\n          version: number\n          batch_size: number\n          unit_of_measure: string\n          estimated_cost: number | null\n          preparation_time: number | null\n          instructions: string | null\n          notes: string | null\n          is_active: boolean\n          created_by: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          code: string\n          description?: string | null\n          category?: string | null\n          version?: number\n          batch_size: number\n          unit_of_measure: string\n          estimated_cost?: number | null\n          preparation_time?: number | null\n          instructions?: string | null\n          notes?: string | null\n          is_active?: boolean\n          created_by?: string | null\n        }\n        Update: {\n          name?: string\n          code?: string\n          description?: string | null\n          category?: string | null\n          version?: number\n          batch_size?: number\n          unit_of_measure?: string\n          estimated_cost?: number | null\n          preparation_time?: number | null\n          instructions?: string | null\n          notes?: string | null\n          is_active?: boolean\n        }\n      }\n      production_batches: {\n        Row: {\n          id: string\n          batch_number: string\n          recipe_id: string\n          batch_type: 'test' | 'production'\n          planned_quantity: number\n          actual_quantity: number | null\n          unit_of_measure: string\n          status: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date: string | null\n          actual_start_date: string | null\n          planned_end_date: string | null\n          actual_end_date: string | null\n          production_cost: number | null\n          yield_percentage: number | null\n          quality_approved: boolean | null\n          notes: string | null\n          created_by: string | null\n          assigned_to: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          batch_number: string\n          recipe_id: string\n          batch_type: 'test' | 'production'\n          planned_quantity: number\n          actual_quantity?: number | null\n          unit_of_measure: string\n          status?: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority?: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date?: string | null\n          actual_start_date?: string | null\n          planned_end_date?: string | null\n          actual_end_date?: string | null\n          production_cost?: number | null\n          yield_percentage?: number | null\n          quality_approved?: boolean | null\n          notes?: string | null\n          created_by?: string | null\n          assigned_to?: string | null\n        }\n        Update: {\n          batch_number?: string\n          recipe_id?: string\n          batch_type?: 'test' | 'production'\n          planned_quantity?: number\n          actual_quantity?: number | null\n          unit_of_measure?: string\n          status?: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority?: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date?: string | null\n          actual_start_date?: string | null\n          planned_end_date?: string | null\n          actual_end_date?: string | null\n          production_cost?: number | null\n          yield_percentage?: number | null\n          quality_approved?: boolean | null\n          notes?: string | null\n          assigned_to?: string | null\n        }\n      }\n    }\n    Views: {\n      [_ in never]: never\n    }\n    Functions: {\n      check_recipe_availability: {\n        Args: {\n          recipe_uuid: string\n          batch_size_multiplier?: number\n        }\n        Returns: {\n          item_id: string\n          item_name: string\n          required_quantity: number\n          available_quantity: number\n          is_sufficient: boolean\n        }[]\n      }\n      calculate_recipe_cost: {\n        Args: {\n          recipe_uuid: string\n          batch_size_multiplier?: number\n        }\n        Returns: number\n      }\n    }\n    Enums: {\n      user_role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,uBAAuB,IAAM,CAAA,GAAA,wKAAA,CAAA,8BAA2B,AAAD;AAG7D,MAAM,4BAA4B;IACvC,MAAM,iBAAiB,QAAQ,GAAG,CAAC,yBAAyB;IAC5D,OAAO,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,gBAAgB;QAC/C,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/app/login/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { createSupabaseClient } from '@/lib/supabase'\nimport { EyeIcon, EyeSlashIcon, UserIcon, LockClosedIcon } from '@heroicons/react/24/outline'\n\nexport default function LoginPage() {\n  const [username, setUsername] = useState('')\n  const [password, setPassword] = useState('')\n  const [showPassword, setShowPassword] = useState(false)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const router = useRouter()\n  const supabase = createSupabaseClient()\n\n  const handleLogin = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    try {\n      // For demo purposes, we'll use predefined credentials\n      // In production, you'd validate against your user database\n      const validCredentials = [\n        { username: 'admin', password: 'admin123', role: 'admin' },\n        { username: 'quality', password: 'quality123', role: 'quality_manager' },\n        { username: 'production', password: 'production123', role: 'production_manager' },\n        { username: 'employee', password: 'employee123', role: 'employee' }\n      ]\n\n      const user = validCredentials.find(\n        cred => cred.username === username && cred.password === password\n      )\n\n      if (user) {\n        // Create a mock session for demo purposes\n        if (typeof window !== 'undefined') {\n          localStorage.setItem('sff_user', JSON.stringify({\n            id: `user_${user.username}`,\n            username: user.username,\n            role: user.role,\n            email: `${user.username}@sff.com`\n          }))\n        }\n        router.push('/')\n      } else {\n        setError('Invalid username or password')\n      }\n    } catch (err) {\n      setError('An unexpected error occurred')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-white py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full\">\n        {/* Logo and Header */}\n        <div className=\"text-center mb-8\">\n          <div className=\"mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-gradient-to-r from-green-500 to-green-600 shadow-lg\">\n            <svg className=\"h-10 w-10 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\" />\n            </svg>\n          </div>\n          <h2 className=\"mt-6 text-3xl font-bold text-gray-900\">\n            Welcome Back\n          </h2>\n          <p className=\"mt-2 text-sm text-gray-600\">\n            SFF Production & Quality Management System\n          </p>\n        </div>\n\n        {/* Login Form */}\n        <div className=\"bg-white rounded-2xl shadow-xl p-8 border border-green-100\">\n          <form className=\"space-y-6\" onSubmit={handleLogin}>\n            {/* Username Field */}\n            <div>\n              <label htmlFor=\"username\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Username\n              </label>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <UserIcon className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  id=\"username\"\n                  name=\"username\"\n                  type=\"text\"\n                  autoComplete=\"username\"\n                  required\n                  className=\"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 text-gray-900 placeholder-gray-500\"\n                  placeholder=\"Enter your username\"\n                  value={username}\n                  onChange={(e) => setUsername(e.target.value)}\n                />\n              </div>\n            </div>\n\n            {/* Password Field */}\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Password\n              </label>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <LockClosedIcon className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  autoComplete=\"current-password\"\n                  required\n                  className=\"block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 text-gray-900 placeholder-gray-500\"\n                  placeholder=\"Enter your password\"\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                />\n                <button\n                  type=\"button\"\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 transition-colors duration-200\"\n                  onClick={() => setShowPassword(!showPassword)}\n                >\n                  {showPassword ? (\n                    <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                  ) : (\n                    <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                  )}\n                </button>\n              </div>\n            </div>\n\n            {/* Error Message */}\n            {error && (\n              <div className=\"rounded-lg bg-red-50 border border-red-200 p-4\">\n                <div className=\"flex\">\n                  <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <div className=\"ml-3\">\n                    <p className=\"text-sm text-red-700\">{error}</p>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Submit Button */}\n            <div>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\n              >\n                {loading ? (\n                  <>\n                    <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                    </svg>\n                    Signing in...\n                  </>\n                ) : (\n                  'Sign In'\n                )}\n              </button>\n            </div>\n\n            {/* Demo Credentials */}\n            <div className=\"mt-6 p-4 bg-green-50 rounded-lg border border-green-200\">\n              <h4 className=\"text-sm font-medium text-green-800 mb-2\">Demo Credentials:</h4>\n              <div className=\"text-xs text-green-700 space-y-1\">\n                <div><strong>Admin:</strong> admin / admin123</div>\n                <div><strong>Quality Manager:</strong> quality / quality123</div>\n                <div><strong>Production Manager:</strong> production / production123</div>\n                <div><strong>Employee:</strong> employee / employee123</div>\n              </div>\n            </div>\n          </form>\n        </div>\n\n        {/* Footer */}\n        <div className=\"text-center mt-6\">\n          <p className=\"text-sm text-gray-500\">\n            Need help? Contact your system administrator\n          </p>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;IAEpC,MAAM,cAAc,OAAO;QACzB,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,sDAAsD;YACtD,2DAA2D;YAC3D,MAAM,mBAAmB;gBACvB;oBAAE,UAAU;oBAAS,UAAU;oBAAY,MAAM;gBAAQ;gBACzD;oBAAE,UAAU;oBAAW,UAAU;oBAAc,MAAM;gBAAkB;gBACvE;oBAAE,UAAU;oBAAc,UAAU;oBAAiB,MAAM;gBAAqB;gBAChF;oBAAE,UAAU;oBAAY,UAAU;oBAAe,MAAM;gBAAW;aACnE;YAED,MAAM,OAAO,iBAAiB,IAAI,CAChC,CAAA,OAAQ,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,KAAK;YAG1D,IAAI,MAAM;gBACR,0CAA0C;gBAC1C,uCAAmC;;gBAOnC;gBACA,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;gCAAuB,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CAC3E,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAM5C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;wBAAY,UAAU;;0CAEpC,8OAAC;;kDACC,8OAAC;wCAAM,SAAQ;wCAAW,WAAU;kDAA+C;;;;;;kDAGnF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,+MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,cAAa;gDACb,QAAQ;gDACR,WAAU;gDACV,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;0CAMjD,8OAAC;;kDACC,8OAAC;wCAAM,SAAQ;wCAAW,WAAU;kDAA+C;;;;;;kDAGnF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,2NAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;;;;;;0DAE5B,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAM,eAAe,SAAS;gDAC9B,cAAa;gDACb,QAAQ;gDACR,WAAU;gDACV,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;0DAE7C,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,gBAAgB,CAAC;0DAE/B,6BACC,8OAAC,uNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;yEAExB,8OAAC,6MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;4BAO1B,uBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAAuB,SAAQ;4CAAY,MAAK;sDAC7D,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAA0N,UAAS;;;;;;;;;;;sDAEhQ,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;0CAO7C,8OAAC;0CACC,cAAA,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,wBACC;;0DACE,8OAAC;gDAAI,WAAU;gDAA6C,OAAM;gDAA6B,MAAK;gDAAO,SAAQ;;kEACjH,8OAAC;wDAAO,WAAU;wDAAa,IAAG;wDAAK,IAAG;wDAAK,GAAE;wDAAK,QAAO;wDAAe,aAAY;;;;;;kEACxF,8OAAC;wDAAK,WAAU;wDAAa,MAAK;wDAAe,GAAE;;;;;;;;;;;;4CAC/C;;uDAIR;;;;;;;;;;;0CAMN,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEAAI,8OAAC;kEAAO;;;;;;oDAAe;;;;;;;0DAC5B,8OAAC;;kEAAI,8OAAC;kEAAO;;;;;;oDAAyB;;;;;;;0DACtC,8OAAC;;kEAAI,8OAAC;kEAAO;;;;;;oDAA4B;;;;;;;0DACzC,8OAAC;;kEAAI,8OAAC;kEAAO;;;;;;oDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOvC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}]}