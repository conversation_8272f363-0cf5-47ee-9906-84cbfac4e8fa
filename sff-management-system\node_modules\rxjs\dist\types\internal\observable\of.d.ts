import { SchedulerLike, ValueFromArray } from '../types';
import { Observable } from '../Observable';
export declare function of(value: null): Observable<null>;
export declare function of(value: undefined): Observable<undefined>;
/** @deprecated The `scheduler` parameter will be removed in v8. Use `scheduled`. Details: https://rxjs.dev/deprecations/scheduler-argument */
export declare function of(scheduler: SchedulerLike): Observable<never>;
/** @deprecated The `scheduler` parameter will be removed in v8. Use `scheduled`. Details: https://rxjs.dev/deprecations/scheduler-argument */
export declare function of<A extends readonly unknown[]>(...valuesAndScheduler: [...A, SchedulerLike]): Observable<ValueFromArray<A>>;
export declare function of(): Observable<never>;
/** @deprecated Do not specify explicit type parameters. Signatures with type parameters that cannot be inferred will be removed in v8. */
export declare function of<T>(): Observable<T>;
export declare function of<T>(value: T): Observable<T>;
export declare function of<A extends readonly unknown[]>(...values: A): Observable<ValueFromArray<A>>;
//# sourceMappingURL=of.d.ts.map