{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\nimport {\n  HomeIcon,\n  CubeIcon,\n  BeakerIcon,\n  CogIcon,\n  DocumentTextIcon,\n  UsersIcon,\n  ChartBarIcon,\n  ClipboardDocumentListIcon,\n} from '@heroicons/react/24/outline'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { clsx } from 'clsx'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/', icon: HomeIcon },\n  { name: 'Inventory', href: '/inventory', icon: CubeIcon },\n  { name: 'Recipes', href: '/recipes', icon: ClipboardDocumentListIcon },\n  { name: 'Production', href: '/production', icon: CogIcon },\n  { name: 'Quality', href: '/quality', icon: BeakerIcon },\n  { name: 'Reports', href: '/reports', icon: ChartBarIcon },\n  { name: 'Documents', href: '/documents', icon: DocumentTextIcon },\n  { name: 'Users', href: '/users', icon: UsersIcon },\n]\n\ninterface SidebarProps {\n  open: boolean\n  setOpen: (open: boolean) => void\n}\n\nexport function Sidebar({ open, setOpen }: SidebarProps) {\n  const pathname = usePathname()\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <Transition.Root show={open} as={Fragment}>\n        <Dialog as=\"div\" className=\"relative z-50 lg:hidden\" onClose={setOpen}>\n          <Transition.Child\n            as={Fragment}\n            enter=\"transition-opacity ease-linear duration-300\"\n            enterFrom=\"opacity-0\"\n            enterTo=\"opacity-100\"\n            leave=\"transition-opacity ease-linear duration-300\"\n            leaveFrom=\"opacity-100\"\n            leaveTo=\"opacity-0\"\n          >\n            <div className=\"fixed inset-0 bg-gray-900/80\" />\n          </Transition.Child>\n\n          <div className=\"fixed inset-0 flex\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"transition ease-in-out duration-300 transform\"\n              enterFrom=\"-translate-x-full\"\n              enterTo=\"translate-x-0\"\n              leave=\"transition ease-in-out duration-300 transform\"\n              leaveFrom=\"translate-x-0\"\n              leaveTo=\"-translate-x-full\"\n            >\n              <Dialog.Panel className=\"relative mr-16 flex w-full max-w-xs flex-1\">\n                <Transition.Child\n                  as={Fragment}\n                  enter=\"ease-in-out duration-300\"\n                  enterFrom=\"opacity-0\"\n                  enterTo=\"opacity-100\"\n                  leave=\"ease-in-out duration-300\"\n                  leaveFrom=\"opacity-100\"\n                  leaveTo=\"opacity-0\"\n                >\n                  <div className=\"absolute left-full top-0 flex w-16 justify-center pt-5\">\n                    <button type=\"button\" className=\"-m-2.5 p-2.5\" onClick={() => setOpen(false)}>\n                      <span className=\"sr-only\">Close sidebar</span>\n                      <XMarkIcon className=\"h-6 w-6 text-white\" aria-hidden=\"true\" />\n                    </button>\n                  </div>\n                </Transition.Child>\n                <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4\">\n                  <div className=\"flex h-16 shrink-0 items-center\">\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">SFF</span>\n                    </div>\n                    <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n                  </div>\n                  <nav className=\"flex flex-1 flex-col\">\n                    <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n                      <li>\n                        <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                          {navigation.map((item) => (\n                            <li key={item.name}>\n                              <Link\n                                href={item.href}\n                                className={clsx(\n                                  pathname === item.href\n                                    ? 'bg-green-50 text-green-700'\n                                    : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                                  'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                                )}\n                              >\n                                <item.icon\n                                  className={clsx(\n                                    pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                                    'h-6 w-6 shrink-0'\n                                  )}\n                                  aria-hidden=\"true\"\n                                />\n                                {item.name}\n                              </Link>\n                            </li>\n                          ))}\n                        </ul>\n                      </li>\n                    </ul>\n                  </nav>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </Dialog>\n      </Transition.Root>\n\n      {/* Static sidebar for desktop */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 bg-white px-6 pb-4\">\n          <div className=\"flex h-16 shrink-0 items-center\">\n            <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-sm\">SFF</span>\n            </div>\n            <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n          </div>\n          <nav className=\"flex flex-1 flex-col\">\n            <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n              <li>\n                <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                  {navigation.map((item) => (\n                    <li key={item.name}>\n                      <Link\n                        href={item.href}\n                        className={clsx(\n                          pathname === item.href\n                            ? 'bg-green-50 text-green-700'\n                            : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                          'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                        )}\n                      >\n                        <item.icon\n                          className={clsx(\n                            pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                            'h-6 w-6 shrink-0'\n                          )}\n                          aria-hidden=\"true\"\n                        />\n                        {item.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </li>\n            </ul>\n          </nav>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;;;AAjBA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,kNAAA,CAAA,WAAQ;IAAC;IAC/C;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,oPAAA,CAAA,4BAAyB;IAAC;IACrE;QAAE,MAAM;QAAc,MAAM;QAAe,MAAM,gNAAA,CAAA,UAAO;IAAC;IACzD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,sNAAA,CAAA,aAAU;IAAC;IACtD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,0NAAA,CAAA,eAAY;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kOAAA,CAAA,mBAAgB;IAAC;IAChE;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,oNAAA,CAAA,YAAS;IAAC;CAClD;AAOM,SAAS,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAgB;;IACrD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE;;0BAEE,6LAAC,0LAAA,CAAA,aAAU,CAAC,IAAI;gBAAC,MAAM;gBAAM,IAAI,6JAAA,CAAA,WAAQ;0BACvC,cAAA,6LAAC,kLAAA,CAAA,SAAM;oBAAC,IAAG;oBAAM,WAAU;oBAA0B,SAAS;;sCAC5D,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,6JAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;sCAGjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;gCACf,IAAI,6JAAA,CAAA,WAAQ;gCACZ,OAAM;gCACN,WAAU;gCACV,SAAQ;gCACR,OAAM;gCACN,WAAU;gCACV,SAAQ;0CAER,cAAA,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;oCAAC,WAAU;;sDACtB,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4CACf,IAAI,6JAAA,CAAA,WAAQ;4CACZ,OAAM;4CACN,WAAU;4CACV,SAAQ;4CACR,OAAM;4CACN,WAAU;4CACV,SAAQ;sDAER,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAO,MAAK;oDAAS,WAAU;oDAAe,SAAS,IAAM,QAAQ;;sEACpE,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;4DAAqB,eAAY;;;;;;;;;;;;;;;;;;;;;;sDAI5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,6LAAC;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;8DAE7D,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAG,MAAK;wDAAO,WAAU;kEACxB,cAAA,6LAAC;sEACC,cAAA,6LAAC;gEAAG,MAAK;gEAAO,WAAU;0EACvB,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;kFACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4EACH,MAAM,KAAK,IAAI;4EACf,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;8FAGF,6LAAC,KAAK,IAAI;oFACR,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;oFAEF,eAAY;;;;;;gFAEb,KAAK,IAAI;;;;;;;uEAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiCxC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAE7D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,MAAK;gCAAO,WAAU;0CACxB,cAAA,6LAAC;8CACC,cAAA,6LAAC;wCAAG,MAAK;wCAAO,WAAU;kDACvB,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;sEAGF,6LAAC,KAAK,IAAI;4DACR,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;4DAEF,eAAY;;;;;;wDAEb,KAAK,IAAI;;;;;;;+CAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BtC;GAtIgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Menu, Transition } from '@headlessui/react'\nimport { Bars3Icon, BellIcon } from '@heroicons/react/24/outline'\nimport { useAuth } from '@/components/providers'\nimport { clsx } from 'clsx'\n\ninterface HeaderProps {\n  setSidebarOpen: (open: boolean) => void\n}\n\nexport function Header({ setSidebarOpen }: HeaderProps) {\n  const { user, signOut } = useAuth()\n\n  const userNavigation = [\n    { name: 'Your profile', href: '#' },\n    { name: 'Settings', href: '#' },\n    { name: 'Sign out', href: '#', onClick: signOut },\n  ]\n\n  return (\n    <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n      <button\n        type=\"button\"\n        className=\"-m-2.5 p-2.5 text-gray-700 lg:hidden\"\n        onClick={() => setSidebarOpen(true)}\n      >\n        <span className=\"sr-only\">Open sidebar</span>\n        <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n      </button>\n\n      {/* Separator */}\n      <div className=\"h-6 w-px bg-gray-200 lg:hidden\" aria-hidden=\"true\" />\n\n      <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n        <div className=\"relative flex flex-1\">\n          {/* Search can be added here later */}\n        </div>\n        <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n          <button type=\"button\" className=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\">\n            <span className=\"sr-only\">View notifications</span>\n            <BellIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n\n          {/* Separator */}\n          <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200\" aria-hidden=\"true\" />\n\n          {/* Profile dropdown */}\n          <Menu as=\"div\" className=\"relative\">\n            <Menu.Button className=\"-m-1.5 flex items-center p-1.5\">\n              <span className=\"sr-only\">Open user menu</span>\n              <div className=\"h-8 w-8 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.username?.charAt(0).toUpperCase() || 'U'}\n                </span>\n              </div>\n              <span className=\"hidden lg:flex lg:items-center\">\n                <span className=\"ml-4 text-sm font-semibold leading-6 text-gray-900\" aria-hidden=\"true\">\n                  {user?.username || 'User'}\n                </span>\n                <span className=\"ml-2 text-xs text-gray-500 capitalize\">\n                  ({user?.role?.replace('_', ' ') || 'Role'})\n                </span>\n              </span>\n            </Menu.Button>\n            <Transition\n              as={Fragment}\n              enter=\"transition ease-out duration-100\"\n              enterFrom=\"transform opacity-0 scale-95\"\n              enterTo=\"transform opacity-100 scale-100\"\n              leave=\"transition ease-in duration-75\"\n              leaveFrom=\"transform opacity-100 scale-100\"\n              leaveTo=\"transform opacity-0 scale-95\"\n            >\n              <Menu.Items className=\"absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none\">\n                {userNavigation.map((item) => (\n                  <Menu.Item key={item.name}>\n                    {({ active }) => (\n                      <button\n                        onClick={item.onClick}\n                        className={clsx(\n                          active ? 'bg-gray-50' : '',\n                          'block w-full text-left px-3 py-1 text-sm leading-6 text-gray-900'\n                        )}\n                      >\n                        {item.name}\n                      </button>\n                    )}\n                  </Menu.Item>\n                ))}\n              </Menu.Items>\n            </Transition>\n          </Menu>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;;;AANA;;;;;;AAYO,SAAS,OAAO,EAAE,cAAc,EAAe;;IACpD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAAgB,MAAM;QAAI;QAClC;YAAE,MAAM;YAAY,MAAM;QAAI;QAC9B;YAAE,MAAM;YAAY,MAAM;YAAK,SAAS;QAAQ;KACjD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,eAAe;;kCAE9B,6LAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;wBAAU,eAAY;;;;;;;;;;;;0BAI7C,6LAAC;gBAAI,WAAU;gBAAiC,eAAY;;;;;;0BAE5D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,MAAK;gCAAS,WAAU;;kDAC9B,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;gCAAgD,eAAY;;;;;;0CAG3E,6LAAC,8KAAA,CAAA,OAAI;gCAAC,IAAG;gCAAM,WAAU;;kDACvB,6LAAC,8KAAA,CAAA,OAAI,CAAC,MAAM;wCAAC,WAAU;;0DACrB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb,MAAM,UAAU,OAAO,GAAG,iBAAiB;;;;;;;;;;;0DAGhD,6LAAC;gDAAK,WAAU;;kEACd,6LAAC;wDAAK,WAAU;wDAAqD,eAAY;kEAC9E,MAAM,YAAY;;;;;;kEAErB,6LAAC;wDAAK,WAAU;;4DAAwC;4DACpD,MAAM,MAAM,QAAQ,KAAK,QAAQ;4DAAO;;;;;;;;;;;;;;;;;;;kDAIhD,6LAAC,0LAAA,CAAA,aAAU;wCACT,IAAI,6JAAA,CAAA,WAAQ;wCACZ,OAAM;wCACN,WAAU;wCACV,SAAQ;wCACR,OAAM;wCACN,WAAU;wCACV,SAAQ;kDAER,cAAA,6LAAC,8KAAA,CAAA,OAAI,CAAC,KAAK;4CAAC,WAAU;sDACnB,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;8DACP,CAAC,EAAE,MAAM,EAAE,iBACV,6LAAC;4DACC,SAAS,KAAK,OAAO;4DACrB,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,SAAS,eAAe,IACxB;sEAGD,KAAK,IAAI;;;;;;mDATA,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqB3C;GAtFgB;;QACY,kIAAA,CAAA,UAAO;;;KADnB", "debugId": null}}, {"offset": {"line": 680, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/components/providers'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const { user } = useAuth()\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Sidebar open={sidebarOpen} setOpen={setSidebarOpen} />\n      \n      <div className=\"lg:pl-72\">\n        <Header setSidebarOpen={setSidebarOpen} />\n        \n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAWO,SAAS,WAAW,EAAE,QAAQ,EAAmB;;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAO;gBAAC,MAAM;gBAAa,SAAS;;;;;;0BAErC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,SAAM;wBAAC,gBAAgB;;;;;;kCAExB,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAnBgB;;QAEG,kIAAA,CAAA,UAAO;;;KAFV", "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/app/quality/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { useAuth } from '@/components/providers'\nimport { PlusIcon, BeakerIcon, DocumentTextIcon, ShieldCheckIcon, ClipboardDocumentCheckIcon } from '@heroicons/react/24/outline'\n\ninterface QualityDocument {\n  id: string\n  document_type: 'msds' | 'coa' | 'tds' | 'quality_spec' | 'lab_report'\n  title: string\n  document_number: string\n  version: string\n  item_name?: string\n  batch_number?: string\n  status: 'draft' | 'review' | 'approved' | 'expired'\n  valid_from?: string\n  valid_until?: string\n  created_at: string\n  created_by: string\n}\n\nexport default function QualityPage() {\n  const { user } = useAuth()\n  const [documents, setDocuments] = useState<QualityDocument[]>([])\n  const [loading, setLoading] = useState(true)\n  const [activeTab, setActiveTab] = useState<'all' | 'msds' | 'coa' | 'tds' | 'lab_reports'>('all')\n\n  useEffect(() => {\n    // Mock quality documents data\n    const mockDocuments: QualityDocument[] = [\n      {\n        id: '1',\n        document_type: 'msds',\n        title: 'Vanilla Extract Safety Data Sheet',\n        document_number: 'MSDS-VAN-001',\n        version: '2.1',\n        item_name: 'Vanilla Extract',\n        status: 'approved',\n        valid_from: '2024-01-01',\n        valid_until: '2025-12-31',\n        created_at: '2024-12-10T10:00:00Z',\n        created_by: 'Sarah Johnson'\n      },\n      {\n        id: '2',\n        document_type: 'coa',\n        title: 'Certificate of Analysis - Batch SFF-20241211-001',\n        document_number: 'COA-SFF-20241211-001',\n        version: '1.0',\n        batch_number: 'SFF-20241211-001',\n        status: 'approved',\n        valid_from: '2024-12-11',\n        created_at: '2024-12-11T16:00:00Z',\n        created_by: 'Mike Wilson'\n      },\n      {\n        id: '3',\n        document_type: 'tds',\n        title: 'Strawberry Flavor Technical Data Sheet',\n        document_number: 'TDS-STR-001',\n        version: '1.3',\n        item_name: 'Strawberry Flavor',\n        status: 'approved',\n        valid_from: '2024-11-01',\n        valid_until: '2025-10-31',\n        created_at: '2024-11-15T14:30:00Z',\n        created_by: 'Emily Davis'\n      },\n      {\n        id: '4',\n        document_type: 'lab_report',\n        title: 'Microbiological Testing Report',\n        document_number: 'LAB-20241210-003',\n        version: '1.0',\n        batch_number: 'SFF-20241211-001',\n        status: 'review',\n        created_at: '2024-12-10T09:15:00Z',\n        created_by: 'John Smith'\n      },\n      {\n        id: '5',\n        document_type: 'quality_spec',\n        title: 'Citrus Burst Quality Specifications',\n        document_number: 'QS-CBF-001',\n        version: '2.0',\n        item_name: 'Citrus Burst Flavor',\n        status: 'approved',\n        valid_from: '2024-12-01',\n        valid_until: '2025-11-30',\n        created_at: '2024-12-01T11:20:00Z',\n        created_by: 'Sarah Johnson'\n      },\n      {\n        id: '6',\n        document_type: 'msds',\n        title: 'Propylene Glycol Safety Data Sheet',\n        document_number: 'MSDS-PG-001',\n        version: '1.0',\n        item_name: 'Propylene Glycol',\n        status: 'draft',\n        created_at: '2024-12-09T13:45:00Z',\n        created_by: 'Emily Davis'\n      }\n    ]\n\n    setTimeout(() => {\n      setDocuments(mockDocuments)\n      setLoading(false)\n    }, 1000)\n  }, [])\n\n  const getDocumentTypeIcon = (type: string) => {\n    switch (type) {\n      case 'msds':\n        return ShieldCheckIcon\n      case 'coa':\n        return ClipboardDocumentCheckIcon\n      case 'tds':\n        return DocumentTextIcon\n      case 'lab_report':\n        return BeakerIcon\n      case 'quality_spec':\n        return ClipboardDocumentCheckIcon\n      default:\n        return DocumentTextIcon\n    }\n  }\n\n  const getDocumentTypeName = (type: string) => {\n    switch (type) {\n      case 'msds':\n        return 'MSDS'\n      case 'coa':\n        return 'COA'\n      case 'tds':\n        return 'TDS'\n      case 'lab_report':\n        return 'Lab Report'\n      case 'quality_spec':\n        return 'Quality Spec'\n      default:\n        return type.toUpperCase()\n    }\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'approved':\n        return 'bg-green-100 text-green-800'\n      case 'review':\n        return 'bg-yellow-100 text-yellow-800'\n      case 'draft':\n        return 'bg-gray-100 text-gray-800'\n      case 'expired':\n        return 'bg-red-100 text-red-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const filteredDocuments = activeTab === 'all' \n    ? documents \n    : documents.filter(doc => {\n        if (activeTab === 'lab_reports') return doc.document_type === 'lab_report'\n        return doc.document_type === activeTab\n      })\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    })\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return (\n    <MainLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"sm:flex sm:items-center sm:justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Quality Management</h1>\n            <p className=\"mt-2 text-sm text-gray-700\">\n              Manage quality documents, certificates, and compliance records\n            </p>\n          </div>\n          <div className=\"mt-4 sm:mt-0\">\n            <button\n              type=\"button\"\n              className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n            >\n              <PlusIcon className=\"-ml-1 mr-2 h-5 w-5\" aria-hidden=\"true\" />\n              New Document\n            </button>\n          </div>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-4\">\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <ShieldCheckIcon className=\"h-6 w-6 text-red-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">MSDS</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {documents.filter(d => d.document_type === 'msds').length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <ClipboardDocumentCheckIcon className=\"h-6 w-6 text-green-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">COA</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {documents.filter(d => d.document_type === 'coa').length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <DocumentTextIcon className=\"h-6 w-6 text-blue-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">TDS</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {documents.filter(d => d.document_type === 'tds').length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <BeakerIcon className=\"h-6 w-6 text-purple-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Lab Reports</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {documents.filter(d => d.document_type === 'lab_report').length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"bg-white shadow rounded-lg\">\n          <div className=\"border-b border-gray-200\">\n            <nav className=\"-mb-px flex space-x-8 px-6\" aria-label=\"Tabs\">\n              {[\n                { key: 'all', name: 'All Documents' },\n                { key: 'msds', name: 'MSDS' },\n                { key: 'coa', name: 'COA' },\n                { key: 'tds', name: 'TDS' },\n                { key: 'lab_reports', name: 'Lab Reports' }\n              ].map((tab) => (\n                <button\n                  key={tab.key}\n                  onClick={() => setActiveTab(tab.key as any)}\n                  className={`${\n                    activeTab === tab.key\n                      ? 'border-green-500 text-green-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}\n                >\n                  {tab.name}\n                </button>\n              ))}\n            </nav>\n          </div>\n\n          {/* Documents Table */}\n          <div className=\"px-4 py-5 sm:p-6\">\n            <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n              {activeTab === 'all' ? 'All Documents' : activeTab.toUpperCase()} ({filteredDocuments.length})\n            </h3>\n            \n            {loading ? (\n              <div className=\"animate-pulse\">\n                {[...Array(6)].map((_, i) => (\n                  <div key={i} className=\"flex items-center space-x-4 py-4\">\n                    <div className=\"h-4 bg-gray-200 rounded w-1/4\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/6\"></div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full divide-y divide-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Document\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Type\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Related Item\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Valid Until\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Created\n                      </th>\n                      <th className=\"relative px-6 py-3\">\n                        <span className=\"sr-only\">Actions</span>\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {filteredDocuments.map((doc) => {\n                      const IconComponent = getDocumentTypeIcon(doc.document_type)\n                      return (\n                        <tr key={doc.id} className=\"hover:bg-gray-50\">\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\n                            <div className=\"flex items-center\">\n                              <IconComponent className=\"h-5 w-5 text-gray-400 mr-3\" />\n                              <div>\n                                <div className=\"text-sm font-medium text-gray-900\">{doc.title}</div>\n                                <div className=\"text-sm text-gray-500\">\n                                  {doc.document_number} • v{doc.version}\n                                </div>\n                              </div>\n                            </div>\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {getDocumentTypeName(doc.document_type)}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {doc.item_name || doc.batch_number || '-'}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\n                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(doc.status)}`}>\n                              {doc.status.toUpperCase()}\n                            </span>\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            {doc.valid_until ? formatDate(doc.valid_until) : '-'}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            {formatDate(doc.created_at)}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                            <button className=\"text-green-600 hover:text-green-900 mr-3\">\n                              View\n                            </button>\n                            <button className=\"text-green-600 hover:text-green-900\">\n                              Edit\n                            </button>\n                          </td>\n                        </tr>\n                      )\n                    })}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </MainLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAsBe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkD;IAE3F,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,8BAA8B;YAC9B,MAAM,gBAAmC;gBACvC;oBACE,IAAI;oBACJ,eAAe;oBACf,OAAO;oBACP,iBAAiB;oBACjB,SAAS;oBACT,WAAW;oBACX,QAAQ;oBACR,YAAY;oBACZ,aAAa;oBACb,YAAY;oBACZ,YAAY;gBACd;gBACA;oBACE,IAAI;oBACJ,eAAe;oBACf,OAAO;oBACP,iBAAiB;oBACjB,SAAS;oBACT,cAAc;oBACd,QAAQ;oBACR,YAAY;oBACZ,YAAY;oBACZ,YAAY;gBACd;gBACA;oBACE,IAAI;oBACJ,eAAe;oBACf,OAAO;oBACP,iBAAiB;oBACjB,SAAS;oBACT,WAAW;oBACX,QAAQ;oBACR,YAAY;oBACZ,aAAa;oBACb,YAAY;oBACZ,YAAY;gBACd;gBACA;oBACE,IAAI;oBACJ,eAAe;oBACf,OAAO;oBACP,iBAAiB;oBACjB,SAAS;oBACT,cAAc;oBACd,QAAQ;oBACR,YAAY;oBACZ,YAAY;gBACd;gBACA;oBACE,IAAI;oBACJ,eAAe;oBACf,OAAO;oBACP,iBAAiB;oBACjB,SAAS;oBACT,WAAW;oBACX,QAAQ;oBACR,YAAY;oBACZ,aAAa;oBACb,YAAY;oBACZ,YAAY;gBACd;gBACA;oBACE,IAAI;oBACJ,eAAe;oBACf,OAAO;oBACP,iBAAiB;oBACjB,SAAS;oBACT,WAAW;oBACX,QAAQ;oBACR,YAAY;oBACZ,YAAY;gBACd;aACD;YAED;yCAAW;oBACT,aAAa;oBACb,WAAW;gBACb;wCAAG;QACL;gCAAG,EAAE;IAEL,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO,gOAAA,CAAA,kBAAe;YACxB,KAAK;gBACH,OAAO,sPAAA,CAAA,6BAA0B;YACnC,KAAK;gBACH,OAAO,kOAAA,CAAA,mBAAgB;YACzB,KAAK;gBACH,OAAO,sNAAA,CAAA,aAAU;YACnB,KAAK;gBACH,OAAO,sPAAA,CAAA,6BAA0B;YACnC;gBACE,OAAO,kOAAA,CAAA,mBAAgB;QAC3B;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO,KAAK,WAAW;QAC3B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB,cAAc,QACpC,YACA,UAAU,MAAM,CAAC,CAAA;QACf,IAAI,cAAc,eAAe,OAAO,IAAI,aAAa,KAAK;QAC9D,OAAO,IAAI,aAAa,KAAK;IAC/B;IAEJ,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC,iJAAA,CAAA,aAAU;kBACT,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAqB,eAAY;;;;;;oCAAS;;;;;;;;;;;;;;;;;;8BAOpE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gOAAA,CAAA,kBAAe;gDAAC,WAAU;gDAAuB,eAAY;;;;;;;;;;;sDAEhE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEACX,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQrE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,sPAAA,CAAA,6BAA0B;gDAAC,WAAU;gDAAyB,eAAY;;;;;;;;;;;sDAE7E,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEACX,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQpE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,kOAAA,CAAA,mBAAgB;gDAAC,WAAU;gDAAwB,eAAY;;;;;;;;;;;sDAElE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEACX,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQpE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,sNAAA,CAAA,aAAU;gDAAC,WAAU;gDAA0B,eAAY;;;;;;;;;;;sDAE9D,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEACX,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK,cAAc,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAU7E,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;gCAA6B,cAAW;0CACpD;oCACC;wCAAE,KAAK;wCAAO,MAAM;oCAAgB;oCACpC;wCAAE,KAAK;wCAAQ,MAAM;oCAAO;oCAC5B;wCAAE,KAAK;wCAAO,MAAM;oCAAM;oCAC1B;wCAAE,KAAK;wCAAO,MAAM;oCAAM;oCAC1B;wCAAE,KAAK;wCAAe,MAAM;oCAAc;iCAC3C,CAAC,GAAG,CAAC,CAAC,oBACL,6LAAC;wCAEC,SAAS,IAAM,aAAa,IAAI,GAAG;wCACnC,WAAW,GACT,cAAc,IAAI,GAAG,GACjB,oCACA,6EACL,2DAA2D,CAAC;kDAE5D,IAAI,IAAI;uCARJ,IAAI,GAAG;;;;;;;;;;;;;;;sCAepB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCACX,cAAc,QAAQ,kBAAkB,UAAU,WAAW;wCAAG;wCAAG,kBAAkB,MAAM;wCAAC;;;;;;;gCAG9F,wBACC,6LAAC;oCAAI,WAAU;8CACZ;2CAAI,MAAM;qCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;4CAAY,WAAU;;8DACrB,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;2CALP;;;;;;;;;yDAUd,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAM,WAAU;;0DACf,6LAAC;gDAAM,WAAU;0DACf,cAAA,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;;;;;;;;;;;0DAIhC,6LAAC;gDAAM,WAAU;0DACd,kBAAkB,GAAG,CAAC,CAAC;oDACtB,MAAM,gBAAgB,oBAAoB,IAAI,aAAa;oDAC3D,qBACE,6LAAC;wDAAgB,WAAU;;0EACzB,6LAAC;gEAAG,WAAU;0EACZ,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAc,WAAU;;;;;;sFACzB,6LAAC;;8FACC,6LAAC;oFAAI,WAAU;8FAAqC,IAAI,KAAK;;;;;;8FAC7D,6LAAC;oFAAI,WAAU;;wFACZ,IAAI,eAAe;wFAAC;wFAAK,IAAI,OAAO;;;;;;;;;;;;;;;;;;;;;;;;0EAK7C,6LAAC;gEAAG,WAAU;0EACX,oBAAoB,IAAI,aAAa;;;;;;0EAExC,6LAAC;gEAAG,WAAU;0EACX,IAAI,SAAS,IAAI,IAAI,YAAY,IAAI;;;;;;0EAExC,6LAAC;gEAAG,WAAU;0EACZ,cAAA,6LAAC;oEAAK,WAAW,CAAC,wEAAwE,EAAE,eAAe,IAAI,MAAM,GAAG;8EACrH,IAAI,MAAM,CAAC,WAAW;;;;;;;;;;;0EAG3B,6LAAC;gEAAG,WAAU;0EACX,IAAI,WAAW,GAAG,WAAW,IAAI,WAAW,IAAI;;;;;;0EAEnD,6LAAC;gEAAG,WAAU;0EACX,WAAW,IAAI,UAAU;;;;;;0EAE5B,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAO,WAAU;kFAA2C;;;;;;kFAG7D,6LAAC;wEAAO,WAAU;kFAAsC;;;;;;;;;;;;;uDAjCnD,IAAI,EAAE;;;;;gDAuCnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpB;GA5XwB;;QACL,kIAAA,CAAA,UAAO;;;KADF", "debugId": null}}]}