{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\nimport {\n  HomeIcon,\n  CubeIcon,\n  BeakerIcon,\n  CogIcon,\n  DocumentTextIcon,\n  UsersIcon,\n  ChartBarIcon,\n  ClipboardDocumentListIcon,\n} from '@heroicons/react/24/outline'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { clsx } from 'clsx'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/', icon: HomeIcon },\n  { name: 'Inventory', href: '/inventory', icon: CubeIcon },\n  { name: 'Recipes', href: '/recipes', icon: ClipboardDocumentListIcon },\n  { name: 'Production', href: '/production', icon: CogIcon },\n  { name: 'Quality', href: '/quality', icon: BeakerIcon },\n  { name: 'Reports', href: '/reports', icon: ChartBarIcon },\n  { name: 'Documents', href: '/documents', icon: DocumentTextIcon },\n  { name: 'Users', href: '/users', icon: UsersIcon },\n]\n\ninterface SidebarProps {\n  open: boolean\n  setOpen: (open: boolean) => void\n}\n\nexport function Sidebar({ open, setOpen }: SidebarProps) {\n  const pathname = usePathname()\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <Transition.Root show={open} as={Fragment}>\n        <Dialog as=\"div\" className=\"relative z-50 lg:hidden\" onClose={setOpen}>\n          <Transition.Child\n            as={Fragment}\n            enter=\"transition-opacity ease-linear duration-300\"\n            enterFrom=\"opacity-0\"\n            enterTo=\"opacity-100\"\n            leave=\"transition-opacity ease-linear duration-300\"\n            leaveFrom=\"opacity-100\"\n            leaveTo=\"opacity-0\"\n          >\n            <div className=\"fixed inset-0 bg-gray-900/80\" />\n          </Transition.Child>\n\n          <div className=\"fixed inset-0 flex\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"transition ease-in-out duration-300 transform\"\n              enterFrom=\"-translate-x-full\"\n              enterTo=\"translate-x-0\"\n              leave=\"transition ease-in-out duration-300 transform\"\n              leaveFrom=\"translate-x-0\"\n              leaveTo=\"-translate-x-full\"\n            >\n              <Dialog.Panel className=\"relative mr-16 flex w-full max-w-xs flex-1\">\n                <Transition.Child\n                  as={Fragment}\n                  enter=\"ease-in-out duration-300\"\n                  enterFrom=\"opacity-0\"\n                  enterTo=\"opacity-100\"\n                  leave=\"ease-in-out duration-300\"\n                  leaveFrom=\"opacity-100\"\n                  leaveTo=\"opacity-0\"\n                >\n                  <div className=\"absolute left-full top-0 flex w-16 justify-center pt-5\">\n                    <button type=\"button\" className=\"-m-2.5 p-2.5\" onClick={() => setOpen(false)}>\n                      <span className=\"sr-only\">Close sidebar</span>\n                      <XMarkIcon className=\"h-6 w-6 text-white\" aria-hidden=\"true\" />\n                    </button>\n                  </div>\n                </Transition.Child>\n                <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4\">\n                  <div className=\"flex h-16 shrink-0 items-center\">\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">SFF</span>\n                    </div>\n                    <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n                  </div>\n                  <nav className=\"flex flex-1 flex-col\">\n                    <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n                      <li>\n                        <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                          {navigation.map((item) => (\n                            <li key={item.name}>\n                              <Link\n                                href={item.href}\n                                className={clsx(\n                                  pathname === item.href\n                                    ? 'bg-green-50 text-green-700'\n                                    : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                                  'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                                )}\n                              >\n                                <item.icon\n                                  className={clsx(\n                                    pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                                    'h-6 w-6 shrink-0'\n                                  )}\n                                  aria-hidden=\"true\"\n                                />\n                                {item.name}\n                              </Link>\n                            </li>\n                          ))}\n                        </ul>\n                      </li>\n                    </ul>\n                  </nav>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </Dialog>\n      </Transition.Root>\n\n      {/* Static sidebar for desktop */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 bg-white px-6 pb-4\">\n          <div className=\"flex h-16 shrink-0 items-center\">\n            <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-sm\">SFF</span>\n            </div>\n            <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n          </div>\n          <nav className=\"flex flex-1 flex-col\">\n            <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n              <li>\n                <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                  {navigation.map((item) => (\n                    <li key={item.name}>\n                      <Link\n                        href={item.href}\n                        className={clsx(\n                          pathname === item.href\n                            ? 'bg-green-50 text-green-700'\n                            : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                          'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                        )}\n                      >\n                        <item.icon\n                          className={clsx(\n                            pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                            'h-6 w-6 shrink-0'\n                          )}\n                          aria-hidden=\"true\"\n                        />\n                        {item.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </li>\n            </ul>\n          </nav>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AAjBA;;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,+MAAA,CAAA,WAAQ;IAAC;IAC/C;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,+MAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,iPAAA,CAAA,4BAAyB;IAAC;IACrE;QAAE,MAAM;QAAc,MAAM;QAAe,MAAM,6MAAA,CAAA,UAAO;IAAC;IACzD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,mNAAA,CAAA,aAAU;IAAC;IACtD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,uNAAA,CAAA,eAAY;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,+NAAA,CAAA,mBAAgB;IAAC;IAChE;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,iNAAA,CAAA,YAAS;IAAC;CAClD;AAOM,SAAS,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAgB;IACrD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE;;0BAEE,8OAAC,uLAAA,CAAA,aAAU,CAAC,IAAI;gBAAC,MAAM;gBAAM,IAAI,qMAAA,CAAA,WAAQ;0BACvC,cAAA,8OAAC,+KAAA,CAAA,SAAM;oBAAC,IAAG;oBAAM,WAAU;oBAA0B,SAAS;;sCAC5D,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,qMAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;gCACf,IAAI,qMAAA,CAAA,WAAQ;gCACZ,OAAM;gCACN,WAAU;gCACV,SAAQ;gCACR,OAAM;gCACN,WAAU;gCACV,SAAQ;0CAER,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;oCAAC,WAAU;;sDACtB,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;4CACf,IAAI,qMAAA,CAAA,WAAQ;4CACZ,OAAM;4CACN,WAAU;4CACV,SAAQ;4CACR,OAAM;4CACN,WAAU;4CACV,SAAQ;sDAER,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAO,MAAK;oDAAS,WAAU;oDAAe,SAAS,IAAM,QAAQ;;sEACpE,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC,iNAAA,CAAA,YAAS;4DAAC,WAAU;4DAAqB,eAAY;;;;;;;;;;;;;;;;;;;;;;sDAI5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,8OAAC;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;8DAE7D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAG,MAAK;wDAAO,WAAU;kEACxB,cAAA,8OAAC;sEACC,cAAA,8OAAC;gEAAG,MAAK;gEAAO,WAAU;0EACvB,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;kFACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4EACH,MAAM,KAAK,IAAI;4EACf,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;8FAGF,8OAAC,KAAK,IAAI;oFACR,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;oFAEF,eAAY;;;;;;gFAEb,KAAK,IAAI;;;;;;;uEAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiCxC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAE7D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,MAAK;gCAAO,WAAU;0CACxB,cAAA,8OAAC;8CACC,cAAA,8OAAC;wCAAG,MAAK;wCAAO,WAAU;kDACvB,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;sEAGF,8OAAC,KAAK,IAAI;4DACR,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;4DAEF,eAAY;;;;;;wDAEb,KAAK,IAAI;;;;;;;+CAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BtC", "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Menu, Transition } from '@headlessui/react'\nimport { Bars3Icon, BellIcon } from '@heroicons/react/24/outline'\nimport { useAuth } from '@/components/providers'\nimport { clsx } from 'clsx'\n\ninterface HeaderProps {\n  setSidebarOpen: (open: boolean) => void\n}\n\nexport function Header({ setSidebarOpen }: HeaderProps) {\n  const { user, signOut } = useAuth()\n\n  const userNavigation = [\n    { name: 'Your profile', href: '#' },\n    { name: 'Settings', href: '#' },\n    { name: 'Sign out', href: '#', onClick: signOut },\n  ]\n\n  return (\n    <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n      <button\n        type=\"button\"\n        className=\"-m-2.5 p-2.5 text-gray-700 lg:hidden\"\n        onClick={() => setSidebarOpen(true)}\n      >\n        <span className=\"sr-only\">Open sidebar</span>\n        <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n      </button>\n\n      {/* Separator */}\n      <div className=\"h-6 w-px bg-gray-200 lg:hidden\" aria-hidden=\"true\" />\n\n      <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n        <div className=\"relative flex flex-1\">\n          {/* Search can be added here later */}\n        </div>\n        <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n          <button type=\"button\" className=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\">\n            <span className=\"sr-only\">View notifications</span>\n            <BellIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n\n          {/* Separator */}\n          <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200\" aria-hidden=\"true\" />\n\n          {/* Profile dropdown */}\n          <Menu as=\"div\" className=\"relative\">\n            <Menu.Button className=\"-m-1.5 flex items-center p-1.5\">\n              <span className=\"sr-only\">Open user menu</span>\n              <div className=\"h-8 w-8 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.username?.charAt(0).toUpperCase() || 'U'}\n                </span>\n              </div>\n              <span className=\"hidden lg:flex lg:items-center\">\n                <span className=\"ml-4 text-sm font-semibold leading-6 text-gray-900\" aria-hidden=\"true\">\n                  {user?.username || 'User'}\n                </span>\n                <span className=\"ml-2 text-xs text-gray-500 capitalize\">\n                  ({user?.role?.replace('_', ' ') || 'Role'})\n                </span>\n              </span>\n            </Menu.Button>\n            <Transition\n              as={Fragment}\n              enter=\"transition ease-out duration-100\"\n              enterFrom=\"transform opacity-0 scale-95\"\n              enterTo=\"transform opacity-100 scale-100\"\n              leave=\"transition ease-in duration-75\"\n              leaveFrom=\"transform opacity-100 scale-100\"\n              leaveTo=\"transform opacity-0 scale-95\"\n            >\n              <Menu.Items className=\"absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none\">\n                {userNavigation.map((item) => (\n                  <Menu.Item key={item.name}>\n                    {({ active }) => (\n                      <button\n                        onClick={item.onClick}\n                        className={clsx(\n                          active ? 'bg-gray-50' : '',\n                          'block w-full text-left px-3 py-1 text-sm leading-6 text-gray-900'\n                        )}\n                      >\n                        {item.name}\n                      </button>\n                    )}\n                  </Menu.Item>\n                ))}\n              </Menu.Items>\n            </Transition>\n          </Menu>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;AANA;;;;;;;AAYO,SAAS,OAAO,EAAE,cAAc,EAAe;IACpD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAAgB,MAAM;QAAI;QAClC;YAAE,MAAM;YAAY,MAAM;QAAI;QAC9B;YAAE,MAAM;YAAY,MAAM;YAAK,SAAS;QAAQ;KACjD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,eAAe;;kCAE9B,8OAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,8OAAC,iNAAA,CAAA,YAAS;wBAAC,WAAU;wBAAU,eAAY;;;;;;;;;;;;0BAI7C,8OAAC;gBAAI,WAAU;gBAAiC,eAAY;;;;;;0BAE5D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,MAAK;gCAAS,WAAU;;kDAC9B,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC,+MAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;0CAI5C,8OAAC;gCAAI,WAAU;gCAAgD,eAAY;;;;;;0CAG3E,8OAAC,2KAAA,CAAA,OAAI;gCAAC,IAAG;gCAAM,WAAU;;kDACvB,8OAAC,2KAAA,CAAA,OAAI,CAAC,MAAM;wCAAC,WAAU;;0DACrB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,MAAM,UAAU,OAAO,GAAG,iBAAiB;;;;;;;;;;;0DAGhD,8OAAC;gDAAK,WAAU;;kEACd,8OAAC;wDAAK,WAAU;wDAAqD,eAAY;kEAC9E,MAAM,YAAY;;;;;;kEAErB,8OAAC;wDAAK,WAAU;;4DAAwC;4DACpD,MAAM,MAAM,QAAQ,KAAK,QAAQ;4DAAO;;;;;;;;;;;;;;;;;;;kDAIhD,8OAAC,uLAAA,CAAA,aAAU;wCACT,IAAI,qMAAA,CAAA,WAAQ;wCACZ,OAAM;wCACN,WAAU;wCACV,SAAQ;wCACR,OAAM;wCACN,WAAU;wCACV,SAAQ;kDAER,cAAA,8OAAC,2KAAA,CAAA,OAAI,CAAC,KAAK;4CAAC,WAAU;sDACnB,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,2KAAA,CAAA,OAAI,CAAC,IAAI;8DACP,CAAC,EAAE,MAAM,EAAE,iBACV,8OAAC;4DACC,SAAS,KAAK,OAAO;4DACrB,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,SAAS,eAAe,IACxB;sEAGD,KAAK,IAAI;;;;;;mDATA,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqB3C", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/components/providers'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const { user } = useAuth()\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Sidebar open={sidebarOpen} setOpen={setSidebarOpen} />\n      \n      <div className=\"lg:pl-72\">\n        <Header setSidebarOpen={setSidebarOpen} />\n        \n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAWO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAa,SAAS;;;;;;0BAErC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,SAAM;wBAAC,gBAAgB;;;;;;kCAExB,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/ui/modal.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\n\ninterface ModalProps {\n  isOpen: boolean\n  onClose: () => void\n  title: string\n  children: React.ReactNode\n  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl'\n}\n\nexport function Modal({ isOpen, onClose, title, children, maxWidth = 'lg' }: ModalProps) {\n  const maxWidthClasses = {\n    sm: 'sm:max-w-sm',\n    md: 'sm:max-w-md',\n    lg: 'sm:max-w-lg',\n    xl: 'sm:max-w-xl',\n    '2xl': 'sm:max-w-2xl'\n  }\n\n  return (\n    <Transition.Root show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={onClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-out duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in duration-200\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 z-10 overflow-y-auto\">\n          <div className=\"flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"ease-out duration-300\"\n              enterFrom=\"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95\"\n              enterTo=\"opacity-100 translate-y-0 sm:scale-100\"\n              leave=\"ease-in duration-200\"\n              leaveFrom=\"opacity-100 translate-y-0 sm:scale-100\"\n              leaveTo=\"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95\"\n            >\n              <Dialog.Panel className={`relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full ${maxWidthClasses[maxWidth]} sm:p-6`}>\n                <div className=\"absolute right-0 top-0 hidden pr-4 pt-4 sm:block\">\n                  <button\n                    type=\"button\"\n                    className=\"rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2\"\n                    onClick={onClose}\n                  >\n                    <span className=\"sr-only\">Close</span>\n                    <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                  </button>\n                </div>\n                <div className=\"sm:flex sm:items-start\">\n                  <div className=\"mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full\">\n                    <Dialog.Title as=\"h3\" className=\"text-lg font-semibold leading-6 text-gray-900 mb-4\">\n                      {title}\n                    </Dialog.Title>\n                    <div className=\"mt-2\">\n                      {children}\n                    </div>\n                  </div>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </div>\n      </Dialog>\n    </Transition.Root>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAcO,SAAS,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAc;IACrF,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;IACT;IAEA,qBACE,8OAAC,uLAAA,CAAA,aAAU,CAAC,IAAI;QAAC,MAAM;QAAQ,IAAI,qMAAA,CAAA,WAAQ;kBACzC,cAAA,8OAAC,+KAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,qMAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,qMAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;gCAAC,WAAW,CAAC,2HAA2H,EAAE,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC;;kDACvL,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS;;8DAET,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;;;;;;kDAG/C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;oDAAC,IAAG;oDAAK,WAAU;8DAC7B;;;;;;8DAEH,8OAAC;oDAAI,WAAU;8DACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvB", "debugId": null}}, {"offset": {"line": 920, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/app/users/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { useAuth } from '@/components/providers'\nimport { Modal } from '@/components/ui/modal'\nimport { DatabaseService } from '@/lib/database'\nimport {\n  PlusIcon,\n  UsersIcon,\n  ShieldCheckIcon,\n  MagnifyingGlassIcon,\n  EyeIcon,\n  EyeSlashIcon,\n  ExclamationTriangleIcon,\n  CheckCircleIcon\n} from '@heroicons/react/24/outline'\n\ninterface User {\n  id: string\n  username: string\n  full_name: string\n  email: string\n  role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n  department: string\n  phone?: string\n  is_active: boolean\n  last_login?: string\n  created_at: string\n  updated_at?: string\n}\n\nexport default function UsersPage() {\n  const { user } = useAuth()\n  const [users, setUsers] = useState<User[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedRole, setSelectedRole] = useState<'all' | 'admin' | 'quality_manager' | 'production_manager' | 'employee'>('all')\n  const [showAddModal, setShowAddModal] = useState(false)\n  const [showPassword, setShowPassword] = useState(false)\n  const [validationErrors, setValidationErrors] = useState<string[]>([])\n  const [isCreating, setIsCreating] = useState(false)\n  const [newUser, setNewUser] = useState({\n    username: '',\n    full_name: '',\n    email: '',\n    password: '',\n    confirm_password: '',\n    role: 'employee' as 'admin' | 'quality_manager' | 'production_manager' | 'employee',\n    department: '',\n    phone: '',\n    employee_id: '',\n    start_date: '',\n    notes: ''\n  })\n\n  useEffect(() => {\n    // Mock users data\n    const mockUsers: User[] = [\n      {\n        id: '1',\n        username: 'admin',\n        full_name: 'System Administrator',\n        email: '<EMAIL>',\n        role: 'admin',\n        department: 'IT',\n        phone: '+****************',\n        is_active: true,\n        last_login: '2024-12-11T09:30:00Z',\n        created_at: '2024-01-15T08:00:00Z'\n      },\n      {\n        id: '2',\n        username: 'quality',\n        full_name: 'Sarah Johnson',\n        email: '<EMAIL>',\n        role: 'quality_manager',\n        department: 'Quality Assurance',\n        phone: '+****************',\n        is_active: true,\n        last_login: '2024-12-11T08:45:00Z',\n        created_at: '2024-02-01T10:00:00Z'\n      },\n      {\n        id: '3',\n        username: 'production',\n        full_name: 'Mike Wilson',\n        email: '<EMAIL>',\n        role: 'production_manager',\n        department: 'Production',\n        phone: '+****************',\n        is_active: true,\n        last_login: '2024-12-11T07:15:00Z',\n        created_at: '2024-02-15T14:30:00Z'\n      },\n      {\n        id: '4',\n        username: 'employee',\n        full_name: 'Emily Davis',\n        email: '<EMAIL>',\n        role: 'employee',\n        department: 'Production',\n        phone: '+****************',\n        is_active: true,\n        last_login: '2024-12-10T16:20:00Z',\n        created_at: '2024-03-01T09:00:00Z'\n      },\n      {\n        id: '5',\n        username: 'jsmith',\n        full_name: 'John Smith',\n        email: '<EMAIL>',\n        role: 'employee',\n        department: 'Quality Assurance',\n        phone: '+****************',\n        is_active: true,\n        last_login: '2024-12-11T10:00:00Z',\n        created_at: '2024-03-15T11:30:00Z'\n      },\n      {\n        id: '6',\n        username: 'agarcia',\n        full_name: 'Ana Garcia',\n        email: '<EMAIL>',\n        role: 'quality_manager',\n        department: 'Quality Assurance',\n        phone: '+****************',\n        is_active: false,\n        last_login: '2024-11-28T15:45:00Z',\n        created_at: '2024-04-01T13:00:00Z'\n      }\n    ]\n\n    setTimeout(() => {\n      setUsers(mockUsers)\n      setLoading(false)\n    }, 1000)\n  }, [])\n\n  const handleAddUser = () => {\n    const user: User = {\n      id: (users.length + 1).toString(),\n      username: newUser.username,\n      full_name: newUser.full_name,\n      email: newUser.email,\n      role: newUser.role,\n      department: newUser.department,\n      phone: newUser.phone,\n      is_active: true,\n      created_at: new Date().toISOString()\n    }\n\n    setUsers([...users, user])\n    setShowAddModal(false)\n    setNewUser({\n      username: '',\n      full_name: '',\n      email: '',\n      role: 'employee',\n      department: '',\n      phone: ''\n    })\n  }\n\n  const roles = [\n    { key: 'all', name: 'All Users', count: users.length },\n    { key: 'admin', name: 'Administrators', count: users.filter(u => u.role === 'admin').length },\n    { key: 'quality_manager', name: 'Quality Managers', count: users.filter(u => u.role === 'quality_manager').length },\n    { key: 'production_manager', name: 'Production Managers', count: users.filter(u => u.role === 'production_manager').length },\n    { key: 'employee', name: 'Employees', count: users.filter(u => u.role === 'employee').length }\n  ]\n\n  const filteredUsers = users.filter(user => {\n    const matchesSearch = user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         user.department.toLowerCase().includes(searchTerm.toLowerCase())\n    \n    const matchesRole = selectedRole === 'all' || user.role === selectedRole\n    \n    return matchesSearch && matchesRole\n  })\n\n  const getRoleColor = (role: string) => {\n    switch (role) {\n      case 'admin':\n        return 'bg-red-100 text-red-800'\n      case 'quality_manager':\n        return 'bg-purple-100 text-purple-800'\n      case 'production_manager':\n        return 'bg-blue-100 text-blue-800'\n      case 'employee':\n        return 'bg-green-100 text-green-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getRoleName = (role: string) => {\n    switch (role) {\n      case 'admin':\n        return 'Administrator'\n      case 'quality_manager':\n        return 'Quality Manager'\n      case 'production_manager':\n        return 'Production Manager'\n      case 'employee':\n        return 'Employee'\n      default:\n        return role\n    }\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    })\n  }\n\n  // Check if current user is admin\n  const isAdmin = user?.role === 'admin'\n\n  if (!user) {\n    return null\n  }\n\n  if (!isAdmin) {\n    return (\n      <MainLayout>\n        <div className=\"text-center py-12\">\n          <ShieldCheckIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900\">Access Denied</h3>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            You don't have permission to access user management.\n          </p>\n        </div>\n      </MainLayout>\n    )\n  }\n\n  return (\n    <MainLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"sm:flex sm:items-center sm:justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">User Management</h1>\n            <p className=\"mt-2 text-sm text-gray-700\">\n              Manage user accounts, roles, and permissions\n            </p>\n          </div>\n          <div className=\"mt-4 sm:mt-0\">\n            <button\n              type=\"button\"\n              onClick={() => setShowAddModal(true)}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n            >\n              <PlusIcon className=\"-ml-1 mr-2 h-5 w-5\" aria-hidden=\"true\" />\n              Add User\n            </button>\n          </div>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-4\">\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <UsersIcon className=\"h-6 w-6 text-blue-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Users</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">{users.length}</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"h-6 w-6 bg-green-100 rounded-full flex items-center justify-center\">\n                    <div className=\"h-2 w-2 bg-green-600 rounded-full\"></div>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Active Users</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {users.filter(u => u.is_active).length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <ShieldCheckIcon className=\"h-6 w-6 text-red-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Administrators</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {users.filter(u => u.role === 'admin').length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"h-6 w-6 bg-blue-100 rounded-full flex items-center justify-center\">\n                    <span className=\"text-xs font-medium text-blue-600\">M</span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Managers</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {users.filter(u => u.role.includes('manager')).length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Search and Filters */}\n        <div className=\"bg-white shadow rounded-lg p-6\">\n          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\n            <div className=\"relative flex-1 max-w-lg\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\n              </div>\n              <input\n                type=\"text\"\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-green-500 focus:border-green-500\"\n                placeholder=\"Search users...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n              />\n            </div>\n            \n            <div className=\"flex space-x-2\">\n              {roles.map((role) => (\n                <button\n                  key={role.key}\n                  onClick={() => setSelectedRole(role.key as any)}\n                  className={`px-3 py-2 rounded-md text-sm font-medium ${\n                    selectedRole === role.key\n                      ? 'bg-green-100 text-green-700'\n                      : 'text-gray-500 hover:text-gray-700'\n                  }`}\n                >\n                  {role.name} ({role.count})\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Users Table */}\n        <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n              Users ({filteredUsers.length})\n            </h3>\n            \n            {loading ? (\n              <div className=\"animate-pulse\">\n                {[...Array(6)].map((_, i) => (\n                  <div key={i} className=\"flex items-center space-x-4 py-4\">\n                    <div className=\"h-10 w-10 bg-gray-200 rounded-full\"></div>\n                    <div className=\"flex-1 space-y-2\">\n                      <div className=\"h-4 bg-gray-200 rounded w-1/4\"></div>\n                      <div className=\"h-3 bg-gray-200 rounded w-1/6\"></div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full divide-y divide-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        User\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Role\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Department\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Last Login\n                      </th>\n                      <th className=\"relative px-6 py-3\">\n                        <span className=\"sr-only\">Actions</span>\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {filteredUsers.map((user) => (\n                      <tr key={user.id} className=\"hover:bg-gray-50\">\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"flex items-center\">\n                            <div className=\"h-10 w-10 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center\">\n                              <span className=\"text-sm font-medium text-white\">\n                                {user.full_name.charAt(0).toUpperCase()}\n                              </span>\n                            </div>\n                            <div className=\"ml-4\">\n                              <div className=\"text-sm font-medium text-gray-900\">{user.full_name}</div>\n                              <div className=\"text-sm text-gray-500\">{user.email}</div>\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>\n                            {getRoleName(user.role)}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {user.department}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                            user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n                          }`}>\n                            {user.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                          {user.last_login ? formatDate(user.last_login) : 'Never'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                          <button className=\"text-green-600 hover:text-green-900 mr-3\">\n                            Edit\n                          </button>\n                          <button className=\"text-red-600 hover:text-red-900\">\n                            {user.is_active ? 'Deactivate' : 'Activate'}\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Add User Modal */}\n        <Modal\n          isOpen={showAddModal}\n          onClose={() => setShowAddModal(false)}\n          title=\"Add New User\"\n          maxWidth=\"xl\"\n        >\n          <form onSubmit={(e) => { e.preventDefault(); handleAddUser(); }} className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n              <div>\n                <label htmlFor=\"user_username\" className=\"block text-sm font-medium text-gray-700\">\n                  Username\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"user_username\"\n                  required\n                  className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                  value={newUser.username}\n                  onChange={(e) => setNewUser({ ...newUser, username: e.target.value })}\n                />\n              </div>\n              <div>\n                <label htmlFor=\"user_full_name\" className=\"block text-sm font-medium text-gray-700\">\n                  Full Name\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"user_full_name\"\n                  required\n                  className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                  value={newUser.full_name}\n                  onChange={(e) => setNewUser({ ...newUser, full_name: e.target.value })}\n                />\n              </div>\n              <div>\n                <label htmlFor=\"user_email\" className=\"block text-sm font-medium text-gray-700\">\n                  Email\n                </label>\n                <input\n                  type=\"email\"\n                  id=\"user_email\"\n                  required\n                  className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                  value={newUser.email}\n                  onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}\n                />\n              </div>\n              <div>\n                <label htmlFor=\"user_phone\" className=\"block text-sm font-medium text-gray-700\">\n                  Phone\n                </label>\n                <input\n                  type=\"tel\"\n                  id=\"user_phone\"\n                  className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                  value={newUser.phone}\n                  onChange={(e) => setNewUser({ ...newUser, phone: e.target.value })}\n                />\n              </div>\n              <div>\n                <label htmlFor=\"user_role\" className=\"block text-sm font-medium text-gray-700\">\n                  Role\n                </label>\n                <select\n                  id=\"user_role\"\n                  className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                  value={newUser.role}\n                  onChange={(e) => setNewUser({ ...newUser, role: e.target.value as any })}\n                >\n                  <option value=\"employee\">Employee</option>\n                  <option value=\"production_manager\">Production Manager</option>\n                  <option value=\"quality_manager\">Quality Manager</option>\n                  <option value=\"admin\">Administrator</option>\n                </select>\n              </div>\n              <div>\n                <label htmlFor=\"user_department\" className=\"block text-sm font-medium text-gray-700\">\n                  Department\n                </label>\n                <select\n                  id=\"user_department\"\n                  className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                  value={newUser.department}\n                  onChange={(e) => setNewUser({ ...newUser, department: e.target.value })}\n                >\n                  <option value=\"\">Select Department</option>\n                  <option value=\"Production\">Production</option>\n                  <option value=\"Quality Assurance\">Quality Assurance</option>\n                  <option value=\"IT\">IT</option>\n                  <option value=\"Administration\">Administration</option>\n                </select>\n              </div>\n            </div>\n            <div className=\"flex justify-end space-x-3 pt-4\">\n              <button\n                type=\"button\"\n                onClick={() => setShowAddModal(false)}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700\"\n              >\n                Add User\n              </button>\n            </div>\n          </form>\n        </Modal>\n      </div>\n    </MainLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAgCe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2E;IAC1H,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,UAAU;QACV,WAAW;QACX,OAAO;QACP,UAAU;QACV,kBAAkB;QAClB,MAAM;QACN,YAAY;QACZ,OAAO;QACP,aAAa;QACb,YAAY;QACZ,OAAO;IACT;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kBAAkB;QAClB,MAAM,YAAoB;YACxB;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;SACD;QAED,WAAW;YACT,SAAS;YACT,WAAW;QACb,GAAG;IACL,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,MAAM,OAAa;YACjB,IAAI,CAAC,MAAM,MAAM,GAAG,CAAC,EAAE,QAAQ;YAC/B,UAAU,QAAQ,QAAQ;YAC1B,WAAW,QAAQ,SAAS;YAC5B,OAAO,QAAQ,KAAK;YACpB,MAAM,QAAQ,IAAI;YAClB,YAAY,QAAQ,UAAU;YAC9B,OAAO,QAAQ,KAAK;YACpB,WAAW;YACX,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,SAAS;eAAI;YAAO;SAAK;QACzB,gBAAgB;QAChB,WAAW;YACT,UAAU;YACV,WAAW;YACX,OAAO;YACP,MAAM;YACN,YAAY;YACZ,OAAO;QACT;IACF;IAEA,MAAM,QAAQ;QACZ;YAAE,KAAK;YAAO,MAAM;YAAa,OAAO,MAAM,MAAM;QAAC;QACrD;YAAE,KAAK;YAAS,MAAM;YAAkB,OAAO,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM;QAAC;QAC5F;YAAE,KAAK;YAAmB,MAAM;YAAoB,OAAO,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,mBAAmB,MAAM;QAAC;QAClH;YAAE,KAAK;YAAsB,MAAM;YAAuB,OAAO,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,sBAAsB,MAAM;QAAC;QAC3H;YAAE,KAAK;YAAY,MAAM;YAAa,OAAO,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,YAAY,MAAM;QAAC;KAC9F;IAED,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,MAAM,gBAAgB,KAAK,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,KAAK,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAElF,MAAM,cAAc,iBAAiB,SAAS,KAAK,IAAI,KAAK;QAE5D,OAAO,iBAAiB;IAC1B;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,iCAAiC;IACjC,MAAM,UAAU,MAAM,SAAS;IAE/B,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC,8IAAA,CAAA,aAAU;sBACT,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6NAAA,CAAA,kBAAe;wBAAC,WAAU;;;;;;kCAC3B,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAMlD;IAEA,qBACE,8OAAC,8IAAA,CAAA,aAAU;kBACT,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;;kDAEV,8OAAC,+MAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAqB,eAAY;;;;;;oCAAS;;;;;;;;;;;;;;;;;;8BAOpE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAwB,eAAY;;;;;;;;;;;sDAE3D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAAqC,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOzE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;sDAGnB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEACX,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6NAAA,CAAA,kBAAe;gDAAC,WAAU;gDAAuB,eAAY;;;;;;;;;;;sDAEhE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEACX,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQzD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAoC;;;;;;;;;;;;;;;;sDAGxD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEACX,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUnE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;4CAAC,WAAU;4CAAwB,eAAY;;;;;;;;;;;kDAErE,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;0CAIjD,8OAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;wCAEC,SAAS,IAAM,gBAAgB,KAAK,GAAG;wCACvC,WAAW,CAAC,yCAAyC,EACnD,iBAAiB,KAAK,GAAG,GACrB,gCACA,qCACJ;;4CAED,KAAK,IAAI;4CAAC;4CAAG,KAAK,KAAK;4CAAC;;uCARpB,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;8BAgBvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAmD;oCACvD,cAAc,MAAM;oCAAC;;;;;;;4BAG9B,wBACC,8OAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;wCAAY,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;uCAJT;;;;;;;;;qDAUd,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;sDAIhC,8OAAC;4CAAM,WAAU;sDACd,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;oDAAiB,WAAU;;sEAC1B,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAK,WAAU;sFACb,KAAK,SAAS,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;kFAGzC,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FAAqC,KAAK,SAAS;;;;;;0FAClE,8OAAC;gFAAI,WAAU;0FAAyB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;sEAIxD,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAW,CAAC,wEAAwE,EAAE,aAAa,KAAK,IAAI,GAAG;0EAClH,YAAY,KAAK,IAAI;;;;;;;;;;;sEAG1B,8OAAC;4DAAG,WAAU;sEACX,KAAK,UAAU;;;;;;sEAElB,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAW,CAAC,wEAAwE,EACxF,KAAK,SAAS,GAAG,gCAAgC,2BACjD;0EACC,KAAK,SAAS,GAAG,WAAW;;;;;;;;;;;sEAGjC,8OAAC;4DAAG,WAAU;sEACX,KAAK,UAAU,GAAG,WAAW,KAAK,UAAU,IAAI;;;;;;sEAEnD,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAO,WAAU;8EAA2C;;;;;;8EAG7D,8OAAC;oEAAO,WAAU;8EACf,KAAK,SAAS,GAAG,eAAe;;;;;;;;;;;;;mDArC9B,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAkD9B,8OAAC,iIAAA,CAAA,QAAK;oBACJ,QAAQ;oBACR,SAAS,IAAM,gBAAgB;oBAC/B,OAAM;oBACN,UAAS;8BAET,cAAA,8OAAC;wBAAK,UAAU,CAAC;4BAAQ,EAAE,cAAc;4BAAI;wBAAiB;wBAAG,WAAU;;0CACzE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAgB,WAAU;0DAA0C;;;;;;0DAGnF,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,QAAQ;gDACR,WAAU;gDACV,OAAO,QAAQ,QAAQ;gDACvB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;kDAGvE,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAiB,WAAU;0DAA0C;;;;;;0DAGpF,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,QAAQ;gDACR,WAAU;gDACV,OAAO,QAAQ,SAAS;gDACxB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,WAAW,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;kDAGxE,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAa,WAAU;0DAA0C;;;;;;0DAGhF,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,QAAQ;gDACR,WAAU;gDACV,OAAO,QAAQ,KAAK;gDACpB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;kDAGpE,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAa,WAAU;0DAA0C;;;;;;0DAGhF,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,WAAU;gDACV,OAAO,QAAQ,KAAK;gDACpB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;kDAGpE,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAY,WAAU;0DAA0C;;;;;;0DAG/E,8OAAC;gDACC,IAAG;gDACH,WAAU;gDACV,OAAO,QAAQ,IAAI;gDACnB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAQ;;kEAEtE,8OAAC;wDAAO,OAAM;kEAAW;;;;;;kEACzB,8OAAC;wDAAO,OAAM;kEAAqB;;;;;;kEACnC,8OAAC;wDAAO,OAAM;kEAAkB;;;;;;kEAChC,8OAAC;wDAAO,OAAM;kEAAQ;;;;;;;;;;;;;;;;;;kDAG1B,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAkB,WAAU;0DAA0C;;;;;;0DAGrF,8OAAC;gDACC,IAAG;gDACH,WAAU;gDACV,OAAO,QAAQ,UAAU;gDACzB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,YAAY,EAAE,MAAM,CAAC,KAAK;oDAAC;;kEAErE,8OAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,8OAAC;wDAAO,OAAM;kEAAa;;;;;;kEAC3B,8OAAC;wDAAO,OAAM;kEAAoB;;;;;;kEAClC,8OAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,8OAAC;wDAAO,OAAM;kEAAiB;;;;;;;;;;;;;;;;;;;;;;;;0CAIrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}