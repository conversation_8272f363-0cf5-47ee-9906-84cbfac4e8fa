{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\nimport {\n  HomeIcon,\n  CubeIcon,\n  BeakerIcon,\n  CogIcon,\n  DocumentTextIcon,\n  UsersIcon,\n  ChartBarIcon,\n  ClipboardDocumentListIcon,\n} from '@heroicons/react/24/outline'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { clsx } from 'clsx'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/', icon: HomeIcon },\n  { name: 'Inventory', href: '/inventory', icon: CubeIcon },\n  { name: 'Recipes', href: '/recipes', icon: ClipboardDocumentListIcon },\n  { name: 'Production', href: '/production', icon: CogIcon },\n  { name: 'Quality', href: '/quality', icon: BeakerIcon },\n  { name: 'Reports', href: '/reports', icon: ChartBarIcon },\n  { name: 'Documents', href: '/documents', icon: DocumentTextIcon },\n  { name: 'Users', href: '/users', icon: UsersIcon },\n]\n\ninterface SidebarProps {\n  open: boolean\n  setOpen: (open: boolean) => void\n}\n\nexport function Sidebar({ open, setOpen }: SidebarProps) {\n  const pathname = usePathname()\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <Transition.Root show={open} as={Fragment}>\n        <Dialog as=\"div\" className=\"relative z-50 lg:hidden\" onClose={setOpen}>\n          <Transition.Child\n            as={Fragment}\n            enter=\"transition-opacity ease-linear duration-300\"\n            enterFrom=\"opacity-0\"\n            enterTo=\"opacity-100\"\n            leave=\"transition-opacity ease-linear duration-300\"\n            leaveFrom=\"opacity-100\"\n            leaveTo=\"opacity-0\"\n          >\n            <div className=\"fixed inset-0 bg-gray-900/80\" />\n          </Transition.Child>\n\n          <div className=\"fixed inset-0 flex\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"transition ease-in-out duration-300 transform\"\n              enterFrom=\"-translate-x-full\"\n              enterTo=\"translate-x-0\"\n              leave=\"transition ease-in-out duration-300 transform\"\n              leaveFrom=\"translate-x-0\"\n              leaveTo=\"-translate-x-full\"\n            >\n              <Dialog.Panel className=\"relative mr-16 flex w-full max-w-xs flex-1\">\n                <Transition.Child\n                  as={Fragment}\n                  enter=\"ease-in-out duration-300\"\n                  enterFrom=\"opacity-0\"\n                  enterTo=\"opacity-100\"\n                  leave=\"ease-in-out duration-300\"\n                  leaveFrom=\"opacity-100\"\n                  leaveTo=\"opacity-0\"\n                >\n                  <div className=\"absolute left-full top-0 flex w-16 justify-center pt-5\">\n                    <button type=\"button\" className=\"-m-2.5 p-2.5\" onClick={() => setOpen(false)}>\n                      <span className=\"sr-only\">Close sidebar</span>\n                      <XMarkIcon className=\"h-6 w-6 text-white\" aria-hidden=\"true\" />\n                    </button>\n                  </div>\n                </Transition.Child>\n                <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4\">\n                  <div className=\"flex h-16 shrink-0 items-center\">\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">SFF</span>\n                    </div>\n                    <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n                  </div>\n                  <nav className=\"flex flex-1 flex-col\">\n                    <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n                      <li>\n                        <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                          {navigation.map((item) => (\n                            <li key={item.name}>\n                              <Link\n                                href={item.href}\n                                className={clsx(\n                                  pathname === item.href\n                                    ? 'bg-green-50 text-green-700'\n                                    : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                                  'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                                )}\n                              >\n                                <item.icon\n                                  className={clsx(\n                                    pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                                    'h-6 w-6 shrink-0'\n                                  )}\n                                  aria-hidden=\"true\"\n                                />\n                                {item.name}\n                              </Link>\n                            </li>\n                          ))}\n                        </ul>\n                      </li>\n                    </ul>\n                  </nav>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </Dialog>\n      </Transition.Root>\n\n      {/* Static sidebar for desktop */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 bg-white px-6 pb-4\">\n          <div className=\"flex h-16 shrink-0 items-center\">\n            <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-sm\">SFF</span>\n            </div>\n            <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n          </div>\n          <nav className=\"flex flex-1 flex-col\">\n            <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n              <li>\n                <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                  {navigation.map((item) => (\n                    <li key={item.name}>\n                      <Link\n                        href={item.href}\n                        className={clsx(\n                          pathname === item.href\n                            ? 'bg-green-50 text-green-700'\n                            : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                          'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                        )}\n                      >\n                        <item.icon\n                          className={clsx(\n                            pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                            'h-6 w-6 shrink-0'\n                          )}\n                          aria-hidden=\"true\"\n                        />\n                        {item.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </li>\n            </ul>\n          </nav>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;;;AAjBA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,kNAAA,CAAA,WAAQ;IAAC;IAC/C;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,oPAAA,CAAA,4BAAyB;IAAC;IACrE;QAAE,MAAM;QAAc,MAAM;QAAe,MAAM,gNAAA,CAAA,UAAO;IAAC;IACzD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,sNAAA,CAAA,aAAU;IAAC;IACtD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,0NAAA,CAAA,eAAY;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kOAAA,CAAA,mBAAgB;IAAC;IAChE;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,oNAAA,CAAA,YAAS;IAAC;CAClD;AAOM,SAAS,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAgB;;IACrD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE;;0BAEE,6LAAC,0LAAA,CAAA,aAAU,CAAC,IAAI;gBAAC,MAAM;gBAAM,IAAI,6JAAA,CAAA,WAAQ;0BACvC,cAAA,6LAAC,kLAAA,CAAA,SAAM;oBAAC,IAAG;oBAAM,WAAU;oBAA0B,SAAS;;sCAC5D,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,6JAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;sCAGjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;gCACf,IAAI,6JAAA,CAAA,WAAQ;gCACZ,OAAM;gCACN,WAAU;gCACV,SAAQ;gCACR,OAAM;gCACN,WAAU;gCACV,SAAQ;0CAER,cAAA,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;oCAAC,WAAU;;sDACtB,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4CACf,IAAI,6JAAA,CAAA,WAAQ;4CACZ,OAAM;4CACN,WAAU;4CACV,SAAQ;4CACR,OAAM;4CACN,WAAU;4CACV,SAAQ;sDAER,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAO,MAAK;oDAAS,WAAU;oDAAe,SAAS,IAAM,QAAQ;;sEACpE,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;4DAAqB,eAAY;;;;;;;;;;;;;;;;;;;;;;sDAI5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,6LAAC;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;8DAE7D,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAG,MAAK;wDAAO,WAAU;kEACxB,cAAA,6LAAC;sEACC,cAAA,6LAAC;gEAAG,MAAK;gEAAO,WAAU;0EACvB,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;kFACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4EACH,MAAM,KAAK,IAAI;4EACf,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;8FAGF,6LAAC,KAAK,IAAI;oFACR,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;oFAEF,eAAY;;;;;;gFAEb,KAAK,IAAI;;;;;;;uEAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiCxC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAE7D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,MAAK;gCAAO,WAAU;0CACxB,cAAA,6LAAC;8CACC,cAAA,6LAAC;wCAAG,MAAK;wCAAO,WAAU;kDACvB,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;sEAGF,6LAAC,KAAK,IAAI;4DACR,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;4DAEF,eAAY;;;;;;wDAEb,KAAK,IAAI;;;;;;;+CAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BtC;GAtIgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Menu, Transition } from '@headlessui/react'\nimport { Bars3Icon, BellIcon } from '@heroicons/react/24/outline'\nimport { useAuth } from '@/components/providers'\nimport { clsx } from 'clsx'\n\ninterface HeaderProps {\n  setSidebarOpen: (open: boolean) => void\n}\n\nexport function Header({ setSidebarOpen }: HeaderProps) {\n  const { user, signOut } = useAuth()\n\n  const userNavigation = [\n    { name: 'Your profile', href: '#' },\n    { name: 'Settings', href: '#' },\n    { name: 'Sign out', href: '#', onClick: signOut },\n  ]\n\n  return (\n    <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n      <button\n        type=\"button\"\n        className=\"-m-2.5 p-2.5 text-gray-700 lg:hidden\"\n        onClick={() => setSidebarOpen(true)}\n      >\n        <span className=\"sr-only\">Open sidebar</span>\n        <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n      </button>\n\n      {/* Separator */}\n      <div className=\"h-6 w-px bg-gray-200 lg:hidden\" aria-hidden=\"true\" />\n\n      <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n        <div className=\"relative flex flex-1\">\n          {/* Search can be added here later */}\n        </div>\n        <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n          <button type=\"button\" className=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\">\n            <span className=\"sr-only\">View notifications</span>\n            <BellIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n\n          {/* Separator */}\n          <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200\" aria-hidden=\"true\" />\n\n          {/* Profile dropdown */}\n          <Menu as=\"div\" className=\"relative\">\n            <Menu.Button className=\"-m-1.5 flex items-center p-1.5\">\n              <span className=\"sr-only\">Open user menu</span>\n              <div className=\"h-8 w-8 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.username?.charAt(0).toUpperCase() || 'U'}\n                </span>\n              </div>\n              <span className=\"hidden lg:flex lg:items-center\">\n                <span className=\"ml-4 text-sm font-semibold leading-6 text-gray-900\" aria-hidden=\"true\">\n                  {user?.username || 'User'}\n                </span>\n                <span className=\"ml-2 text-xs text-gray-500 capitalize\">\n                  ({user?.role?.replace('_', ' ') || 'Role'})\n                </span>\n              </span>\n            </Menu.Button>\n            <Transition\n              as={Fragment}\n              enter=\"transition ease-out duration-100\"\n              enterFrom=\"transform opacity-0 scale-95\"\n              enterTo=\"transform opacity-100 scale-100\"\n              leave=\"transition ease-in duration-75\"\n              leaveFrom=\"transform opacity-100 scale-100\"\n              leaveTo=\"transform opacity-0 scale-95\"\n            >\n              <Menu.Items className=\"absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none\">\n                {userNavigation.map((item) => (\n                  <Menu.Item key={item.name}>\n                    {({ active }) => (\n                      <button\n                        onClick={item.onClick}\n                        className={clsx(\n                          active ? 'bg-gray-50' : '',\n                          'block w-full text-left px-3 py-1 text-sm leading-6 text-gray-900'\n                        )}\n                      >\n                        {item.name}\n                      </button>\n                    )}\n                  </Menu.Item>\n                ))}\n              </Menu.Items>\n            </Transition>\n          </Menu>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;;;AANA;;;;;;AAYO,SAAS,OAAO,EAAE,cAAc,EAAe;;IACpD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAAgB,MAAM;QAAI;QAClC;YAAE,MAAM;YAAY,MAAM;QAAI;QAC9B;YAAE,MAAM;YAAY,MAAM;YAAK,SAAS;QAAQ;KACjD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,eAAe;;kCAE9B,6LAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;wBAAU,eAAY;;;;;;;;;;;;0BAI7C,6LAAC;gBAAI,WAAU;gBAAiC,eAAY;;;;;;0BAE5D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,MAAK;gCAAS,WAAU;;kDAC9B,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;gCAAgD,eAAY;;;;;;0CAG3E,6LAAC,8KAAA,CAAA,OAAI;gCAAC,IAAG;gCAAM,WAAU;;kDACvB,6LAAC,8KAAA,CAAA,OAAI,CAAC,MAAM;wCAAC,WAAU;;0DACrB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb,MAAM,UAAU,OAAO,GAAG,iBAAiB;;;;;;;;;;;0DAGhD,6LAAC;gDAAK,WAAU;;kEACd,6LAAC;wDAAK,WAAU;wDAAqD,eAAY;kEAC9E,MAAM,YAAY;;;;;;kEAErB,6LAAC;wDAAK,WAAU;;4DAAwC;4DACpD,MAAM,MAAM,QAAQ,KAAK,QAAQ;4DAAO;;;;;;;;;;;;;;;;;;;kDAIhD,6LAAC,0LAAA,CAAA,aAAU;wCACT,IAAI,6JAAA,CAAA,WAAQ;wCACZ,OAAM;wCACN,WAAU;wCACV,SAAQ;wCACR,OAAM;wCACN,WAAU;wCACV,SAAQ;kDAER,cAAA,6LAAC,8KAAA,CAAA,OAAI,CAAC,KAAK;4CAAC,WAAU;sDACnB,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;8DACP,CAAC,EAAE,MAAM,EAAE,iBACV,6LAAC;4DACC,SAAS,KAAK,OAAO;4DACrB,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,SAAS,eAAe,IACxB;sEAGD,KAAK,IAAI;;;;;;mDATA,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqB3C;GAtFgB;;QACY,kIAAA,CAAA,UAAO;;;KADnB", "debugId": null}}, {"offset": {"line": 680, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/components/providers'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const { user } = useAuth()\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Sidebar open={sidebarOpen} setOpen={setSidebarOpen} />\n      \n      <div className=\"lg:pl-72\">\n        <Header setSidebarOpen={setSidebarOpen} />\n        \n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAWO,SAAS,WAAW,EAAE,QAAQ,EAAmB;;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAO;gBAAC,MAAM;gBAAa,SAAS;;;;;;0BAErC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,SAAM;wBAAC,gBAAgB;;;;;;kCAExB,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAnBgB;;QAEG,kIAAA,CAAA,UAAO;;;KAFV", "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/ui/modal.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\n\ninterface ModalProps {\n  isOpen: boolean\n  onClose: () => void\n  title: string\n  children: React.ReactNode\n  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl'\n}\n\nexport function Modal({ isOpen, onClose, title, children, maxWidth = 'lg' }: ModalProps) {\n  const maxWidthClasses = {\n    sm: 'sm:max-w-sm',\n    md: 'sm:max-w-md',\n    lg: 'sm:max-w-lg',\n    xl: 'sm:max-w-xl',\n    '2xl': 'sm:max-w-2xl'\n  }\n\n  return (\n    <Transition.Root show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={onClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-out duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in duration-200\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 z-10 overflow-y-auto\">\n          <div className=\"flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"ease-out duration-300\"\n              enterFrom=\"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95\"\n              enterTo=\"opacity-100 translate-y-0 sm:scale-100\"\n              leave=\"ease-in duration-200\"\n              leaveFrom=\"opacity-100 translate-y-0 sm:scale-100\"\n              leaveTo=\"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95\"\n            >\n              <Dialog.Panel className={`relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full ${maxWidthClasses[maxWidth]} sm:p-6`}>\n                <div className=\"absolute right-0 top-0 hidden pr-4 pt-4 sm:block\">\n                  <button\n                    type=\"button\"\n                    className=\"rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2\"\n                    onClick={onClose}\n                  >\n                    <span className=\"sr-only\">Close</span>\n                    <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                  </button>\n                </div>\n                <div className=\"sm:flex sm:items-start\">\n                  <div className=\"mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full\">\n                    <Dialog.Title as=\"h3\" className=\"text-lg font-semibold leading-6 text-gray-900 mb-4\">\n                      {title}\n                    </Dialog.Title>\n                    <div className=\"mt-2\">\n                      {children}\n                    </div>\n                  </div>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </div>\n      </Dialog>\n    </Transition.Root>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAcO,SAAS,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAc;IACrF,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;IACT;IAEA,qBACE,6LAAC,0LAAA,CAAA,aAAU,CAAC,IAAI;QAAC,MAAM;QAAQ,IAAI,6JAAA,CAAA,WAAQ;kBACzC,cAAA,6LAAC,kLAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,6JAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,6JAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;gCAAC,WAAW,CAAC,2HAA2H,EAAE,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC;;kDACvL,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS;;8DAET,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;;;;;;kDAG/C,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;oDAAC,IAAG;oDAAK,WAAU;8DAC7B;;;;;;8DAEH,6LAAC;oDAAI,WAAU;8DACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvB;KA/DgB", "debugId": null}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createClientComponentClient } from '@supabase/auth-helpers-nextjs'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Client component client\nexport const createSupabaseClient = () => createClientComponentClient()\n\n// Admin client (server-side only)\nexport const createSupabaseAdminClient = () => {\n  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n  return createClient(supabaseUrl, serviceRoleKey, {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  })\n}\n\n// Database types\nexport interface Database {\n  public: {\n    Tables: {\n      profiles: {\n        Row: {\n          id: string\n          email: string\n          full_name: string\n          role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department: string | null\n          phone: string | null\n          is_active: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          email: string\n          full_name: string\n          role?: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department?: string | null\n          phone?: string | null\n          is_active?: boolean\n        }\n        Update: {\n          email?: string\n          full_name?: string\n          role?: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n          department?: string | null\n          phone?: string | null\n          is_active?: boolean\n        }\n      }\n      inventory_categories: {\n        Row: {\n          id: string\n          name: string\n          description: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          description?: string | null\n        }\n        Update: {\n          name?: string\n          description?: string | null\n        }\n      }\n      inventory_items: {\n        Row: {\n          id: string\n          name: string\n          sku: string\n          category_id: string | null\n          description: string | null\n          unit_of_measure: string\n          current_stock: number\n          minimum_stock: number\n          maximum_stock: number | null\n          unit_cost: number\n          supplier_info: any | null\n          storage_conditions: string | null\n          expiry_date: string | null\n          batch_number: string | null\n          is_active: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          sku: string\n          category_id?: string | null\n          description?: string | null\n          unit_of_measure: string\n          current_stock?: number\n          minimum_stock?: number\n          maximum_stock?: number | null\n          unit_cost?: number\n          supplier_info?: any | null\n          storage_conditions?: string | null\n          expiry_date?: string | null\n          batch_number?: string | null\n          is_active?: boolean\n        }\n        Update: {\n          name?: string\n          sku?: string\n          category_id?: string | null\n          description?: string | null\n          unit_of_measure?: string\n          current_stock?: number\n          minimum_stock?: number\n          maximum_stock?: number | null\n          unit_cost?: number\n          supplier_info?: any | null\n          storage_conditions?: string | null\n          expiry_date?: string | null\n          batch_number?: string | null\n          is_active?: boolean\n        }\n      }\n      recipes: {\n        Row: {\n          id: string\n          name: string\n          code: string\n          description: string | null\n          category: string | null\n          version: number\n          batch_size: number\n          unit_of_measure: string\n          estimated_cost: number | null\n          preparation_time: number | null\n          instructions: string | null\n          notes: string | null\n          is_active: boolean\n          created_by: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          name: string\n          code: string\n          description?: string | null\n          category?: string | null\n          version?: number\n          batch_size: number\n          unit_of_measure: string\n          estimated_cost?: number | null\n          preparation_time?: number | null\n          instructions?: string | null\n          notes?: string | null\n          is_active?: boolean\n          created_by?: string | null\n        }\n        Update: {\n          name?: string\n          code?: string\n          description?: string | null\n          category?: string | null\n          version?: number\n          batch_size?: number\n          unit_of_measure?: string\n          estimated_cost?: number | null\n          preparation_time?: number | null\n          instructions?: string | null\n          notes?: string | null\n          is_active?: boolean\n        }\n      }\n      production_batches: {\n        Row: {\n          id: string\n          batch_number: string\n          recipe_id: string\n          batch_type: 'test' | 'production'\n          planned_quantity: number\n          actual_quantity: number | null\n          unit_of_measure: string\n          status: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date: string | null\n          actual_start_date: string | null\n          planned_end_date: string | null\n          actual_end_date: string | null\n          production_cost: number | null\n          yield_percentage: number | null\n          quality_approved: boolean | null\n          notes: string | null\n          created_by: string | null\n          assigned_to: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          batch_number: string\n          recipe_id: string\n          batch_type: 'test' | 'production'\n          planned_quantity: number\n          actual_quantity?: number | null\n          unit_of_measure: string\n          status?: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority?: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date?: string | null\n          actual_start_date?: string | null\n          planned_end_date?: string | null\n          actual_end_date?: string | null\n          production_cost?: number | null\n          yield_percentage?: number | null\n          quality_approved?: boolean | null\n          notes?: string | null\n          created_by?: string | null\n          assigned_to?: string | null\n        }\n        Update: {\n          batch_number?: string\n          recipe_id?: string\n          batch_type?: 'test' | 'production'\n          planned_quantity?: number\n          actual_quantity?: number | null\n          unit_of_measure?: string\n          status?: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n          priority?: 'low' | 'normal' | 'high' | 'urgent'\n          planned_start_date?: string | null\n          actual_start_date?: string | null\n          planned_end_date?: string | null\n          actual_end_date?: string | null\n          production_cost?: number | null\n          yield_percentage?: number | null\n          quality_approved?: boolean | null\n          notes?: string | null\n          assigned_to?: string | null\n        }\n      }\n    }\n    Views: {\n      [_ in never]: never\n    }\n    Functions: {\n      check_recipe_availability: {\n        Args: {\n          recipe_uuid: string\n          batch_size_multiplier?: number\n        }\n        Returns: {\n          item_id: string\n          item_name: string\n          required_quantity: number\n          available_quantity: number\n          is_sufficient: boolean\n        }[]\n      }\n      calculate_recipe_cost: {\n        Args: {\n          recipe_uuid: string\n          batch_size_multiplier?: number\n        }\n        Returns: number\n      }\n    }\n    Enums: {\n      user_role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAGoB;AAHpB;AACA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,uBAAuB,IAAM,CAAA,GAAA,2KAAA,CAAA,8BAA2B,AAAD;AAG7D,MAAM,4BAA4B;IACvC,MAAM,iBAAiB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB;IAC5D,OAAO,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa,gBAAgB;QAC/C,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 973, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/lib/database.ts"], "sourcesContent": ["import { createSupabaseClient } from './supabase'\n\nconst supabase = createSupabaseClient()\n\n// Inventory Items\nexport interface InventoryItem {\n  id: string\n  name: string\n  sku: string\n  category_id: string\n  current_stock: number\n  minimum_stock: number\n  unit_of_measure: string\n  unit_cost: number\n  supplier?: string\n  description?: string\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface InventoryCategory {\n  id: string\n  name: string\n  description?: string\n  is_active: boolean\n}\n\n// Recipes\nexport interface Recipe {\n  id: string\n  name: string\n  code: string\n  category: string\n  batch_size: number\n  unit_of_measure: string\n  instructions?: string\n  is_active: boolean\n  version: number\n  created_at: string\n  updated_at: string\n}\n\n// Production Batches\nexport interface ProductionBatch {\n  id: string\n  batch_number: string\n  recipe_id: string\n  batch_type: 'test' | 'production'\n  planned_quantity: number\n  actual_quantity?: number\n  unit_of_measure: string\n  status: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'\n  priority: 'low' | 'normal' | 'high' | 'urgent'\n  planned_start_date?: string\n  planned_end_date?: string\n  actual_start_date?: string\n  actual_end_date?: string\n  assigned_to?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\n// Quality Documents\nexport interface QualityDocument {\n  id: string\n  document_type: 'msds' | 'coa' | 'tds' | 'quality_spec' | 'lab_report'\n  title: string\n  document_number: string\n  version: string\n  item_id?: string\n  batch_id?: string\n  status: 'draft' | 'review' | 'approved' | 'expired'\n  valid_from?: string\n  valid_until?: string\n  file_path?: string\n  created_by: string\n  created_at: string\n  updated_at: string\n}\n\n// Database service functions\nexport class DatabaseService {\n  // Inventory Items\n  static async getInventoryItems(): Promise<InventoryItem[]> {\n    try {\n      const { data, error } = await supabase\n        .from('inventory_items')\n        .select(`\n          *,\n          inventory_categories(name)\n        `)\n        .order('name')\n\n      if (error) {\n        console.error('Error fetching inventory items:', error)\n        // Only return demo data if it's a connection error\n        if (error.message.includes('Failed to fetch') || error.message.includes('network')) {\n          return this.getDemoInventoryItems()\n        }\n        return []\n      }\n\n      // Process the data to match the expected interface\n      const processedItems = (data || []).map(item => {\n        // Calculate status based on stock levels\n        const currentStock = parseFloat(item.current_stock?.toString() || '0')\n        const minimumStock = parseFloat(item.minimum_stock?.toString() || '0')\n\n        let status: 'in_stock' | 'low_stock' | 'out_of_stock' = 'in_stock'\n        if (currentStock === 0) {\n          status = 'out_of_stock'\n        } else if (currentStock <= minimumStock) {\n          status = 'low_stock'\n        }\n\n        return {\n          ...item,\n          current_stock: currentStock,\n          minimum_stock: minimumStock,\n          maximum_stock: parseFloat(item.maximum_stock?.toString() || '0'),\n          unit_cost: parseFloat(item.unit_cost?.toString() || '0'),\n          status,\n          category_name: item.inventory_categories?.name || 'Unknown',\n          is_active: item.is_active !== false // Default to true if null\n        }\n      })\n\n      return processedItems\n    } catch (error) {\n      console.error('Database connection error:', error)\n      return this.getDemoInventoryItems()\n    }\n  }\n\n  static getDemoInventoryItems(): InventoryItem[] {\n    return [\n      {\n        id: 'demo_item_1',\n        name: 'Vanilla Extract Premium',\n        sku: 'VAN-001',\n        category_id: 'demo_cat_1',\n        current_stock: 25.5,\n        minimum_stock: 10.0,\n        unit_of_measure: 'liters',\n        unit_cost: 12.50,\n        supplier: 'Premium Ingredients Co.',\n        description: 'High-quality vanilla extract for premium flavoring applications',\n        is_active: true,\n        created_at: '2024-01-15T08:00:00Z',\n        updated_at: '2024-01-15T08:00:00Z'\n      },\n      {\n        id: 'demo_item_2',\n        name: 'Strawberry Concentrate',\n        sku: 'STR-002',\n        category_id: 'demo_cat_1',\n        current_stock: 8.2,\n        minimum_stock: 15.0,\n        unit_of_measure: 'liters',\n        unit_cost: 18.75,\n        supplier: 'Fruit Essences Ltd.',\n        description: 'Natural strawberry concentrate for beverage and dessert applications',\n        is_active: true,\n        created_at: '2024-01-20T10:30:00Z',\n        updated_at: '2024-01-20T10:30:00Z'\n      },\n      {\n        id: 'demo_item_3',\n        name: 'Citric Acid Food Grade',\n        sku: 'CIT-003',\n        category_id: 'demo_cat_2',\n        current_stock: 45.0,\n        minimum_stock: 20.0,\n        unit_of_measure: 'kg',\n        unit_cost: 3.25,\n        supplier: 'Chemical Solutions Inc.',\n        description: 'Food-grade citric acid for pH adjustment and preservation',\n        is_active: true,\n        created_at: '2024-02-01T14:15:00Z',\n        updated_at: '2024-02-01T14:15:00Z'\n      },\n      {\n        id: 'demo_item_4',\n        name: 'Glass Bottles 500ml',\n        sku: 'BTL-004',\n        category_id: 'demo_cat_3',\n        current_stock: 0,\n        minimum_stock: 100,\n        unit_of_measure: 'pieces',\n        unit_cost: 0.85,\n        supplier: 'Packaging Solutions Ltd.',\n        description: 'Clear glass bottles with screw caps for beverage packaging',\n        is_active: true,\n        created_at: '2024-02-10T09:45:00Z',\n        updated_at: '2024-02-10T09:45:00Z'\n      },\n      {\n        id: 'demo_item_5',\n        name: 'Natural Lemon Oil',\n        sku: 'LEM-005',\n        category_id: 'demo_cat_1',\n        current_stock: 3.8,\n        minimum_stock: 5.0,\n        unit_of_measure: 'liters',\n        unit_cost: 45.00,\n        supplier: 'Essential Oils Direct',\n        description: 'Cold-pressed natural lemon oil for citrus flavoring',\n        is_active: true,\n        created_at: '2024-02-15T11:20:00Z',\n        updated_at: '2024-02-15T11:20:00Z'\n      }\n    ]\n  }\n\n  static async createInventoryItem(item: Omit<InventoryItem, 'id' | 'created_at' | 'updated_at'>): Promise<InventoryItem | null> {\n    try {\n      // Map the item data to match the database schema\n      const dbItem = {\n        name: item.name,\n        sku: item.sku,\n        category_id: item.category_id,\n        description: item.description || null,\n        unit_of_measure: item.unit_of_measure,\n        current_stock: item.current_stock || 0,\n        minimum_stock: item.minimum_stock || 0,\n        maximum_stock: item.maximum_stock || null,\n        unit_cost: item.unit_cost || 0,\n        supplier_info: item.supplier_name ? {\n          name: item.supplier_name,\n          code: item.supplier_code,\n          batch_number: item.batch_number\n        } : null,\n        storage_conditions: item.requires_refrigeration ? 'Refrigerated' : null,\n        expiry_date: item.expiry_date || null,\n        batch_number: item.batch_number || null,\n        is_active: true\n      }\n\n      const { data, error } = await supabase\n        .from('inventory_items')\n        .insert([dbItem])\n        .select(`\n          *,\n          inventory_categories(name)\n        `)\n        .single()\n\n      if (error) {\n        console.error('Error creating inventory item:', error)\n        // Only use demo fallback for connection errors\n        if (error.message.includes('Failed to fetch') || error.message.includes('network')) {\n          return this.createDemoInventoryItem(item)\n        }\n        return null\n      }\n\n      // Process the returned data to match the expected interface\n      if (data) {\n        const processedItem = {\n          ...data,\n          current_stock: parseFloat(data.current_stock?.toString() || '0'),\n          minimum_stock: parseFloat(data.minimum_stock?.toString() || '0'),\n          maximum_stock: parseFloat(data.maximum_stock?.toString() || '0'),\n          unit_cost: parseFloat(data.unit_cost?.toString() || '0'),\n          category_name: data.inventory_categories?.name || 'Unknown',\n          status: 'in_stock' as const\n        }\n        return processedItem\n      }\n\n      return data\n    } catch (error) {\n      console.error('Database connection error:', error)\n      return this.createDemoInventoryItem(item)\n    }\n  }\n\n  static createDemoInventoryItem(item: Omit<InventoryItem, 'id' | 'created_at' | 'updated_at'>): InventoryItem {\n    return {\n      ...item,\n      id: `demo_item_${Date.now()}`,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }\n  }\n\n  static async updateInventoryItem(id: string, updates: Partial<InventoryItem>): Promise<InventoryItem | null> {\n    const { data, error } = await supabase\n      .from('inventory_items')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error updating inventory item:', error)\n      return null\n    }\n\n    return data\n  }\n\n  // Inventory Categories\n  static async getInventoryCategories(): Promise<InventoryCategory[]> {\n    try {\n      const { data, error } = await supabase\n        .from('inventory_categories')\n        .select('*')\n        .order('name')\n\n      if (error) {\n        console.error('Error fetching inventory categories:', error)\n        // Only return demo data if it's a connection error\n        if (error.message.includes('Failed to fetch') || error.message.includes('network')) {\n          return this.getDemoInventoryCategories()\n        }\n        return []\n      }\n\n      // Return actual data from database (even if empty)\n      // Add is_active: true to match interface since DB doesn't have this column\n      const categoriesWithActive = (data || []).map(cat => ({\n        ...cat,\n        is_active: true\n      }))\n\n      return categoriesWithActive\n    } catch (error) {\n      console.error('Database connection error:', error)\n      return this.getDemoInventoryCategories()\n    }\n  }\n\n  static getDemoInventoryCategories(): InventoryCategory[] {\n    return [\n      {\n        id: 'demo_cat_1',\n        name: 'Flavoring Agents',\n        description: 'Natural and artificial flavoring compounds and extracts',\n        is_active: true\n      },\n      {\n        id: 'demo_cat_2',\n        name: 'Preservatives & Additives',\n        description: 'Food-grade preservatives, stabilizers, and additives',\n        is_active: true\n      },\n      {\n        id: 'demo_cat_3',\n        name: 'Packaging Materials',\n        description: 'Bottles, caps, labels, and packaging supplies',\n        is_active: true\n      },\n      {\n        id: 'demo_cat_4',\n        name: 'Raw Materials',\n        description: 'Base ingredients and raw materials for production',\n        is_active: true\n      },\n      {\n        id: 'demo_cat_5',\n        name: 'Quality Control',\n        description: 'Testing materials and quality control supplies',\n        is_active: true\n      }\n    ]\n  }\n\n  // Recipes\n  static async getRecipes(): Promise<Recipe[]> {\n    const { data, error } = await supabase\n      .from('recipes')\n      .select('*')\n      .eq('is_active', true)\n      .order('name')\n\n    if (error) {\n      console.error('Error fetching recipes:', error)\n      return []\n    }\n\n    return data || []\n  }\n\n  static async createRecipe(recipe: Omit<Recipe, 'id' | 'created_at' | 'updated_at'>): Promise<Recipe | null> {\n    const { data, error } = await supabase\n      .from('recipes')\n      .insert([recipe])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error creating recipe:', error)\n      return null\n    }\n\n    return data\n  }\n\n  // Production Batches\n  static async getProductionBatches(): Promise<ProductionBatch[]> {\n    const { data, error } = await supabase\n      .from('production_batches')\n      .select(`\n        *,\n        recipes(name, code)\n      `)\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      console.error('Error fetching production batches:', error)\n      return []\n    }\n\n    return data || []\n  }\n\n  static async createProductionBatch(batch: Omit<ProductionBatch, 'id' | 'created_at' | 'updated_at'>): Promise<ProductionBatch | null> {\n    const { data, error } = await supabase\n      .from('production_batches')\n      .insert([batch])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error creating production batch:', error)\n      return null\n    }\n\n    return data\n  }\n\n  // Quality Documents\n  static async getQualityDocuments(): Promise<QualityDocument[]> {\n    const { data, error } = await supabase\n      .from('quality_documents')\n      .select('*')\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      console.error('Error fetching quality documents:', error)\n      return []\n    }\n\n    return data || []\n  }\n\n  static async createQualityDocument(doc: Omit<QualityDocument, 'id' | 'created_at' | 'updated_at'>): Promise<QualityDocument | null> {\n    try {\n      const { data, error } = await supabase\n        .from('quality_documents')\n        .insert([doc])\n        .select()\n        .single()\n\n      if (error) {\n        console.error('Error creating quality document:', error)\n        // Only use demo fallback for connection errors\n        if (error.message.includes('Failed to fetch') || error.message.includes('network')) {\n          return {\n            ...doc,\n            id: `demo_doc_${Date.now()}`,\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n          }\n        }\n        return null\n      }\n\n      return data\n    } catch (error) {\n      console.error('Database connection error:', error)\n      return {\n        ...doc,\n        id: `demo_doc_${Date.now()}`,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }\n    }\n  }\n\n  // User Management\n  static async getUsers(): Promise<any[]> {\n    try {\n      const { data, error } = await supabase\n        .from('profiles')\n        .select('*')\n        .order('created_at', { ascending: false })\n\n      if (error) {\n        // Silently handle database errors and return demo users\n        // This is expected when the database is empty or has constraints\n        return this.getDemoUsers()\n      }\n\n      // If no users in database, return demo users\n      if (!data || data.length === 0) {\n        return this.getDemoUsers()\n      }\n\n      return data\n    } catch (error) {\n      // Silently handle connection errors and return demo users\n      return this.getDemoUsers()\n    }\n  }\n\n  static getDemoUsers(): any[] {\n    return [\n      {\n        id: 'demo_admin',\n        username: 'admin',\n        full_name: 'System Administrator',\n        email: '<EMAIL>',\n        role: 'admin',\n        department: 'IT',\n        phone: '+****************',\n        is_active: true,\n        created_at: '2024-01-15T08:00:00Z',\n        updated_at: '2024-01-15T08:00:00Z'\n      },\n      {\n        id: 'demo_quality',\n        username: 'quality',\n        full_name: 'Sarah Johnson',\n        email: '<EMAIL>',\n        role: 'quality_manager',\n        department: 'Quality Assurance',\n        phone: '+****************',\n        is_active: true,\n        created_at: '2024-02-01T10:00:00Z',\n        updated_at: '2024-02-01T10:00:00Z'\n      },\n      {\n        id: 'demo_production',\n        username: 'production',\n        full_name: 'Mike Wilson',\n        email: '<EMAIL>',\n        role: 'production_manager',\n        department: 'Production',\n        phone: '+****************',\n        is_active: true,\n        created_at: '2024-02-15T14:30:00Z',\n        updated_at: '2024-02-15T14:30:00Z'\n      },\n      {\n        id: 'demo_employee',\n        username: 'employee',\n        full_name: 'Emily Davis',\n        email: '<EMAIL>',\n        role: 'employee',\n        department: 'Production',\n        phone: '+****************',\n        is_active: true,\n        created_at: '2024-03-01T09:00:00Z',\n        updated_at: '2024-03-01T09:00:00Z'\n      }\n    ]\n  }\n\n  static async createUser(userData: {\n    username: string\n    email: string\n    password_hash: string\n    role: string\n    full_name: string\n    department: string\n    phone?: string\n    is_active: boolean\n  }): Promise<any | null> {\n    try {\n      // For demo purposes, since we can't create auth users directly,\n      // we'll simulate user creation and return a demo user object\n      const newUser = {\n        id: `demo_${Date.now()}`,\n        username: userData.username,\n        email: userData.email,\n        full_name: userData.full_name,\n        role: userData.role,\n        department: userData.department,\n        phone: userData.phone || null,\n        is_active: userData.is_active,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }\n\n      // In a real implementation, this would create the user in the database\n      // For now, we'll just return the user object for demo purposes\n      return newUser\n\n      // Commented out actual database insertion due to auth constraints\n      /*\n      const { data, error } = await supabase\n        .from('profiles')\n        .insert([{\n          ...userData,\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        }])\n        .select()\n        .single()\n\n      if (error) {\n        console.error('Error creating user:', error)\n        return null\n      }\n\n      return data\n      */\n    } catch (error) {\n      return null\n    }\n  }\n\n  static async updateUser(id: string, updates: any): Promise<any | null> {\n    const { data, error } = await supabase\n      .from('profiles')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error updating user:', error)\n      return null\n    }\n\n    return data\n  }\n\n  static async toggleUserStatus(id: string, isActive: boolean): Promise<boolean> {\n    try {\n      // For demo users (those with demo_ prefix), just return success\n      if (id.startsWith('demo_')) {\n        return true\n      }\n\n      const { error } = await supabase\n        .from('profiles')\n        .update({ is_active: isActive, updated_at: new Date().toISOString() })\n        .eq('id', id)\n\n      if (error) {\n        return false\n      }\n\n      return true\n    } catch (error) {\n      return false\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;AAiF7B,MAAM;IACX,kBAAkB;IAClB,aAAa,oBAA8C;QACzD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,mBACL,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,KAAK,CAAC;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,mDAAmD;gBACnD,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,sBAAsB,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;oBAClF,OAAO,IAAI,CAAC,qBAAqB;gBACnC;gBACA,OAAO,EAAE;YACX;YAEA,mDAAmD;YACnD,MAAM,iBAAiB,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAA;gBACtC,yCAAyC;gBACzC,MAAM,eAAe,WAAW,KAAK,aAAa,EAAE,cAAc;gBAClE,MAAM,eAAe,WAAW,KAAK,aAAa,EAAE,cAAc;gBAElE,IAAI,SAAoD;gBACxD,IAAI,iBAAiB,GAAG;oBACtB,SAAS;gBACX,OAAO,IAAI,gBAAgB,cAAc;oBACvC,SAAS;gBACX;gBAEA,OAAO;oBACL,GAAG,IAAI;oBACP,eAAe;oBACf,eAAe;oBACf,eAAe,WAAW,KAAK,aAAa,EAAE,cAAc;oBAC5D,WAAW,WAAW,KAAK,SAAS,EAAE,cAAc;oBACpD;oBACA,eAAe,KAAK,oBAAoB,EAAE,QAAQ;oBAClD,WAAW,KAAK,SAAS,KAAK,MAAM,0BAA0B;gBAChE;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,IAAI,CAAC,qBAAqB;QACnC;IACF;IAEA,OAAO,wBAAyC;QAC9C,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;SACD;IACH;IAEA,aAAa,oBAAoB,IAA6D,EAAiC;QAC7H,IAAI;YACF,iDAAiD;YACjD,MAAM,SAAS;gBACb,MAAM,KAAK,IAAI;gBACf,KAAK,KAAK,GAAG;gBACb,aAAa,KAAK,WAAW;gBAC7B,aAAa,KAAK,WAAW,IAAI;gBACjC,iBAAiB,KAAK,eAAe;gBACrC,eAAe,KAAK,aAAa,IAAI;gBACrC,eAAe,KAAK,aAAa,IAAI;gBACrC,eAAe,KAAK,aAAa,IAAI;gBACrC,WAAW,KAAK,SAAS,IAAI;gBAC7B,eAAe,KAAK,aAAa,GAAG;oBAClC,MAAM,KAAK,aAAa;oBACxB,MAAM,KAAK,aAAa;oBACxB,cAAc,KAAK,YAAY;gBACjC,IAAI;gBACJ,oBAAoB,KAAK,sBAAsB,GAAG,iBAAiB;gBACnE,aAAa,KAAK,WAAW,IAAI;gBACjC,cAAc,KAAK,YAAY,IAAI;gBACnC,WAAW;YACb;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,mBACL,MAAM,CAAC;gBAAC;aAAO,EACf,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,+CAA+C;gBAC/C,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,sBAAsB,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;oBAClF,OAAO,IAAI,CAAC,uBAAuB,CAAC;gBACtC;gBACA,OAAO;YACT;YAEA,4DAA4D;YAC5D,IAAI,MAAM;gBACR,MAAM,gBAAgB;oBACpB,GAAG,IAAI;oBACP,eAAe,WAAW,KAAK,aAAa,EAAE,cAAc;oBAC5D,eAAe,WAAW,KAAK,aAAa,EAAE,cAAc;oBAC5D,eAAe,WAAW,KAAK,aAAa,EAAE,cAAc;oBAC5D,WAAW,WAAW,KAAK,SAAS,EAAE,cAAc;oBACpD,eAAe,KAAK,oBAAoB,EAAE,QAAQ;oBAClD,QAAQ;gBACV;gBACA,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,IAAI,CAAC,uBAAuB,CAAC;QACtC;IACF;IAEA,OAAO,wBAAwB,IAA6D,EAAiB;QAC3G,OAAO;YACL,GAAG,IAAI;YACP,IAAI,CAAC,UAAU,EAAE,KAAK,GAAG,IAAI;YAC7B,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;IACF;IAEA,aAAa,oBAAoB,EAAU,EAAE,OAA+B,EAAiC;QAC3G,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,mBACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,uBAAuB;IACvB,aAAa,yBAAuD;QAClE,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,wBACL,MAAM,CAAC,KACP,KAAK,CAAC;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,mDAAmD;gBACnD,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,sBAAsB,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;oBAClF,OAAO,IAAI,CAAC,0BAA0B;gBACxC;gBACA,OAAO,EAAE;YACX;YAEA,mDAAmD;YACnD,2EAA2E;YAC3E,MAAM,uBAAuB,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAA,MAAO,CAAC;oBACpD,GAAG,GAAG;oBACN,WAAW;gBACb,CAAC;YAED,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,IAAI,CAAC,0BAA0B;QACxC;IACF;IAEA,OAAO,6BAAkD;QACvD,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;SACD;IACH;IAEA,UAAU;IACV,aAAa,aAAgC;QAC3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,EAAE;QACX;QAEA,OAAO,QAAQ,EAAE;IACnB;IAEA,aAAa,aAAa,MAAwD,EAA0B;QAC1G,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,WACL,MAAM,CAAC;YAAC;SAAO,EACf,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,qBAAqB;IACrB,aAAa,uBAAmD;QAC9D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,sBACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO,EAAE;QACX;QAEA,OAAO,QAAQ,EAAE;IACnB;IAEA,aAAa,sBAAsB,KAAgE,EAAmC;QACpI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,sBACL,MAAM,CAAC;YAAC;SAAM,EACd,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,oBAAoB;IACpB,aAAa,sBAAkD;QAC7D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO,EAAE;QACX;QAEA,OAAO,QAAQ,EAAE;IACnB;IAEA,aAAa,sBAAsB,GAA8D,EAAmC;QAClI,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC;gBAAC;aAAI,EACZ,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,+CAA+C;gBAC/C,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,sBAAsB,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;oBAClF,OAAO;wBACL,GAAG,GAAG;wBACN,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;wBAC5B,YAAY,IAAI,OAAO,WAAW;wBAClC,YAAY,IAAI,OAAO,WAAW;oBACpC;gBACF;gBACA,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;gBACL,GAAG,GAAG;gBACN,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;gBAC5B,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;QACF;IACF;IAEA,kBAAkB;IAClB,aAAa,WAA2B;QACtC,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,wDAAwD;gBACxD,iEAAiE;gBACjE,OAAO,IAAI,CAAC,YAAY;YAC1B;YAEA,6CAA6C;YAC7C,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;gBAC9B,OAAO,IAAI,CAAC,YAAY;YAC1B;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,0DAA0D;YAC1D,OAAO,IAAI,CAAC,YAAY;QAC1B;IACF;IAEA,OAAO,eAAsB;QAC3B,OAAO;YACL;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;SACD;IACH;IAEA,aAAa,WAAW,QASvB,EAAuB;QACtB,IAAI;YACF,gEAAgE;YAChE,6DAA6D;YAC7D,MAAM,UAAU;gBACd,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;gBACxB,UAAU,SAAS,QAAQ;gBAC3B,OAAO,SAAS,KAAK;gBACrB,WAAW,SAAS,SAAS;gBAC7B,MAAM,SAAS,IAAI;gBACnB,YAAY,SAAS,UAAU;gBAC/B,OAAO,SAAS,KAAK,IAAI;gBACzB,WAAW,SAAS,SAAS;gBAC7B,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,uEAAuE;YACvE,+DAA+D;YAC/D,OAAO;QAEP,kEAAkE;QAClE;;;;;;;;;;;;;;;;;MAiBA,GACF,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,aAAa,WAAW,EAAU,EAAE,OAAY,EAAuB;QACrE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,aAAa,iBAAiB,EAAU,EAAE,QAAiB,EAAoB;QAC7E,IAAI;YACF,gEAAgE;YAChE,IAAI,GAAG,UAAU,CAAC,UAAU;gBAC1B,OAAO;YACT;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,YACL,MAAM,CAAC;gBAAE,WAAW;gBAAU,YAAY,IAAI,OAAO,WAAW;YAAG,GACnE,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO;gBACT,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 1471, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/app/users/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { useAuth } from '@/components/providers'\nimport { Modal } from '@/components/ui/modal'\nimport { DatabaseService } from '@/lib/database'\nimport {\n  PlusIcon,\n  UsersIcon,\n  ShieldCheckIcon,\n  MagnifyingGlassIcon,\n  EyeIcon,\n  EyeSlashIcon,\n  ExclamationTriangleIcon,\n  CheckCircleIcon\n} from '@heroicons/react/24/outline'\n\ninterface User {\n  id: string\n  username: string\n  full_name: string\n  email: string\n  role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'\n  department: string\n  phone?: string\n  is_active: boolean\n  last_login?: string\n  created_at: string\n  updated_at?: string\n}\n\nexport default function UsersPage() {\n  const { user } = useAuth()\n  const [users, setUsers] = useState<User[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedRole, setSelectedRole] = useState<'all' | 'admin' | 'quality_manager' | 'production_manager' | 'employee'>('all')\n  const [showAddModal, setShowAddModal] = useState(false)\n  const [showPassword, setShowPassword] = useState(false)\n  const [validationErrors, setValidationErrors] = useState<string[]>([])\n  const [isCreating, setIsCreating] = useState(false)\n  const [newUser, setNewUser] = useState({\n    username: '',\n    full_name: '',\n    email: '',\n    password: '',\n    confirm_password: '',\n    role: 'employee' as 'admin' | 'quality_manager' | 'production_manager' | 'employee',\n    department: '',\n    phone: '',\n    employee_id: '',\n    start_date: '',\n    notes: ''\n  })\n\n  useEffect(() => {\n    const loadUsers = async () => {\n      try {\n        setLoading(true)\n        const userData = await DatabaseService.getUsers()\n\n        // Process user data\n        const processedUsers = userData.map(user => ({\n          ...user,\n          created_at: user.created_at || new Date().toISOString(),\n          updated_at: user.updated_at || user.created_at || new Date().toISOString()\n        }))\n\n        setUsers(processedUsers)\n      } catch (error) {\n        // DatabaseService handles errors gracefully and returns demo users\n        // No need to log errors here as they're expected in demo mode\n        setUsers([])\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    loadUsers()\n  }, [])\n\n  const validateUser = () => {\n    const errors: string[] = []\n\n    // Required field validation\n    if (!newUser.username.trim()) errors.push('Username is required')\n    if (!newUser.full_name.trim()) errors.push('Full name is required')\n    if (!newUser.email.trim()) errors.push('Email is required')\n    if (!newUser.password) errors.push('Password is required')\n    if (!newUser.confirm_password) errors.push('Password confirmation is required')\n    if (!newUser.department.trim()) errors.push('Department is required')\n\n    // Username validation\n    if (newUser.username.length < 3) errors.push('Username must be at least 3 characters')\n    if (!/^[a-zA-Z0-9_]+$/.test(newUser.username)) errors.push('Username can only contain letters, numbers, and underscores')\n\n    // Check if username already exists\n    if (users.some(user => user.username.toLowerCase() === newUser.username.toLowerCase())) {\n      errors.push('Username already exists')\n    }\n\n    // Email validation\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n    if (newUser.email && !emailRegex.test(newUser.email)) errors.push('Invalid email format')\n\n    // Check if email already exists\n    if (users.some(user => user.email.toLowerCase() === newUser.email.toLowerCase())) {\n      errors.push('Email already exists')\n    }\n\n    // Password validation\n    if (newUser.password.length < 6) errors.push('Password must be at least 6 characters')\n    if (newUser.password !== newUser.confirm_password) errors.push('Passwords do not match')\n\n    // Phone validation (if provided)\n    if (newUser.phone && !/^[\\+]?[1-9][\\d]{0,15}$/.test(newUser.phone.replace(/[\\s\\-\\(\\)]/g, ''))) {\n      errors.push('Invalid phone number format')\n    }\n\n    setValidationErrors(errors)\n    return errors.length === 0\n  }\n\n  const generateEmployeeId = () => {\n    const year = new Date().getFullYear()\n    const sequence = String(users.length + 1).padStart(4, '0')\n    return `SFF${year}${sequence}`\n  }\n\n  const handleAddUser = async () => {\n    if (!validateUser()) return\n\n    try {\n      setIsCreating(true)\n\n      const userData = {\n        username: newUser.username.toLowerCase(),\n        email: newUser.email.toLowerCase(),\n        password_hash: newUser.password, // In production, this should be properly hashed\n        role: newUser.role,\n        full_name: newUser.full_name,\n        department: newUser.department,\n        phone: newUser.phone || null,\n        is_active: true\n      }\n\n      const createdUser = await DatabaseService.createUser(userData)\n\n      if (createdUser) {\n        // Add the new user to the local state\n        const newUserData: User = {\n          ...createdUser,\n          created_at: createdUser.created_at || new Date().toISOString()\n        }\n\n        setUsers([...users, newUserData])\n        setShowAddModal(false)\n        resetForm()\n\n        alert(`✅ User \"${newUser.full_name}\" created successfully!\\n\\n📋 Account Details:\\n• Username: ${newUser.username}\\n• Email: ${newUser.email}\\n• Role: ${newUser.role}\\n• Department: ${newUser.department}\\n• Employee ID: ${generateEmployeeId()}\\n\\n🔐 Login Credentials:\\n• Username: ${newUser.username}\\n• Password: ${newUser.password}\\n\\n⚠️ Note: This is a demo system. In production, passwords would be securely hashed.`)\n      } else {\n        alert('❌ Failed to create user. Please check the form and try again.')\n      }\n    } catch (error) {\n      alert('❌ Error creating user. Please check the form and try again.')\n    } finally {\n      setIsCreating(false)\n    }\n  }\n\n  const resetForm = () => {\n    setNewUser({\n      username: '',\n      full_name: '',\n      email: '',\n      password: '',\n      confirm_password: '',\n      role: 'employee',\n      department: '',\n      phone: '',\n      employee_id: '',\n      start_date: '',\n      notes: ''\n    })\n    setValidationErrors([])\n    setShowPassword(false)\n  }\n\n  const toggleUserStatus = async (userId: string, currentStatus: boolean) => {\n    try {\n      const success = await DatabaseService.toggleUserStatus(userId, !currentStatus)\n\n      if (success) {\n        setUsers(users.map(user =>\n          user.id === userId\n            ? { ...user, is_active: !currentStatus }\n            : user\n        ))\n      } else {\n        alert('❌ Failed to update user status')\n      }\n    } catch (error) {\n      alert('❌ Error updating user status')\n    }\n  }\n\n  const roles = [\n    { key: 'all', name: 'All Users', count: users.length },\n    { key: 'admin', name: 'Administrators', count: users.filter(u => u.role === 'admin').length },\n    { key: 'quality_manager', name: 'Quality Managers', count: users.filter(u => u.role === 'quality_manager').length },\n    { key: 'production_manager', name: 'Production Managers', count: users.filter(u => u.role === 'production_manager').length },\n    { key: 'employee', name: 'Employees', count: users.filter(u => u.role === 'employee').length }\n  ]\n\n  const filteredUsers = users.filter(user => {\n    const matchesSearch = user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         user.department.toLowerCase().includes(searchTerm.toLowerCase())\n    \n    const matchesRole = selectedRole === 'all' || user.role === selectedRole\n    \n    return matchesSearch && matchesRole\n  })\n\n  const getRoleColor = (role: string) => {\n    switch (role) {\n      case 'admin':\n        return 'bg-red-100 text-red-800'\n      case 'quality_manager':\n        return 'bg-purple-100 text-purple-800'\n      case 'production_manager':\n        return 'bg-blue-100 text-blue-800'\n      case 'employee':\n        return 'bg-green-100 text-green-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getRoleName = (role: string) => {\n    switch (role) {\n      case 'admin':\n        return 'Administrator'\n      case 'quality_manager':\n        return 'Quality Manager'\n      case 'production_manager':\n        return 'Production Manager'\n      case 'employee':\n        return 'Employee'\n      default:\n        return role\n    }\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    })\n  }\n\n  // Check if current user is admin\n  const isAdmin = user?.role === 'admin'\n\n  if (!user) {\n    return null\n  }\n\n  if (!isAdmin) {\n    return (\n      <MainLayout>\n        <div className=\"text-center py-12\">\n          <ShieldCheckIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900\">Access Denied</h3>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            You don't have permission to access user management.\n          </p>\n        </div>\n      </MainLayout>\n    )\n  }\n\n  return (\n    <MainLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"sm:flex sm:items-center sm:justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">User Management</h1>\n            <p className=\"mt-2 text-sm text-gray-700\">\n              Manage user accounts, roles, and permissions with real database integration\n            </p>\n          </div>\n          <div className=\"mt-4 sm:mt-0\">\n            <button\n              type=\"button\"\n              onClick={() => setShowAddModal(true)}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n            >\n              <PlusIcon className=\"-ml-1 mr-2 h-5 w-5\" aria-hidden=\"true\" />\n              Add User\n            </button>\n          </div>\n        </div>\n\n        {/* Demo System Notice */}\n        <div className=\"bg-blue-50 border border-blue-200 rounded-md p-4\">\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0\">\n              <CheckCircleIcon className=\"h-5 w-5 text-blue-400\" />\n            </div>\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-blue-800\">Professional User Management System</h3>\n              <div className=\"mt-2 text-sm text-blue-700\">\n                <p>\n                  ✅ <strong>Complete user creation</strong> with password management and validation<br/>\n                  ✅ <strong>Real database integration</strong> with Supabase profiles table<br/>\n                  ✅ <strong>Role-based access control</strong> and department organization<br/>\n                  ✅ <strong>Professional form validation</strong> with comprehensive error handling<br/>\n                  ✅ <strong>User status management</strong> (activate/deactivate functionality)\n                </p>\n                <p className=\"mt-2 font-medium\">\n                  🔐 <strong>Demo Mode:</strong> New users are created in demo mode for testing. In production, this would integrate with Supabase Auth for secure user management.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-4\">\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <UsersIcon className=\"h-6 w-6 text-blue-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Users</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">{users.length}</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"h-6 w-6 bg-green-100 rounded-full flex items-center justify-center\">\n                    <div className=\"h-2 w-2 bg-green-600 rounded-full\"></div>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Active Users</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {users.filter(u => u.is_active).length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <ShieldCheckIcon className=\"h-6 w-6 text-red-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Administrators</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {users.filter(u => u.role === 'admin').length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"h-6 w-6 bg-blue-100 rounded-full flex items-center justify-center\">\n                    <span className=\"text-xs font-medium text-blue-600\">M</span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Managers</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {users.filter(u => u.role.includes('manager')).length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Search and Filters */}\n        <div className=\"bg-white shadow rounded-lg p-6\">\n          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\n            <div className=\"relative flex-1 max-w-lg\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\n              </div>\n              <input\n                type=\"text\"\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-green-500 focus:border-green-500\"\n                placeholder=\"Search users...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n              />\n            </div>\n            \n            <div className=\"flex space-x-2\">\n              {roles.map((role) => (\n                <button\n                  key={role.key}\n                  onClick={() => setSelectedRole(role.key as any)}\n                  className={`px-3 py-2 rounded-md text-sm font-medium ${\n                    selectedRole === role.key\n                      ? 'bg-green-100 text-green-700'\n                      : 'text-gray-500 hover:text-gray-700'\n                  }`}\n                >\n                  {role.name} ({role.count})\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Users Table */}\n        <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n              Users ({filteredUsers.length})\n            </h3>\n            \n            {loading ? (\n              <div className=\"animate-pulse\">\n                {[...Array(6)].map((_, i) => (\n                  <div key={i} className=\"flex items-center space-x-4 py-4\">\n                    <div className=\"h-10 w-10 bg-gray-200 rounded-full\"></div>\n                    <div className=\"flex-1 space-y-2\">\n                      <div className=\"h-4 bg-gray-200 rounded w-1/4\"></div>\n                      <div className=\"h-3 bg-gray-200 rounded w-1/6\"></div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full divide-y divide-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        User\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Role\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Department\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Last Login\n                      </th>\n                      <th className=\"relative px-6 py-3\">\n                        <span className=\"sr-only\">Actions</span>\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {filteredUsers.map((user) => (\n                      <tr key={user.id} className=\"hover:bg-gray-50\">\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"flex items-center\">\n                            <div className=\"h-10 w-10 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center\">\n                              <span className=\"text-sm font-medium text-white\">\n                                {user.full_name.charAt(0).toUpperCase()}\n                              </span>\n                            </div>\n                            <div className=\"ml-4\">\n                              <div className=\"text-sm font-medium text-gray-900\">{user.full_name}</div>\n                              <div className=\"text-sm text-gray-500\">{user.email}</div>\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>\n                            {getRoleName(user.role)}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {user.department}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                            user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n                          }`}>\n                            {user.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                          {user.last_login ? formatDate(user.last_login) : 'Never'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                          <button\n                            className=\"text-green-600 hover:text-green-900 mr-3\"\n                            onClick={() => alert('Edit functionality coming soon!')}\n                          >\n                            Edit\n                          </button>\n                          <button\n                            className={`${user.is_active ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'}`}\n                            onClick={() => toggleUserStatus(user.id, user.is_active)}\n                          >\n                            {user.is_active ? 'Deactivate' : 'Activate'}\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Professional Add User Modal */}\n        <Modal\n          isOpen={showAddModal}\n          onClose={() => { resetForm(); setShowAddModal(false); }}\n          title=\"Create New User Account\"\n          maxWidth=\"2xl\"\n        >\n          <div className=\"space-y-6\">\n            {/* Validation Errors */}\n            {validationErrors.length > 0 && (\n              <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n                <div className=\"flex\">\n                  <ExclamationTriangleIcon className=\"h-5 w-5 text-red-400\" />\n                  <div className=\"ml-3\">\n                    <h3 className=\"text-sm font-medium text-red-800\">Please fix the following errors:</h3>\n                    <ul className=\"mt-2 text-sm text-red-700 list-disc list-inside\">\n                      {validationErrors.map((error, index) => (\n                        <li key={index}>{error}</li>\n                      ))}\n                    </ul>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <form onSubmit={(e) => { e.preventDefault(); handleAddUser(); }} className=\"space-y-6\">\n              {/* Basic Information */}\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Basic Information</h3>\n                <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                  <div>\n                    <label htmlFor=\"user_username\" className=\"block text-sm font-medium text-gray-700\">\n                      Username *\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"user_username\"\n                      required\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                      value={newUser.username}\n                      onChange={(e) => setNewUser({ ...newUser, username: e.target.value })}\n                      placeholder=\"e.g., jsmith\"\n                    />\n                    <p className=\"mt-1 text-xs text-gray-500\">3+ characters, letters, numbers, and underscores only</p>\n                  </div>\n                  <div>\n                    <label htmlFor=\"user_full_name\" className=\"block text-sm font-medium text-gray-700\">\n                      Full Name *\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"user_full_name\"\n                      required\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                      value={newUser.full_name}\n                      onChange={(e) => setNewUser({ ...newUser, full_name: e.target.value })}\n                      placeholder=\"e.g., John Smith\"\n                    />\n                  </div>\n                  <div>\n                    <label htmlFor=\"user_email\" className=\"block text-sm font-medium text-gray-700\">\n                      Email Address *\n                    </label>\n                    <input\n                      type=\"email\"\n                      id=\"user_email\"\n                      required\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                      value={newUser.email}\n                      onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}\n                      placeholder=\"e.g., <EMAIL>\"\n                    />\n                  </div>\n                  <div>\n                    <label htmlFor=\"user_phone\" className=\"block text-sm font-medium text-gray-700\">\n                      Phone Number\n                    </label>\n                    <input\n                      type=\"tel\"\n                      id=\"user_phone\"\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                      value={newUser.phone}\n                      onChange={(e) => setNewUser({ ...newUser, phone: e.target.value })}\n                      placeholder=\"e.g., +****************\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              {/* Security Information */}\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Security Information</h3>\n                <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                  <div>\n                    <label htmlFor=\"user_password\" className=\"block text-sm font-medium text-gray-700\">\n                      Password *\n                    </label>\n                    <div className=\"mt-1 relative\">\n                      <input\n                        type={showPassword ? \"text\" : \"password\"}\n                        id=\"user_password\"\n                        required\n                        className=\"block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 pr-10\"\n                        value={newUser.password}\n                        onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}\n                        placeholder=\"Minimum 6 characters\"\n                      />\n                      <button\n                        type=\"button\"\n                        className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                        onClick={() => setShowPassword(!showPassword)}\n                      >\n                        {showPassword ? (\n                          <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                        ) : (\n                          <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                        )}\n                      </button>\n                    </div>\n                    <p className=\"mt-1 text-xs text-gray-500\">Minimum 6 characters required</p>\n                  </div>\n                  <div>\n                    <label htmlFor=\"user_confirm_password\" className=\"block text-sm font-medium text-gray-700\">\n                      Confirm Password *\n                    </label>\n                    <input\n                      type={showPassword ? \"text\" : \"password\"}\n                      id=\"user_confirm_password\"\n                      required\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                      value={newUser.confirm_password}\n                      onChange={(e) => setNewUser({ ...newUser, confirm_password: e.target.value })}\n                      placeholder=\"Re-enter password\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              {/* Role and Department */}\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Role and Department</h3>\n                <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                  <div>\n                    <label htmlFor=\"user_role\" className=\"block text-sm font-medium text-gray-700\">\n                      Role *\n                    </label>\n                    <select\n                      id=\"user_role\"\n                      required\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                      value={newUser.role}\n                      onChange={(e) => setNewUser({ ...newUser, role: e.target.value as any })}\n                    >\n                      <option value=\"employee\">Employee</option>\n                      <option value=\"production_manager\">Production Manager</option>\n                      <option value=\"quality_manager\">Quality Manager</option>\n                      <option value=\"admin\">Administrator</option>\n                    </select>\n                  </div>\n                  <div>\n                    <label htmlFor=\"user_department\" className=\"block text-sm font-medium text-gray-700\">\n                      Department *\n                    </label>\n                    <select\n                      id=\"user_department\"\n                      required\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                      value={newUser.department}\n                      onChange={(e) => setNewUser({ ...newUser, department: e.target.value })}\n                    >\n                      <option value=\"\">Select Department</option>\n                      <option value=\"Production\">Production</option>\n                      <option value=\"Quality Assurance\">Quality Assurance</option>\n                      <option value=\"Research & Development\">Research & Development</option>\n                      <option value=\"IT\">Information Technology</option>\n                      <option value=\"Administration\">Administration</option>\n                      <option value=\"Sales & Marketing\">Sales & Marketing</option>\n                    </select>\n                  </div>\n                  <div>\n                    <label htmlFor=\"user_employee_id\" className=\"block text-sm font-medium text-gray-700\">\n                      Employee ID\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"user_employee_id\"\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                      value={newUser.employee_id}\n                      onChange={(e) => setNewUser({ ...newUser, employee_id: e.target.value })}\n                      placeholder=\"Auto-generated if empty\"\n                    />\n                  </div>\n                  <div>\n                    <label htmlFor=\"user_start_date\" className=\"block text-sm font-medium text-gray-700\">\n                      Start Date\n                    </label>\n                    <input\n                      type=\"date\"\n                      id=\"user_start_date\"\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                      value={newUser.start_date}\n                      onChange={(e) => setNewUser({ ...newUser, start_date: e.target.value })}\n                    />\n                  </div>\n                </div>\n              </div>\n\n              {/* Additional Notes */}\n              <div>\n                <label htmlFor=\"user_notes\" className=\"block text-sm font-medium text-gray-700\">\n                  Additional Notes\n                </label>\n                <textarea\n                  id=\"user_notes\"\n                  rows={3}\n                  className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500\"\n                  value={newUser.notes}\n                  onChange={(e) => setNewUser({ ...newUser, notes: e.target.value })}\n                  placeholder=\"Any additional information about the user...\"\n                />\n              </div>\n\n              {/* Action Buttons */}\n              <div className=\"flex justify-end space-x-3 pt-6 border-t border-gray-200\">\n                <button\n                  type=\"button\"\n                  onClick={() => { resetForm(); setShowAddModal(false); }}\n                  className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n                  disabled={isCreating}\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"submit\"\n                  disabled={isCreating}\n                  className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 disabled:bg-gray-400\"\n                >\n                  {isCreating ? (\n                    <>\n                      <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\n                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                      </svg>\n                      Creating...\n                    </>\n                  ) : (\n                    <>\n                      <CheckCircleIcon className=\"-ml-1 mr-2 h-4 w-4\" />\n                      Create User Account\n                    </>\n                  )}\n                </button>\n              </div>\n            </form>\n          </div>\n        </Modal>\n      </div>\n    </MainLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAgCe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2E;IAC1H,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,UAAU;QACV,WAAW;QACX,OAAO;QACP,UAAU;QACV,kBAAkB;QAClB,MAAM;QACN,YAAY;QACZ,OAAO;QACP,aAAa;QACb,YAAY;QACZ,OAAO;IACT;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;iDAAY;oBAChB,IAAI;wBACF,WAAW;wBACX,MAAM,WAAW,MAAM,yHAAA,CAAA,kBAAe,CAAC,QAAQ;wBAE/C,oBAAoB;wBACpB,MAAM,iBAAiB,SAAS,GAAG;4EAAC,CAAA,OAAQ,CAAC;oCAC3C,GAAG,IAAI;oCACP,YAAY,KAAK,UAAU,IAAI,IAAI,OAAO,WAAW;oCACrD,YAAY,KAAK,UAAU,IAAI,KAAK,UAAU,IAAI,IAAI,OAAO,WAAW;gCAC1E,CAAC;;wBAED,SAAS;oBACX,EAAE,OAAO,OAAO;wBACd,mEAAmE;wBACnE,8DAA8D;wBAC9D,SAAS,EAAE;oBACb,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;8BAAG,EAAE;IAEL,MAAM,eAAe;QACnB,MAAM,SAAmB,EAAE;QAE3B,4BAA4B;QAC5B,IAAI,CAAC,QAAQ,QAAQ,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC;QAC1C,IAAI,CAAC,QAAQ,SAAS,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC;QAC3C,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC;QACvC,IAAI,CAAC,QAAQ,QAAQ,EAAE,OAAO,IAAI,CAAC;QACnC,IAAI,CAAC,QAAQ,gBAAgB,EAAE,OAAO,IAAI,CAAC;QAC3C,IAAI,CAAC,QAAQ,UAAU,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC;QAE5C,sBAAsB;QACtB,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG,OAAO,IAAI,CAAC;QAC7C,IAAI,CAAC,kBAAkB,IAAI,CAAC,QAAQ,QAAQ,GAAG,OAAO,IAAI,CAAC;QAE3D,mCAAmC;QACnC,IAAI,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC,WAAW,OAAO,QAAQ,QAAQ,CAAC,WAAW,KAAK;YACtF,OAAO,IAAI,CAAC;QACd;QAEA,mBAAmB;QACnB,MAAM,aAAa;QACnB,IAAI,QAAQ,KAAK,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ,KAAK,GAAG,OAAO,IAAI,CAAC;QAElE,gCAAgC;QAChC,IAAI,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC,WAAW,OAAO,QAAQ,KAAK,CAAC,WAAW,KAAK;YAChF,OAAO,IAAI,CAAC;QACd;QAEA,sBAAsB;QACtB,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG,OAAO,IAAI,CAAC;QAC7C,IAAI,QAAQ,QAAQ,KAAK,QAAQ,gBAAgB,EAAE,OAAO,IAAI,CAAC;QAE/D,iCAAiC;QACjC,IAAI,QAAQ,KAAK,IAAI,CAAC,yBAAyB,IAAI,CAAC,QAAQ,KAAK,CAAC,OAAO,CAAC,eAAe,MAAM;YAC7F,OAAO,IAAI,CAAC;QACd;QAEA,oBAAoB;QACpB,OAAO,OAAO,MAAM,KAAK;IAC3B;IAEA,MAAM,qBAAqB;QACzB,MAAM,OAAO,IAAI,OAAO,WAAW;QACnC,MAAM,WAAW,OAAO,MAAM,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG;QACtD,OAAO,CAAC,GAAG,EAAE,OAAO,UAAU;IAChC;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,cAAc;YAEd,MAAM,WAAW;gBACf,UAAU,QAAQ,QAAQ,CAAC,WAAW;gBACtC,OAAO,QAAQ,KAAK,CAAC,WAAW;gBAChC,eAAe,QAAQ,QAAQ;gBAC/B,MAAM,QAAQ,IAAI;gBAClB,WAAW,QAAQ,SAAS;gBAC5B,YAAY,QAAQ,UAAU;gBAC9B,OAAO,QAAQ,KAAK,IAAI;gBACxB,WAAW;YACb;YAEA,MAAM,cAAc,MAAM,yHAAA,CAAA,kBAAe,CAAC,UAAU,CAAC;YAErD,IAAI,aAAa;gBACf,sCAAsC;gBACtC,MAAM,cAAoB;oBACxB,GAAG,WAAW;oBACd,YAAY,YAAY,UAAU,IAAI,IAAI,OAAO,WAAW;gBAC9D;gBAEA,SAAS;uBAAI;oBAAO;iBAAY;gBAChC,gBAAgB;gBAChB;gBAEA,MAAM,CAAC,QAAQ,EAAE,QAAQ,SAAS,CAAC,4DAA4D,EAAE,QAAQ,QAAQ,CAAC,WAAW,EAAE,QAAQ,KAAK,CAAC,UAAU,EAAE,QAAQ,IAAI,CAAC,gBAAgB,EAAE,QAAQ,UAAU,CAAC,iBAAiB,EAAE,qBAAqB,uCAAuC,EAAE,QAAQ,QAAQ,CAAC,cAAc,EAAE,QAAQ,QAAQ,CAAC,sFAAsF,CAAC;YACva,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,YAAY;QAChB,WAAW;YACT,UAAU;YACV,WAAW;YACX,OAAO;YACP,UAAU;YACV,kBAAkB;YAClB,MAAM;YACN,YAAY;YACZ,OAAO;YACP,aAAa;YACb,YAAY;YACZ,OAAO;QACT;QACA,oBAAoB,EAAE;QACtB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB,OAAO,QAAgB;QAC9C,IAAI;YACF,MAAM,UAAU,MAAM,yHAAA,CAAA,kBAAe,CAAC,gBAAgB,CAAC,QAAQ,CAAC;YAEhE,IAAI,SAAS;gBACX,SAAS,MAAM,GAAG,CAAC,CAAA,OACjB,KAAK,EAAE,KAAK,SACR;wBAAE,GAAG,IAAI;wBAAE,WAAW,CAAC;oBAAc,IACrC;YAER,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,QAAQ;QACZ;YAAE,KAAK;YAAO,MAAM;YAAa,OAAO,MAAM,MAAM;QAAC;QACrD;YAAE,KAAK;YAAS,MAAM;YAAkB,OAAO,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM;QAAC;QAC5F;YAAE,KAAK;YAAmB,MAAM;YAAoB,OAAO,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,mBAAmB,MAAM;QAAC;QAClH;YAAE,KAAK;YAAsB,MAAM;YAAuB,OAAO,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,sBAAsB,MAAM;QAAC;QAC3H;YAAE,KAAK;YAAY,MAAM;YAAa,OAAO,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,YAAY,MAAM;QAAC;KAC9F;IAED,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,MAAM,gBAAgB,KAAK,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,KAAK,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAElF,MAAM,cAAc,iBAAiB,SAAS,KAAK,IAAI,KAAK;QAE5D,OAAO,iBAAiB;IAC1B;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,iCAAiC;IACjC,MAAM,UAAU,MAAM,SAAS;IAE/B,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC,iJAAA,CAAA,aAAU;sBACT,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gOAAA,CAAA,kBAAe;wBAAC,WAAU;;;;;;kCAC3B,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAMlD;IAEA,qBACE,6LAAC,iJAAA,CAAA,aAAU;kBACT,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;;kDAEV,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAqB,eAAY;;;;;;oCAAS;;;;;;;;;;;;;;;;;;8BAOpE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gOAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;;;;;;0CAE7B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;oDAAE;kEACC,6LAAC;kEAAO;;;;;;oDAA+B;kEAAwC,6LAAC;;;;;oDAAI;kEACpF,6LAAC;kEAAO;;;;;;oDAAkC;kEAA6B,6LAAC;;;;;oDAAI;kEAC5E,6LAAC;kEAAO;;;;;;oDAAkC;kEAA4B,6LAAC;;;;;oDAAI;kEAC3E,6LAAC;kEAAO;;;;;;oDAAqC;kEAAkC,6LAAC;;;;;oDAAI;kEACpF,6LAAC;kEAAO;;;;;;oDAA+B;;;;;;;0DAE3C,6LAAC;gDAAE,WAAU;;oDAAmB;kEAC3B,6LAAC;kEAAO;;;;;;oDAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQxC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAwB,eAAY;;;;;;;;;;;sDAE3D,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAAqC,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOzE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;sDAGnB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEACX,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gOAAA,CAAA,kBAAe;gDAAC,WAAU;gDAAuB,eAAY;;;;;;;;;;;sDAEhE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEACX,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQzD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAoC;;;;;;;;;;;;;;;;sDAGxD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEACX,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUnE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,wOAAA,CAAA,sBAAmB;4CAAC,WAAU;4CAAwB,eAAY;;;;;;;;;;;kDAErE,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;0CAIjD,6LAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;wCAEC,SAAS,IAAM,gBAAgB,KAAK,GAAG;wCACvC,WAAW,CAAC,yCAAyC,EACnD,iBAAiB,KAAK,GAAG,GACrB,gCACA,qCACJ;;4CAED,KAAK,IAAI;4CAAC;4CAAG,KAAK,KAAK;4CAAC;;uCARpB,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;8BAgBvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAmD;oCACvD,cAAc,MAAM;oCAAC;;;;;;;4BAG9B,wBACC,6LAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wCAAY,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;;;;;;;;;;;;uCAJT;;;;;;;;;qDAUd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;sDAIhC,6LAAC;4CAAM,WAAU;sDACd,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;oDAAiB,WAAU;;sEAC1B,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;sFACb,KAAK,SAAS,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;kFAGzC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FAAqC,KAAK,SAAS;;;;;;0FAClE,6LAAC;gFAAI,WAAU;0FAAyB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;sEAIxD,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,CAAC,wEAAwE,EAAE,aAAa,KAAK,IAAI,GAAG;0EAClH,YAAY,KAAK,IAAI;;;;;;;;;;;sEAG1B,6LAAC;4DAAG,WAAU;sEACX,KAAK,UAAU;;;;;;sEAElB,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,CAAC,wEAAwE,EACxF,KAAK,SAAS,GAAG,gCAAgC,2BACjD;0EACC,KAAK,SAAS,GAAG,WAAW;;;;;;;;;;;sEAGjC,6LAAC;4DAAG,WAAU;sEACX,KAAK,UAAU,GAAG,WAAW,KAAK,UAAU,IAAI;;;;;;sEAEnD,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEACC,WAAU;oEACV,SAAS,IAAM,MAAM;8EACtB;;;;;;8EAGD,6LAAC;oEACC,WAAW,GAAG,KAAK,SAAS,GAAG,oCAAoC,uCAAuC;oEAC1G,SAAS,IAAM,iBAAiB,KAAK,EAAE,EAAE,KAAK,SAAS;8EAEtD,KAAK,SAAS,GAAG,eAAe;;;;;;;;;;;;;mDA3C9B,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAwD9B,6LAAC,oIAAA,CAAA,QAAK;oBACJ,QAAQ;oBACR,SAAS;wBAAQ;wBAAa,gBAAgB;oBAAQ;oBACtD,OAAM;oBACN,UAAS;8BAET,cAAA,6LAAC;wBAAI,WAAU;;4BAEZ,iBAAiB,MAAM,GAAG,mBACzB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gPAAA,CAAA,0BAAuB;4CAAC,WAAU;;;;;;sDACnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,6LAAC;oDAAG,WAAU;8DACX,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,6LAAC;sEAAgB;2DAAR;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQrB,6LAAC;gCAAK,UAAU,CAAC;oCAAQ,EAAE,cAAc;oCAAI;gCAAiB;gCAAG,WAAU;;kDAEzE,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAgB,WAAU;0EAA0C;;;;;;0EAGnF,6LAAC;gEACC,MAAK;gEACL,IAAG;gEACH,QAAQ;gEACR,WAAU;gEACV,OAAO,QAAQ,QAAQ;gEACvB,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACnE,aAAY;;;;;;0EAEd,6LAAC;gEAAE,WAAU;0EAA6B;;;;;;;;;;;;kEAE5C,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAiB,WAAU;0EAA0C;;;;;;0EAGpF,6LAAC;gEACC,MAAK;gEACL,IAAG;gEACH,QAAQ;gEACR,WAAU;gEACV,OAAO,QAAQ,SAAS;gEACxB,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACpE,aAAY;;;;;;;;;;;;kEAGhB,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAa,WAAU;0EAA0C;;;;;;0EAGhF,6LAAC;gEACC,MAAK;gEACL,IAAG;gEACH,QAAQ;gEACR,WAAU;gEACV,OAAO,QAAQ,KAAK;gEACpB,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAC;gEAChE,aAAY;;;;;;;;;;;;kEAGhB,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAa,WAAU;0EAA0C;;;;;;0EAGhF,6LAAC;gEACC,MAAK;gEACL,IAAG;gEACH,WAAU;gEACV,OAAO,QAAQ,KAAK;gEACpB,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAC;gEAChE,aAAY;;;;;;;;;;;;;;;;;;;;;;;;kDAOpB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAgB,WAAU;0EAA0C;;;;;;0EAGnF,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,MAAM,eAAe,SAAS;wEAC9B,IAAG;wEACH,QAAQ;wEACR,WAAU;wEACV,OAAO,QAAQ,QAAQ;wEACvB,UAAU,CAAC,IAAM,WAAW;gFAAE,GAAG,OAAO;gFAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4EAAC;wEACnE,aAAY;;;;;;kFAEd,6LAAC;wEACC,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,gBAAgB,CAAC;kFAE/B,6BACC,6LAAC,0NAAA,CAAA,eAAY;4EAAC,WAAU;;;;;iGAExB,6LAAC,gNAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;;;;;;;;;;;;0EAIzB,6LAAC;gEAAE,WAAU;0EAA6B;;;;;;;;;;;;kEAE5C,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAwB,WAAU;0EAA0C;;;;;;0EAG3F,6LAAC;gEACC,MAAM,eAAe,SAAS;gEAC9B,IAAG;gEACH,QAAQ;gEACR,WAAU;gEACV,OAAO,QAAQ,gBAAgB;gEAC/B,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,kBAAkB,EAAE,MAAM,CAAC,KAAK;oEAAC;gEAC3E,aAAY;;;;;;;;;;;;;;;;;;;;;;;;kDAOpB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAY,WAAU;0EAA0C;;;;;;0EAG/E,6LAAC;gEACC,IAAG;gEACH,QAAQ;gEACR,WAAU;gEACV,OAAO,QAAQ,IAAI;gEACnB,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oEAAQ;;kFAEtE,6LAAC;wEAAO,OAAM;kFAAW;;;;;;kFACzB,6LAAC;wEAAO,OAAM;kFAAqB;;;;;;kFACnC,6LAAC;wEAAO,OAAM;kFAAkB;;;;;;kFAChC,6LAAC;wEAAO,OAAM;kFAAQ;;;;;;;;;;;;;;;;;;kEAG1B,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAkB,WAAU;0EAA0C;;;;;;0EAGrF,6LAAC;gEACC,IAAG;gEACH,QAAQ;gEACR,WAAU;gEACV,OAAO,QAAQ,UAAU;gEACzB,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,YAAY,EAAE,MAAM,CAAC,KAAK;oEAAC;;kFAErE,6LAAC;wEAAO,OAAM;kFAAG;;;;;;kFACjB,6LAAC;wEAAO,OAAM;kFAAa;;;;;;kFAC3B,6LAAC;wEAAO,OAAM;kFAAoB;;;;;;kFAClC,6LAAC;wEAAO,OAAM;kFAAyB;;;;;;kFACvC,6LAAC;wEAAO,OAAM;kFAAK;;;;;;kFACnB,6LAAC;wEAAO,OAAM;kFAAiB;;;;;;kFAC/B,6LAAC;wEAAO,OAAM;kFAAoB;;;;;;;;;;;;;;;;;;kEAGtC,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAmB,WAAU;0EAA0C;;;;;;0EAGtF,6LAAC;gEACC,MAAK;gEACL,IAAG;gEACH,WAAU;gEACV,OAAO,QAAQ,WAAW;gEAC1B,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACtE,aAAY;;;;;;;;;;;;kEAGhB,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAkB,WAAU;0EAA0C;;;;;;0EAGrF,6LAAC;gEACC,MAAK;gEACL,IAAG;gEACH,WAAU;gEACV,OAAO,QAAQ,UAAU;gEACzB,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,YAAY,EAAE,MAAM,CAAC,KAAK;oEAAC;;;;;;;;;;;;;;;;;;;;;;;;kDAO7E,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAa,WAAU;0DAA0C;;;;;;0DAGhF,6LAAC;gDACC,IAAG;gDACH,MAAM;gDACN,WAAU;gDACV,OAAO,QAAQ,KAAK;gDACpB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAChE,aAAY;;;;;;;;;;;;kDAKhB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,SAAS;oDAAQ;oDAAa,gBAAgB;gDAAQ;gDACtD,WAAU;gDACV,UAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,MAAK;gDACL,UAAU;gDACV,WAAU;0DAET,2BACC;;sEACE,6LAAC;4DAAI,WAAU;4DAA6C,MAAK;4DAAO,SAAQ;;8EAC9E,6LAAC;oEAAO,WAAU;oEAAa,IAAG;oEAAK,IAAG;oEAAK,GAAE;oEAAK,QAAO;oEAAe,aAAY;;;;;;8EACxF,6LAAC;oEAAK,WAAU;oEAAa,MAAK;oEAAe,GAAE;;;;;;;;;;;;wDAC/C;;iFAIR;;sEACE,6LAAC,gOAAA,CAAA,kBAAe;4DAAC,WAAU;;;;;;wDAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxE;GArwBwB;;QACL,kIAAA,CAAA,UAAO;;;KADF", "debugId": null}}]}