const { spawn } = require('child_process')
const { execSync } = require('child_process')
const fs = require('fs')

console.log('🚀 تشغيل SFF Management System - تطبيق سطح المكتب\n')

// Check if Node.js is available
try {
  execSync('node --version', { stdio: 'pipe' })
  console.log('✅ Node.js متوفر')
} catch (error) {
  console.error('❌ خطأ: Node.js غير متوفر. يرجى تثبيت Node.js من https://nodejs.org/')
  process.exit(1)
}

// Check if dependencies are installed
if (!fs.existsSync('node_modules')) {
  console.log('📦 تثبيت المتطلبات...')
  try {
    execSync('npm install', { stdio: 'inherit' })
    console.log('✅ تم تثبيت المتطلبات بنجاح')
  } catch (error) {
    console.error('❌ خطأ في تثبيت المتطلبات')
    process.exit(1)
  }
} else {
  console.log('✅ المتطلبات متوفرة')
}

console.log('\n🌐 بدء خادم التطوير...')
console.log('⏳ انتظار تحميل التطبيق...')
console.log('🖥️ سيتم فتح نافذة التطبيق تلقائياً\n')

// Start the development server and Electron
const child = spawn('npm', ['run', 'electron-dev'], {
  stdio: 'inherit',
  shell: true
})

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف التطبيق...')
  child.kill('SIGINT')
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n🛑 إيقاف التطبيق...')
  child.kill('SIGTERM')
  process.exit(0)
})

child.on('close', (code) => {
  console.log(`\n📝 انتهى التطبيق برمز: ${code}`)
  process.exit(code)
})

child.on('error', (error) => {
  console.error('❌ خطأ في تشغيل التطبيق:', error.message)
  process.exit(1)
})
