import { createClient } from '@supabase/supabase-js'
import { createClientComponentClient, createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Client-side Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Client component client
export const createSupabaseClient = () => createClientComponentClient()

// Server component client
export const createSupabaseServerClient = () => createServerComponentClient({ cookies })

// Admin client (server-side only)
export const createSupabaseAdminClient = () => {
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
  return createClient(supabaseUrl, serviceRole<PERSON><PERSON>, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

// Database types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string
          role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'
          department: string | null
          phone: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name: string
          role?: 'admin' | 'quality_manager' | 'production_manager' | 'employee'
          department?: string | null
          phone?: string | null
          is_active?: boolean
        }
        Update: {
          email?: string
          full_name?: string
          role?: 'admin' | 'quality_manager' | 'production_manager' | 'employee'
          department?: string | null
          phone?: string | null
          is_active?: boolean
        }
      }
      inventory_categories: {
        Row: {
          id: string
          name: string
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          name: string
          description?: string | null
        }
        Update: {
          name?: string
          description?: string | null
        }
      }
      inventory_items: {
        Row: {
          id: string
          name: string
          sku: string
          category_id: string | null
          description: string | null
          unit_of_measure: string
          current_stock: number
          minimum_stock: number
          maximum_stock: number | null
          unit_cost: number
          supplier_info: any | null
          storage_conditions: string | null
          expiry_date: string | null
          batch_number: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          name: string
          sku: string
          category_id?: string | null
          description?: string | null
          unit_of_measure: string
          current_stock?: number
          minimum_stock?: number
          maximum_stock?: number | null
          unit_cost?: number
          supplier_info?: any | null
          storage_conditions?: string | null
          expiry_date?: string | null
          batch_number?: string | null
          is_active?: boolean
        }
        Update: {
          name?: string
          sku?: string
          category_id?: string | null
          description?: string | null
          unit_of_measure?: string
          current_stock?: number
          minimum_stock?: number
          maximum_stock?: number | null
          unit_cost?: number
          supplier_info?: any | null
          storage_conditions?: string | null
          expiry_date?: string | null
          batch_number?: string | null
          is_active?: boolean
        }
      }
      recipes: {
        Row: {
          id: string
          name: string
          code: string
          description: string | null
          category: string | null
          version: number
          batch_size: number
          unit_of_measure: string
          estimated_cost: number | null
          preparation_time: number | null
          instructions: string | null
          notes: string | null
          is_active: boolean
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          name: string
          code: string
          description?: string | null
          category?: string | null
          version?: number
          batch_size: number
          unit_of_measure: string
          estimated_cost?: number | null
          preparation_time?: number | null
          instructions?: string | null
          notes?: string | null
          is_active?: boolean
          created_by?: string | null
        }
        Update: {
          name?: string
          code?: string
          description?: string | null
          category?: string | null
          version?: number
          batch_size?: number
          unit_of_measure?: string
          estimated_cost?: number | null
          preparation_time?: number | null
          instructions?: string | null
          notes?: string | null
          is_active?: boolean
        }
      }
      production_batches: {
        Row: {
          id: string
          batch_number: string
          recipe_id: string
          batch_type: 'test' | 'production'
          planned_quantity: number
          actual_quantity: number | null
          unit_of_measure: string
          status: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'
          priority: 'low' | 'normal' | 'high' | 'urgent'
          planned_start_date: string | null
          actual_start_date: string | null
          planned_end_date: string | null
          actual_end_date: string | null
          production_cost: number | null
          yield_percentage: number | null
          quality_approved: boolean | null
          notes: string | null
          created_by: string | null
          assigned_to: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          batch_number: string
          recipe_id: string
          batch_type: 'test' | 'production'
          planned_quantity: number
          actual_quantity?: number | null
          unit_of_measure: string
          status?: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'
          priority?: 'low' | 'normal' | 'high' | 'urgent'
          planned_start_date?: string | null
          actual_start_date?: string | null
          planned_end_date?: string | null
          actual_end_date?: string | null
          production_cost?: number | null
          yield_percentage?: number | null
          quality_approved?: boolean | null
          notes?: string | null
          created_by?: string | null
          assigned_to?: string | null
        }
        Update: {
          batch_number?: string
          recipe_id?: string
          batch_type?: 'test' | 'production'
          planned_quantity?: number
          actual_quantity?: number | null
          unit_of_measure?: string
          status?: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'
          priority?: 'low' | 'normal' | 'high' | 'urgent'
          planned_start_date?: string | null
          actual_start_date?: string | null
          planned_end_date?: string | null
          actual_end_date?: string | null
          production_cost?: number | null
          yield_percentage?: number | null
          quality_approved?: boolean | null
          notes?: string | null
          assigned_to?: string | null
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      check_recipe_availability: {
        Args: {
          recipe_uuid: string
          batch_size_multiplier?: number
        }
        Returns: {
          item_id: string
          item_name: string
          required_quantity: number
          available_quantity: number
          is_sufficient: boolean
        }[]
      }
      calculate_recipe_cost: {
        Args: {
          recipe_uuid: string
          batch_size_multiplier?: number
        }
        Returns: number
      }
    }
    Enums: {
      user_role: 'admin' | 'quality_manager' | 'production_manager' | 'employee'
    }
  }
}
