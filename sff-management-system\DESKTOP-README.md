# SFF Management System - تطبيق سطح المكتب

## نظام إدارة الإنتاج والجودة SFF - إصدار سطح المكتب

### 🖥️ تطبيق سطح مكتب متكامل لإدارة الإنتاج والجودة

---

## ✨ المميزات الجديدة في إصدار سطح المكتب

### 🚀 **مميزات التطبيق المكتبي**
- ✅ **تشغيل مستقل** - لا يحتاج متصفح ويب
- ✅ **أداء محسن** - سرعة أكبر واستجابة أفضل
- ✅ **واجهة أصلية** - تصميم يتناسب مع نظام التشغيل
- ✅ **اختصارات لوحة المفاتيح** - تحكم سريع ومريح
- ✅ **قوائم تطبيق** - قوائم احترافية باللغة العربية
- ✅ **حفظ محلي** - إمكانية العمل بدون إنترنت

### 🛡️ **الأمان والخصوصية**
- ✅ **بيانات محلية** - جميع البيانات محفوظة محلياً
- ✅ **لا توجد تتبع** - خصوصية كاملة للمستخدم
- ✅ **تشفير البيانات** - حماية متقدمة للمعلومات
- ✅ **نسخ احتياطية** - حفظ تلقائي للبيانات

---

## 🔧 متطلبات التشغيل

### **الحد الأدنى للمتطلبات:**
- **نظام التشغيل:** Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **المعالج:** Intel/AMD x64 أو Apple Silicon
- **الذاكرة:** 4 GB RAM
- **التخزين:** 500 MB مساحة فارغة
- **Node.js:** الإصدار 18 أو أحدث

### **المتطلبات الموصى بها:**
- **المعالج:** Intel i5/AMD Ryzen 5 أو أفضل
- **الذاكرة:** 8 GB RAM أو أكثر
- **التخزين:** 2 GB مساحة فارغة
- **الشاشة:** 1920x1080 أو أعلى

---

## 🚀 طرق التشغيل

### **الطريقة الأولى: التشغيل السريع (Windows)**
```bash
# انقر نقراً مزدوجاً على الملف
start-desktop.bat
```

### **الطريقة الثانية: سطر الأوامر**
```bash
# تثبيت المتطلبات
npm install

# تشغيل التطبيق في وضع التطوير
npm run electron-dev

# أو تشغيل التطبيق المبني
npm run electron
```

### **الطريقة الثالثة: بناء التطبيق للتوزيع**
```bash
# بناء التطبيق لنظام التشغيل الحالي
npm run dist

# بناء ملف محمول
npm run pack
```

---

## 📋 الميزات المتاحة

### **🏭 إدارة الإنتاج**
- ✅ **إدارة المخزون** - تتبع شامل للمواد والمنتجات
- ✅ **إدارة الوصفات** - نظام وصفات متقدم مع حساب التكاليف
- ✅ **إدارة الإنتاج** - تتبع دفعات الإنتاج والجودة
- ✅ **إدارة المستخدمين** - نظام صلاحيات متقدم

### **📊 إدارة الجودة**
- ✅ **الوثائق** - MSDS, COA, TDS, مواصفات الجودة
- ✅ **التقارير** - تقارير شاملة وإحصائيات
- ✅ **المراقبة** - مراقبة مستمرة للجودة
- ✅ **التدقيق** - سجل كامل للعمليات

### **💾 إدارة البيانات**
- ✅ **قاعدة بيانات محلية** - Supabase مدمجة
- ✅ **نسخ احتياطية** - حفظ تلقائي ويدوي
- ✅ **استيراد/تصدير** - تبادل البيانات بسهولة
- ✅ **مزامنة** - مزامنة مع الخدمات السحابية

---

## ⌨️ اختصارات لوحة المفاتيح

### **اختصارات عامة:**
- `Ctrl+N` - جديد
- `Ctrl+O` - فتح
- `Ctrl+S` - حفظ
- `Ctrl+Q` - خروج
- `F11` - ملء الشاشة
- `F12` - أدوات المطور

### **اختصارات التنقل:**
- `Ctrl+1` - لوحة التحكم
- `Ctrl+2` - إدارة المخزون
- `Ctrl+3` - إدارة الوصفات
- `Ctrl+4` - إدارة الإنتاج
- `Ctrl+5` - إدارة الجودة

### **اختصارات التحرير:**
- `Ctrl+Z` - تراجع
- `Ctrl+Y` - إعادة
- `Ctrl+C` - نسخ
- `Ctrl+V` - لصق
- `Ctrl+X` - قص

---

## 🔧 الإعدادات والتخصيص

### **إعدادات التطبيق:**
- **اللغة:** العربية (افتراضي), الإنجليزية
- **المظهر:** فاتح, داكن, تلقائي
- **الإشعارات:** تفعيل/إلغاء الإشعارات
- **النسخ الاحتياطية:** تلقائي/يدوي

### **إعدادات قاعدة البيانات:**
- **الموقع:** محلي/سحابي
- **النسخ الاحتياطي:** يومي/أسبوعي/شهري
- **التشفير:** تفعيل/إلغاء
- **ضغط البيانات:** تفعيل/إلغاء

---

## 🛠️ استكشاف الأخطاء وإصلاحها

### **مشاكل شائعة وحلولها:**

#### **التطبيق لا يبدأ:**
```bash
# تحقق من تثبيت Node.js
node --version

# إعادة تثبيت المتطلبات
rm -rf node_modules
npm install
```

#### **خطأ في قاعدة البيانات:**
```bash
# إعادة تعيين قاعدة البيانات
npm run reset-db

# استعادة من نسخة احتياطية
npm run restore-backup
```

#### **مشاكل الأداء:**
- أغلق التطبيقات الأخرى
- تحقق من مساحة القرص الصلب
- أعد تشغيل التطبيق

---

## 📞 الدعم والمساعدة

### **طرق الحصول على المساعدة:**
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +966-XX-XXX-XXXX
- **الموقع:** https://sff-management.com
- **التوثيق:** https://docs.sff-management.com

### **الإبلاغ عن الأخطاء:**
- استخدم أدوات المطور (F12)
- احفظ رسائل الخطأ
- أرسل تقرير مفصل

---

## 📄 الترخيص والحقوق

**حقوق الطبع والنشر © 2024 SFF Company**
**جميع الحقوق محفوظة**

هذا التطبيق مرخص للاستخدام التجاري والشخصي.
للمزيد من المعلومات، راجع ملف LICENSE.

---

## 🔄 التحديثات

### **الإصدار الحالي: 1.0.0**
- إطلاق أول إصدار من تطبيق سطح المكتب
- دعم كامل لجميع الميزات
- واجهة محسنة لسطح المكتب
- أداء محسن وثبات أكبر

### **التحديثات القادمة:**
- دعم المزيد من اللغات
- ميزات ذكية بالذكاء الاصطناعي
- تكامل مع أنظمة ERP
- تطبيق الهاتف المحمول

---

**استمتع باستخدام نظام إدارة الإنتاج والجودة SFF! 🎉**
