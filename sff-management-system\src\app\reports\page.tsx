'use client'

import { useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { useAuth } from '@/components/providers'
import { 
  ChartBarIcon, 
  DocumentArrowDownIcon, 
  CurrencyDollarIcon,
  CubeIcon,
  CogIcon,
  BeakerIcon,
  CalendarIcon
} from '@heroicons/react/24/outline'

interface ReportCard {
  id: string
  title: string
  description: string
  icon: any
  category: 'production' | 'inventory' | 'quality' | 'financial'
  color: string
  bgColor: string
}

export default function ReportsPage() {
  const { user } = useAuth()
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'production' | 'inventory' | 'quality' | 'financial'>('all')

  const reportCards: ReportCard[] = [
    {
      id: '1',
      title: 'Production Efficiency Report',
      description: 'Analyze production performance, yield rates, and batch completion times',
      icon: CogIcon,
      category: 'production',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 hover:bg-blue-100'
    },
    {
      id: '2',
      title: 'Inventory Turnover Analysis',
      description: 'Track inventory movement, stock levels, and usage patterns',
      icon: CubeIcon,
      category: 'inventory',
      color: 'text-green-600',
      bgColor: 'bg-green-50 hover:bg-green-100'
    },
    {
      id: '3',
      title: 'Quality Compliance Report',
      description: 'Monitor quality metrics, test results, and compliance status',
      icon: BeakerIcon,
      category: 'quality',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50 hover:bg-purple-100'
    },
    {
      id: '4',
      title: 'Cost Analysis Report',
      description: 'Detailed breakdown of production costs and material expenses',
      icon: CurrencyDollarIcon,
      category: 'financial',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50 hover:bg-yellow-100'
    },
    {
      id: '5',
      title: 'Monthly Production Summary',
      description: 'Comprehensive overview of monthly production activities',
      icon: ChartBarIcon,
      category: 'production',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 hover:bg-blue-100'
    },
    {
      id: '6',
      title: 'Low Stock Alert Report',
      description: 'Items requiring immediate attention and reordering',
      icon: CubeIcon,
      category: 'inventory',
      color: 'text-red-600',
      bgColor: 'bg-red-50 hover:bg-red-100'
    },
    {
      id: '7',
      title: 'Recipe Cost Breakdown',
      description: 'Detailed cost analysis for each recipe and formulation',
      icon: CurrencyDollarIcon,
      category: 'financial',
      color: 'text-green-600',
      bgColor: 'bg-green-50 hover:bg-green-100'
    },
    {
      id: '8',
      title: 'Quality Test Results',
      description: 'Laboratory test results and quality metrics overview',
      icon: BeakerIcon,
      category: 'quality',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50 hover:bg-purple-100'
    },
    {
      id: '9',
      title: 'Batch Tracking Report',
      description: 'Complete traceability from raw materials to finished products',
      icon: CogIcon,
      category: 'production',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 hover:bg-blue-100'
    }
  ]

  const categories = [
    { key: 'all', name: 'All Reports', count: reportCards.length },
    { key: 'production', name: 'Production', count: reportCards.filter(r => r.category === 'production').length },
    { key: 'inventory', name: 'Inventory', count: reportCards.filter(r => r.category === 'inventory').length },
    { key: 'quality', name: 'Quality', count: reportCards.filter(r => r.category === 'quality').length },
    { key: 'financial', name: 'Financial', count: reportCards.filter(r => r.category === 'financial').length }
  ]

  const filteredReports = selectedCategory === 'all' 
    ? reportCards 
    : reportCards.filter(report => report.category === selectedCategory)

  if (!user) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
            <p className="mt-2 text-sm text-gray-700">
              Generate comprehensive reports and analyze your production data
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex space-x-3">
            <button
              type="button"
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <CalendarIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
              Date Range
            </button>
            <button
              type="button"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <DocumentArrowDownIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
              Export All
            </button>
          </div>
        </div>

        {/* Category Tabs */}
        <div className="bg-white shadow rounded-lg">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
              {categories.map((category) => (
                <button
                  key={category.key}
                  onClick={() => setSelectedCategory(category.key as any)}
                  className={`${
                    selectedCategory === category.key
                      ? 'border-green-500 text-green-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
                >
                  {category.name}
                  <span className={`ml-2 py-0.5 px-2 rounded-full text-xs ${
                    selectedCategory === category.key
                      ? 'bg-green-100 text-green-600'
                      : 'bg-gray-100 text-gray-500'
                  }`}>
                    {category.count}
                  </span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-6 w-6 text-blue-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Reports Generated</dt>
                    <dd className="text-lg font-medium text-gray-900">247</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DocumentArrowDownIcon className="h-6 w-6 text-green-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">This Month</dt>
                    <dd className="text-lg font-medium text-gray-900">23</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CalendarIcon className="h-6 w-6 text-purple-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Scheduled</dt>
                    <dd className="text-lg font-medium text-gray-900">8</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CurrencyDollarIcon className="h-6 w-6 text-yellow-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Cost Savings</dt>
                    <dd className="text-lg font-medium text-gray-900">$12.4K</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Reports Grid */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {filteredReports.map((report) => {
            const IconComponent = report.icon
            return (
              <div
                key={report.id}
                className={`relative group bg-white p-6 rounded-lg shadow hover:shadow-lg transition-all duration-200 cursor-pointer border border-gray-200 ${report.bgColor}`}
              >
                <div>
                  <span className={`rounded-lg inline-flex p-3 ring-4 ring-white ${report.bgColor.replace('hover:', '')}`}>
                    <IconComponent className={`h-6 w-6 ${report.color}`} aria-hidden="true" />
                  </span>
                </div>
                <div className="mt-8">
                  <h3 className="text-lg font-medium text-gray-900 group-hover:text-gray-600">
                    <span className="absolute inset-0" aria-hidden="true" />
                    {report.title}
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    {report.description}
                  </p>
                </div>
                <div className="mt-6 flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${
                      report.category === 'production' ? 'bg-blue-100 text-blue-800' :
                      report.category === 'inventory' ? 'bg-green-100 text-green-800' :
                      report.category === 'quality' ? 'bg-purple-100 text-purple-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {report.category}
                    </span>
                  </div>
                  <div className="flex space-x-2">
                    <button className="text-sm text-green-600 hover:text-green-500 font-medium">
                      Generate
                    </button>
                    <button className="text-sm text-gray-500 hover:text-gray-400">
                      <DocumentArrowDownIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Recent Reports */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Recent Reports
            </h3>
            <div className="space-y-3">
              {[
                { name: 'Production Efficiency Report - November 2024', date: '2024-12-01', size: '2.4 MB' },
                { name: 'Inventory Turnover Analysis - Q4 2024', date: '2024-11-28', size: '1.8 MB' },
                { name: 'Quality Compliance Report - Week 48', date: '2024-11-25', size: '3.1 MB' },
                { name: 'Cost Analysis Report - November 2024', date: '2024-11-22', size: '1.2 MB' }
              ].map((report, index) => (
                <div key={index} className="flex items-center justify-between py-3 border-b border-gray-200 last:border-b-0">
                  <div className="flex items-center space-x-3">
                    <DocumentArrowDownIcon className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">{report.name}</p>
                      <p className="text-sm text-gray-500">{report.date} • {report.size}</p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button className="text-sm text-green-600 hover:text-green-500">
                      Download
                    </button>
                    <button className="text-sm text-gray-500 hover:text-gray-400">
                      View
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
