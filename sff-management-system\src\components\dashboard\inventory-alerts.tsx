'use client'

import { useEffect, useState } from 'react'
import { createSupabaseClient } from '@/lib/supabase'
import Link from 'next/link'
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline'

interface LowStockItem {
  id: string
  name: string
  sku: string
  current_stock: number
  minimum_stock: number
  unit_of_measure: string
}

export function InventoryAlerts() {
  const [lowStockItems, setLowStockItems] = useState<LowStockItem[]>([])
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseClient()

  useEffect(() => {
    async function fetchLowStockItems() {
      try {
        // Mock low stock items for demo
        const mockLowStockItems: LowStockItem[] = [
          {
            id: '1',
            name: 'Vanilla Extract',
            sku: 'VAN-001',
            current_stock: 5,
            minimum_stock: 10,
            unit_of_measure: 'liters'
          },
          {
            id: '2',
            name: 'Citric Acid',
            sku: 'CIT-001',
            current_stock: 8,
            minimum_stock: 12,
            unit_of_measure: 'kg'
          },
          {
            id: '3',
            name: 'Glass Bottles 100ml',
            sku: 'BOT-100',
            current_stock: 200,
            minimum_stock: 500,
            unit_of_measure: 'pieces'
          }
        ]

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800))

        setLowStockItems(mockLowStockItems)
      } catch (error) {
        console.error('Error fetching low stock items:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchLowStockItems()
  }, [])

  if (loading) {
    return (
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Inventory Alerts</h3>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center space-x-3 animate-pulse">
                <div className="h-5 w-5 bg-gray-200 rounded"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Inventory Alerts</h3>
          <Link
            href="/inventory"
            className="text-sm font-medium text-blue-600 hover:text-blue-500"
          >
            View all
          </Link>
        </div>
        
        {lowStockItems.length === 0 ? (
          <div className="text-center py-6">
            <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100">
              <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 className="mt-2 text-sm font-medium text-gray-900">All good!</h3>
            <p className="mt-1 text-sm text-gray-500">No low stock alerts at the moment.</p>
          </div>
        ) : (
          <div className="space-y-3">
            {lowStockItems.map((item) => (
              <div
                key={item.id}
                className="flex items-center space-x-3 p-3 bg-red-50 rounded-lg border border-red-200"
              >
                <ExclamationTriangleIcon className="h-5 w-5 text-red-600 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {item.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {item.current_stock} {item.unit_of_measure} remaining 
                    (min: {item.minimum_stock})
                  </p>
                </div>
              </div>
            ))}
            
            {lowStockItems.length > 0 && (
              <div className="mt-4">
                <Link
                  href="/inventory?filter=low_stock"
                  className="w-full flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200"
                >
                  View all low stock items
                </Link>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
