/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable static export for Electron
  output: 'export',
  
  // Disable image optimization for static export
  images: {
    unoptimized: true
  },
  
  // Configure trailing slash
  trailingSlash: true,
  
  // Configure asset prefix for production
  assetPrefix: process.env.NODE_ENV === 'production' ? './' : '',
  
  // Configure base path
  basePath: '',
  
  // Disable server-side features for static export
  experimental: {
    esmExternals: false
  },
  
  // Configure webpack for Electron compatibility
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.target = 'electron-renderer'
    }
    
    // Handle node modules in Electron
    config.externals = config.externals || []
    config.externals.push({
      'electron': 'commonjs electron'
    })
    
    return config
  },
  
  // Environment variables
  env: {
    ELECTRON_IS_DEV: process.env.NODE_ENV === 'development'
  }
}

module.exports = nextConfig
