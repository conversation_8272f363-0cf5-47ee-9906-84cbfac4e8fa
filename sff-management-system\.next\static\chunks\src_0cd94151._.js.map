{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\nimport {\n  HomeIcon,\n  CubeIcon,\n  BeakerIcon,\n  CogIcon,\n  DocumentTextIcon,\n  UsersIcon,\n  ChartBarIcon,\n  ClipboardDocumentListIcon,\n} from '@heroicons/react/24/outline'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { clsx } from 'clsx'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/', icon: HomeIcon },\n  { name: 'Inventory', href: '/inventory', icon: CubeIcon },\n  { name: 'Recipes', href: '/recipes', icon: ClipboardDocumentListIcon },\n  { name: 'Production', href: '/production', icon: CogIcon },\n  { name: 'Quality', href: '/quality', icon: BeakerIcon },\n  { name: 'Reports', href: '/reports', icon: ChartBarIcon },\n  { name: 'Documents', href: '/documents', icon: DocumentTextIcon },\n  { name: 'Users', href: '/users', icon: UsersIcon },\n]\n\ninterface SidebarProps {\n  open: boolean\n  setOpen: (open: boolean) => void\n}\n\nexport function Sidebar({ open, setOpen }: SidebarProps) {\n  const pathname = usePathname()\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <Transition.Root show={open} as={Fragment}>\n        <Dialog as=\"div\" className=\"relative z-50 lg:hidden\" onClose={setOpen}>\n          <Transition.Child\n            as={Fragment}\n            enter=\"transition-opacity ease-linear duration-300\"\n            enterFrom=\"opacity-0\"\n            enterTo=\"opacity-100\"\n            leave=\"transition-opacity ease-linear duration-300\"\n            leaveFrom=\"opacity-100\"\n            leaveTo=\"opacity-0\"\n          >\n            <div className=\"fixed inset-0 bg-gray-900/80\" />\n          </Transition.Child>\n\n          <div className=\"fixed inset-0 flex\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"transition ease-in-out duration-300 transform\"\n              enterFrom=\"-translate-x-full\"\n              enterTo=\"translate-x-0\"\n              leave=\"transition ease-in-out duration-300 transform\"\n              leaveFrom=\"translate-x-0\"\n              leaveTo=\"-translate-x-full\"\n            >\n              <Dialog.Panel className=\"relative mr-16 flex w-full max-w-xs flex-1\">\n                <Transition.Child\n                  as={Fragment}\n                  enter=\"ease-in-out duration-300\"\n                  enterFrom=\"opacity-0\"\n                  enterTo=\"opacity-100\"\n                  leave=\"ease-in-out duration-300\"\n                  leaveFrom=\"opacity-100\"\n                  leaveTo=\"opacity-0\"\n                >\n                  <div className=\"absolute left-full top-0 flex w-16 justify-center pt-5\">\n                    <button type=\"button\" className=\"-m-2.5 p-2.5\" onClick={() => setOpen(false)}>\n                      <span className=\"sr-only\">Close sidebar</span>\n                      <XMarkIcon className=\"h-6 w-6 text-white\" aria-hidden=\"true\" />\n                    </button>\n                  </div>\n                </Transition.Child>\n                <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4\">\n                  <div className=\"flex h-16 shrink-0 items-center\">\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">SFF</span>\n                    </div>\n                    <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n                  </div>\n                  <nav className=\"flex flex-1 flex-col\">\n                    <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n                      <li>\n                        <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                          {navigation.map((item) => (\n                            <li key={item.name}>\n                              <Link\n                                href={item.href}\n                                className={clsx(\n                                  pathname === item.href\n                                    ? 'bg-green-50 text-green-700'\n                                    : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                                  'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                                )}\n                              >\n                                <item.icon\n                                  className={clsx(\n                                    pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                                    'h-6 w-6 shrink-0'\n                                  )}\n                                  aria-hidden=\"true\"\n                                />\n                                {item.name}\n                              </Link>\n                            </li>\n                          ))}\n                        </ul>\n                      </li>\n                    </ul>\n                  </nav>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </Dialog>\n      </Transition.Root>\n\n      {/* Static sidebar for desktop */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 bg-white px-6 pb-4\">\n          <div className=\"flex h-16 shrink-0 items-center\">\n            <div className=\"h-8 w-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-sm\">SFF</span>\n            </div>\n            <span className=\"ml-2 text-xl font-semibold text-gray-900\">Production</span>\n          </div>\n          <nav className=\"flex flex-1 flex-col\">\n            <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n              <li>\n                <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                  {navigation.map((item) => (\n                    <li key={item.name}>\n                      <Link\n                        href={item.href}\n                        className={clsx(\n                          pathname === item.href\n                            ? 'bg-green-50 text-green-700'\n                            : 'text-gray-700 hover:text-green-700 hover:bg-green-50',\n                          'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'\n                        )}\n                      >\n                        <item.icon\n                          className={clsx(\n                            pathname === item.href ? 'text-green-700' : 'text-gray-400 group-hover:text-green-700',\n                            'h-6 w-6 shrink-0'\n                          )}\n                          aria-hidden=\"true\"\n                        />\n                        {item.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </li>\n            </ul>\n          </nav>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;;;AAjBA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,kNAAA,CAAA,WAAQ;IAAC;IAC/C;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,oPAAA,CAAA,4BAAyB;IAAC;IACrE;QAAE,MAAM;QAAc,MAAM;QAAe,MAAM,gNAAA,CAAA,UAAO;IAAC;IACzD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,sNAAA,CAAA,aAAU;IAAC;IACtD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,0NAAA,CAAA,eAAY;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kOAAA,CAAA,mBAAgB;IAAC;IAChE;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,oNAAA,CAAA,YAAS;IAAC;CAClD;AAOM,SAAS,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAgB;;IACrD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE;;0BAEE,6LAAC,0LAAA,CAAA,aAAU,CAAC,IAAI;gBAAC,MAAM;gBAAM,IAAI,6JAAA,CAAA,WAAQ;0BACvC,cAAA,6LAAC,kLAAA,CAAA,SAAM;oBAAC,IAAG;oBAAM,WAAU;oBAA0B,SAAS;;sCAC5D,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,6JAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;sCAGjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;gCACf,IAAI,6JAAA,CAAA,WAAQ;gCACZ,OAAM;gCACN,WAAU;gCACV,SAAQ;gCACR,OAAM;gCACN,WAAU;gCACV,SAAQ;0CAER,cAAA,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;oCAAC,WAAU;;sDACtB,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4CACf,IAAI,6JAAA,CAAA,WAAQ;4CACZ,OAAM;4CACN,WAAU;4CACV,SAAQ;4CACR,OAAM;4CACN,WAAU;4CACV,SAAQ;sDAER,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAO,MAAK;oDAAS,WAAU;oDAAe,SAAS,IAAM,QAAQ;;sEACpE,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;4DAAqB,eAAY;;;;;;;;;;;;;;;;;;;;;;sDAI5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,6LAAC;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;8DAE7D,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAG,MAAK;wDAAO,WAAU;kEACxB,cAAA,6LAAC;sEACC,cAAA,6LAAC;gEAAG,MAAK;gEAAO,WAAU;0EACvB,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;kFACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4EACH,MAAM,KAAK,IAAI;4EACf,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;8FAGF,6LAAC,KAAK,IAAI;oFACR,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;oFAEF,eAAY;;;;;;gFAEb,KAAK,IAAI;;;;;;;uEAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiCxC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAE7D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,MAAK;gCAAO,WAAU;0CACxB,cAAA,6LAAC;8CACC,cAAA,6LAAC;wCAAG,MAAK;wCAAO,WAAU;kDACvB,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAClB,+BACA,wDACJ;;sEAGF,6LAAC,KAAK,IAAI;4DACR,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aAAa,KAAK,IAAI,GAAG,mBAAmB,4CAC5C;4DAEF,eAAY;;;;;;wDAEb,KAAK,IAAI;;;;;;;+CAjBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BtC;GAtIgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Menu, Transition } from '@headlessui/react'\nimport { Bars3Icon, BellIcon } from '@heroicons/react/24/outline'\nimport { useAuth } from '@/components/providers'\nimport { clsx } from 'clsx'\n\ninterface HeaderProps {\n  setSidebarOpen: (open: boolean) => void\n}\n\nexport function Header({ setSidebarOpen }: HeaderProps) {\n  const { user, signOut } = useAuth()\n\n  const userNavigation = [\n    { name: 'Your profile', href: '#' },\n    { name: 'Settings', href: '#' },\n    { name: 'Sign out', href: '#', onClick: signOut },\n  ]\n\n  return (\n    <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n      <button\n        type=\"button\"\n        className=\"-m-2.5 p-2.5 text-gray-700 lg:hidden\"\n        onClick={() => setSidebarOpen(true)}\n      >\n        <span className=\"sr-only\">Open sidebar</span>\n        <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n      </button>\n\n      {/* Separator */}\n      <div className=\"h-6 w-px bg-gray-200 lg:hidden\" aria-hidden=\"true\" />\n\n      <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n        <div className=\"relative flex flex-1\">\n          {/* Search can be added here later */}\n        </div>\n        <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n          <button type=\"button\" className=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\">\n            <span className=\"sr-only\">View notifications</span>\n            <BellIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n\n          {/* Separator */}\n          <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200\" aria-hidden=\"true\" />\n\n          {/* Profile dropdown */}\n          <Menu as=\"div\" className=\"relative\">\n            <Menu.Button className=\"-m-1.5 flex items-center p-1.5\">\n              <span className=\"sr-only\">Open user menu</span>\n              <div className=\"h-8 w-8 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.username?.charAt(0).toUpperCase() || 'U'}\n                </span>\n              </div>\n              <span className=\"hidden lg:flex lg:items-center\">\n                <span className=\"ml-4 text-sm font-semibold leading-6 text-gray-900\" aria-hidden=\"true\">\n                  {user?.username || 'User'}\n                </span>\n                <span className=\"ml-2 text-xs text-gray-500 capitalize\">\n                  ({user?.role?.replace('_', ' ') || 'Role'})\n                </span>\n              </span>\n            </Menu.Button>\n            <Transition\n              as={Fragment}\n              enter=\"transition ease-out duration-100\"\n              enterFrom=\"transform opacity-0 scale-95\"\n              enterTo=\"transform opacity-100 scale-100\"\n              leave=\"transition ease-in duration-75\"\n              leaveFrom=\"transform opacity-100 scale-100\"\n              leaveTo=\"transform opacity-0 scale-95\"\n            >\n              <Menu.Items className=\"absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none\">\n                {userNavigation.map((item) => (\n                  <Menu.Item key={item.name}>\n                    {({ active }) => (\n                      <button\n                        onClick={item.onClick}\n                        className={clsx(\n                          active ? 'bg-gray-50' : '',\n                          'block w-full text-left px-3 py-1 text-sm leading-6 text-gray-900'\n                        )}\n                      >\n                        {item.name}\n                      </button>\n                    )}\n                  </Menu.Item>\n                ))}\n              </Menu.Items>\n            </Transition>\n          </Menu>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;;;AANA;;;;;;AAYO,SAAS,OAAO,EAAE,cAAc,EAAe;;IACpD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAAgB,MAAM;QAAI;QAClC;YAAE,MAAM;YAAY,MAAM;QAAI;QAC9B;YAAE,MAAM;YAAY,MAAM;YAAK,SAAS;QAAQ;KACjD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,eAAe;;kCAE9B,6LAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;wBAAU,eAAY;;;;;;;;;;;;0BAI7C,6LAAC;gBAAI,WAAU;gBAAiC,eAAY;;;;;;0BAE5D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,MAAK;gCAAS,WAAU;;kDAC9B,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;gCAAgD,eAAY;;;;;;0CAG3E,6LAAC,8KAAA,CAAA,OAAI;gCAAC,IAAG;gCAAM,WAAU;;kDACvB,6LAAC,8KAAA,CAAA,OAAI,CAAC,MAAM;wCAAC,WAAU;;0DACrB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb,MAAM,UAAU,OAAO,GAAG,iBAAiB;;;;;;;;;;;0DAGhD,6LAAC;gDAAK,WAAU;;kEACd,6LAAC;wDAAK,WAAU;wDAAqD,eAAY;kEAC9E,MAAM,YAAY;;;;;;kEAErB,6LAAC;wDAAK,WAAU;;4DAAwC;4DACpD,MAAM,MAAM,QAAQ,KAAK,QAAQ;4DAAO;;;;;;;;;;;;;;;;;;;kDAIhD,6LAAC,0LAAA,CAAA,aAAU;wCACT,IAAI,6JAAA,CAAA,WAAQ;wCACZ,OAAM;wCACN,WAAU;wCACV,SAAQ;wCACR,OAAM;wCACN,WAAU;wCACV,SAAQ;kDAER,cAAA,6LAAC,8KAAA,CAAA,OAAI,CAAC,KAAK;4CAAC,WAAU;sDACnB,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;8DACP,CAAC,EAAE,MAAM,EAAE,iBACV,6LAAC;4DACC,SAAS,KAAK,OAAO;4DACrB,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,SAAS,eAAe,IACxB;sEAGD,KAAK,IAAI;;;;;;mDATA,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqB3C;GAtFgB;;QACY,kIAAA,CAAA,UAAO;;;KADnB", "debugId": null}}, {"offset": {"line": 680, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/components/providers'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const { user } = useAuth()\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Sidebar open={sidebarOpen} setOpen={setSidebarOpen} />\n      \n      <div className=\"lg:pl-72\">\n        <Header setSidebarOpen={setSidebarOpen} />\n        \n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAWO,SAAS,WAAW,EAAE,QAAQ,EAAmB;;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAO;gBAAC,MAAM;gBAAa,SAAS;;;;;;0BAErC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,SAAM;wBAAC,gBAAgB;;;;;;kCAExB,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAnBgB;;QAEG,kIAAA,CAAA,UAAO;;;KAFV", "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sff%20food%20flavo/sff-management-system/src/app/reports/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { useAuth } from '@/components/providers'\nimport { \n  ChartBarIcon, \n  DocumentArrowDownIcon, \n  CurrencyDollarIcon,\n  CubeIcon,\n  CogIcon,\n  BeakerIcon,\n  CalendarIcon\n} from '@heroicons/react/24/outline'\n\ninterface ReportCard {\n  id: string\n  title: string\n  description: string\n  icon: any\n  category: 'production' | 'inventory' | 'quality' | 'financial'\n  color: string\n  bgColor: string\n}\n\nexport default function ReportsPage() {\n  const { user } = useAuth()\n  const [selectedCategory, setSelectedCategory] = useState<'all' | 'production' | 'inventory' | 'quality' | 'financial'>('all')\n\n  const reportCards: ReportCard[] = [\n    {\n      id: '1',\n      title: 'Production Efficiency Report',\n      description: 'Analyze production performance, yield rates, and batch completion times',\n      icon: CogIcon,\n      category: 'production',\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-50 hover:bg-blue-100'\n    },\n    {\n      id: '2',\n      title: 'Inventory Turnover Analysis',\n      description: 'Track inventory movement, stock levels, and usage patterns',\n      icon: CubeIcon,\n      category: 'inventory',\n      color: 'text-green-600',\n      bgColor: 'bg-green-50 hover:bg-green-100'\n    },\n    {\n      id: '3',\n      title: 'Quality Compliance Report',\n      description: 'Monitor quality metrics, test results, and compliance status',\n      icon: BeakerIcon,\n      category: 'quality',\n      color: 'text-purple-600',\n      bgColor: 'bg-purple-50 hover:bg-purple-100'\n    },\n    {\n      id: '4',\n      title: 'Cost Analysis Report',\n      description: 'Detailed breakdown of production costs and material expenses',\n      icon: CurrencyDollarIcon,\n      category: 'financial',\n      color: 'text-yellow-600',\n      bgColor: 'bg-yellow-50 hover:bg-yellow-100'\n    },\n    {\n      id: '5',\n      title: 'Monthly Production Summary',\n      description: 'Comprehensive overview of monthly production activities',\n      icon: ChartBarIcon,\n      category: 'production',\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-50 hover:bg-blue-100'\n    },\n    {\n      id: '6',\n      title: 'Low Stock Alert Report',\n      description: 'Items requiring immediate attention and reordering',\n      icon: CubeIcon,\n      category: 'inventory',\n      color: 'text-red-600',\n      bgColor: 'bg-red-50 hover:bg-red-100'\n    },\n    {\n      id: '7',\n      title: 'Recipe Cost Breakdown',\n      description: 'Detailed cost analysis for each recipe and formulation',\n      icon: CurrencyDollarIcon,\n      category: 'financial',\n      color: 'text-green-600',\n      bgColor: 'bg-green-50 hover:bg-green-100'\n    },\n    {\n      id: '8',\n      title: 'Quality Test Results',\n      description: 'Laboratory test results and quality metrics overview',\n      icon: BeakerIcon,\n      category: 'quality',\n      color: 'text-purple-600',\n      bgColor: 'bg-purple-50 hover:bg-purple-100'\n    },\n    {\n      id: '9',\n      title: 'Batch Tracking Report',\n      description: 'Complete traceability from raw materials to finished products',\n      icon: CogIcon,\n      category: 'production',\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-50 hover:bg-blue-100'\n    }\n  ]\n\n  const categories = [\n    { key: 'all', name: 'All Reports', count: reportCards.length },\n    { key: 'production', name: 'Production', count: reportCards.filter(r => r.category === 'production').length },\n    { key: 'inventory', name: 'Inventory', count: reportCards.filter(r => r.category === 'inventory').length },\n    { key: 'quality', name: 'Quality', count: reportCards.filter(r => r.category === 'quality').length },\n    { key: 'financial', name: 'Financial', count: reportCards.filter(r => r.category === 'financial').length }\n  ]\n\n  const filteredReports = selectedCategory === 'all' \n    ? reportCards \n    : reportCards.filter(report => report.category === selectedCategory)\n\n  if (!user) {\n    return null\n  }\n\n  return (\n    <MainLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"sm:flex sm:items-center sm:justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Reports & Analytics</h1>\n            <p className=\"mt-2 text-sm text-gray-700\">\n              Generate comprehensive reports and analyze your production data\n            </p>\n          </div>\n          <div className=\"mt-4 sm:mt-0 flex space-x-3\">\n            <button\n              type=\"button\"\n              className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n            >\n              <CalendarIcon className=\"-ml-1 mr-2 h-5 w-5\" aria-hidden=\"true\" />\n              Date Range\n            </button>\n            <button\n              type=\"button\"\n              className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n            >\n              <DocumentArrowDownIcon className=\"-ml-1 mr-2 h-5 w-5\" aria-hidden=\"true\" />\n              Export All\n            </button>\n          </div>\n        </div>\n\n        {/* Category Tabs */}\n        <div className=\"bg-white shadow rounded-lg\">\n          <div className=\"border-b border-gray-200\">\n            <nav className=\"-mb-px flex space-x-8 px-6\" aria-label=\"Tabs\">\n              {categories.map((category) => (\n                <button\n                  key={category.key}\n                  onClick={() => setSelectedCategory(category.key as any)}\n                  className={`${\n                    selectedCategory === category.key\n                      ? 'border-green-500 text-green-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}\n                >\n                  {category.name}\n                  <span className={`ml-2 py-0.5 px-2 rounded-full text-xs ${\n                    selectedCategory === category.key\n                      ? 'bg-green-100 text-green-600'\n                      : 'bg-gray-100 text-gray-500'\n                  }`}>\n                    {category.count}\n                  </span>\n                </button>\n              ))}\n            </nav>\n          </div>\n        </div>\n\n        {/* Quick Stats */}\n        <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-4\">\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <ChartBarIcon className=\"h-6 w-6 text-blue-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Reports Generated</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">247</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <DocumentArrowDownIcon className=\"h-6 w-6 text-green-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">This Month</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">23</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <CalendarIcon className=\"h-6 w-6 text-purple-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Scheduled</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">8</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <CurrencyDollarIcon className=\"h-6 w-6 text-yellow-600\" aria-hidden=\"true\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Cost Savings</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">$12.4K</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Reports Grid */}\n        <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3\">\n          {filteredReports.map((report) => {\n            const IconComponent = report.icon\n            return (\n              <div\n                key={report.id}\n                className={`relative group bg-white p-6 rounded-lg shadow hover:shadow-lg transition-all duration-200 cursor-pointer border border-gray-200 ${report.bgColor}`}\n              >\n                <div>\n                  <span className={`rounded-lg inline-flex p-3 ring-4 ring-white ${report.bgColor.replace('hover:', '')}`}>\n                    <IconComponent className={`h-6 w-6 ${report.color}`} aria-hidden=\"true\" />\n                  </span>\n                </div>\n                <div className=\"mt-8\">\n                  <h3 className=\"text-lg font-medium text-gray-900 group-hover:text-gray-600\">\n                    <span className=\"absolute inset-0\" aria-hidden=\"true\" />\n                    {report.title}\n                  </h3>\n                  <p className=\"mt-2 text-sm text-gray-500\">\n                    {report.description}\n                  </p>\n                </div>\n                <div className=\"mt-6 flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${\n                      report.category === 'production' ? 'bg-blue-100 text-blue-800' :\n                      report.category === 'inventory' ? 'bg-green-100 text-green-800' :\n                      report.category === 'quality' ? 'bg-purple-100 text-purple-800' :\n                      'bg-yellow-100 text-yellow-800'\n                    }`}>\n                      {report.category}\n                    </span>\n                  </div>\n                  <div className=\"flex space-x-2\">\n                    <button className=\"text-sm text-green-600 hover:text-green-500 font-medium\">\n                      Generate\n                    </button>\n                    <button className=\"text-sm text-gray-500 hover:text-gray-400\">\n                      <DocumentArrowDownIcon className=\"h-4 w-4\" />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )\n          })}\n        </div>\n\n        {/* Recent Reports */}\n        <div className=\"bg-white shadow rounded-lg\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n              Recent Reports\n            </h3>\n            <div className=\"space-y-3\">\n              {[\n                { name: 'Production Efficiency Report - November 2024', date: '2024-12-01', size: '2.4 MB' },\n                { name: 'Inventory Turnover Analysis - Q4 2024', date: '2024-11-28', size: '1.8 MB' },\n                { name: 'Quality Compliance Report - Week 48', date: '2024-11-25', size: '3.1 MB' },\n                { name: 'Cost Analysis Report - November 2024', date: '2024-11-22', size: '1.2 MB' }\n              ].map((report, index) => (\n                <div key={index} className=\"flex items-center justify-between py-3 border-b border-gray-200 last:border-b-0\">\n                  <div className=\"flex items-center space-x-3\">\n                    <DocumentArrowDownIcon className=\"h-5 w-5 text-gray-400\" />\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900\">{report.name}</p>\n                      <p className=\"text-sm text-gray-500\">{report.date} • {report.size}</p>\n                    </div>\n                  </div>\n                  <div className=\"flex space-x-2\">\n                    <button className=\"text-sm text-green-600 hover:text-green-500\">\n                      Download\n                    </button>\n                    <button className=\"text-sm text-gray-500 hover:text-gray-400\">\n                      View\n                    </button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </MainLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAyBe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgE;IAEvH,MAAM,cAA4B;QAChC;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,gNAAA,CAAA,UAAO;YACb,UAAU;YACV,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,kNAAA,CAAA,WAAQ;YACd,UAAU;YACV,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,sNAAA,CAAA,aAAU;YAChB,UAAU;YACV,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,sOAAA,CAAA,qBAAkB;YACxB,UAAU;YACV,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,0NAAA,CAAA,eAAY;YAClB,UAAU;YACV,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,kNAAA,CAAA,WAAQ;YACd,UAAU;YACV,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,sOAAA,CAAA,qBAAkB;YACxB,UAAU;YACV,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,sNAAA,CAAA,aAAU;YAChB,UAAU;YACV,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,gNAAA,CAAA,UAAO;YACb,UAAU;YACV,OAAO;YACP,SAAS;QACX;KACD;IAED,MAAM,aAAa;QACjB;YAAE,KAAK;YAAO,MAAM;YAAe,OAAO,YAAY,MAAM;QAAC;QAC7D;YAAE,KAAK;YAAc,MAAM;YAAc,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,cAAc,MAAM;QAAC;QAC5G;YAAE,KAAK;YAAa,MAAM;YAAa,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,aAAa,MAAM;QAAC;QACzG;YAAE,KAAK;YAAW,MAAM;YAAW,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,WAAW,MAAM;QAAC;QACnG;YAAE,KAAK;YAAa,MAAM;YAAa,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,aAAa,MAAM;QAAC;KAC1G;IAED,MAAM,kBAAkB,qBAAqB,QACzC,cACA,YAAY,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,KAAK;IAErD,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC,iJAAA,CAAA,aAAU;kBACT,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,WAAU;;sDAEV,6LAAC,0NAAA,CAAA,eAAY;4CAAC,WAAU;4CAAqB,eAAY;;;;;;wCAAS;;;;;;;8CAGpE,6LAAC;oCACC,MAAK;oCACL,WAAU;;sDAEV,6LAAC,4OAAA,CAAA,wBAAqB;4CAAC,WAAU;4CAAqB,eAAY;;;;;;wCAAS;;;;;;;;;;;;;;;;;;;8BAOjF,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAA6B,cAAW;sCACpD,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;oCAEC,SAAS,IAAM,oBAAoB,SAAS,GAAG;oCAC/C,WAAW,GACT,qBAAqB,SAAS,GAAG,GAC7B,oCACA,6EACL,6EAA6E,CAAC;;wCAE9E,SAAS,IAAI;sDACd,6LAAC;4CAAK,WAAW,CAAC,sCAAsC,EACtD,qBAAqB,SAAS,GAAG,GAC7B,gCACA,6BACJ;sDACC,SAAS,KAAK;;;;;;;mCAdZ,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;8BAuB3B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,0NAAA,CAAA,eAAY;gDAAC,WAAU;gDAAwB,eAAY;;;;;;;;;;;sDAE9D,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,4OAAA,CAAA,wBAAqB;gDAAC,WAAU;gDAAyB,eAAY;;;;;;;;;;;sDAExE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,0NAAA,CAAA,eAAY;gDAAC,WAAU;gDAA0B,eAAY;;;;;;;;;;;sDAEhE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,sOAAA,CAAA,qBAAkB;gDAAC,WAAU;gDAA0B,eAAY;;;;;;;;;;;sDAEtE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS9D,6LAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC;wBACpB,MAAM,gBAAgB,OAAO,IAAI;wBACjC,qBACE,6LAAC;4BAEC,WAAW,CAAC,gIAAgI,EAAE,OAAO,OAAO,EAAE;;8CAE9J,6LAAC;8CACC,cAAA,6LAAC;wCAAK,WAAW,CAAC,6CAA6C,EAAE,OAAO,OAAO,CAAC,OAAO,CAAC,UAAU,KAAK;kDACrG,cAAA,6LAAC;4CAAc,WAAW,CAAC,QAAQ,EAAE,OAAO,KAAK,EAAE;4CAAE,eAAY;;;;;;;;;;;;;;;;8CAGrE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;oDAAmB,eAAY;;;;;;gDAC9C,OAAO,KAAK;;;;;;;sDAEf,6LAAC;4CAAE,WAAU;sDACV,OAAO,WAAW;;;;;;;;;;;;8CAGvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAW,CAAC,mFAAmF,EACnG,OAAO,QAAQ,KAAK,eAAe,8BACnC,OAAO,QAAQ,KAAK,cAAc,gCAClC,OAAO,QAAQ,KAAK,YAAY,kCAChC,iCACA;0DACC,OAAO,QAAQ;;;;;;;;;;;sDAGpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAO,WAAU;8DAA0D;;;;;;8DAG5E,6LAAC;oDAAO,WAAU;8DAChB,cAAA,6LAAC,4OAAA,CAAA,wBAAqB;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;2BAjClC,OAAO,EAAE;;;;;oBAuCpB;;;;;;8BAIF,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM;wCAAgD,MAAM;wCAAc,MAAM;oCAAS;oCAC3F;wCAAE,MAAM;wCAAyC,MAAM;wCAAc,MAAM;oCAAS;oCACpF;wCAAE,MAAM;wCAAuC,MAAM;wCAAc,MAAM;oCAAS;oCAClF;wCAAE,MAAM;wCAAwC,MAAM;wCAAc,MAAM;oCAAS;iCACpF,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACb,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,4OAAA,CAAA,wBAAqB;wDAAC,WAAU;;;;;;kEACjC,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAqC,OAAO,IAAI;;;;;;0EAC7D,6LAAC;gEAAE,WAAU;;oEAAyB,OAAO,IAAI;oEAAC;oEAAI,OAAO,IAAI;;;;;;;;;;;;;;;;;;;0DAGrE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAO,WAAU;kEAA8C;;;;;;kEAGhE,6LAAC;wDAAO,WAAU;kEAA4C;;;;;;;;;;;;;uCAZxD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwB1B;GAzTwB;;QACL,kIAAA,CAAA,UAAO;;;KADF", "debugId": null}}]}