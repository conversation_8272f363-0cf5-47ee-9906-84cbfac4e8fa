'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { useAuth } from '@/components/providers'
import { Modal } from '@/components/ui/modal'
import { PlusIcon, BeakerIcon, DocumentTextIcon, ShieldCheckIcon, ClipboardDocumentCheckIcon } from '@heroicons/react/24/outline'

interface QualityDocument {
  id: string
  document_type: 'msds' | 'coa' | 'tds' | 'quality_spec' | 'lab_report'
  title: string
  document_number: string
  version: string
  item_name?: string
  batch_number?: string
  status: 'draft' | 'review' | 'approved' | 'expired'
  valid_from?: string
  valid_until?: string
  created_at: string
  created_by: string
}

export default function QualityPage() {
  const { user } = useAuth()
  const [documents, setDocuments] = useState<QualityDocument[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'all' | 'msds' | 'coa' | 'tds' | 'lab_reports'>('all')
  const [showAddModal, setShowAddModal] = useState(false)
  const [newDocument, setNewDocument] = useState({
    document_type: 'msds' as 'msds' | 'coa' | 'tds' | 'quality_spec' | 'lab_report',
    title: '',
    document_number: '',
    version: '1.0',
    item_name: '',
    batch_number: ''
  })

  useEffect(() => {
    // Mock quality documents data
    const mockDocuments: QualityDocument[] = [
      {
        id: '1',
        document_type: 'msds',
        title: 'Vanilla Extract Safety Data Sheet',
        document_number: 'MSDS-VAN-001',
        version: '2.1',
        item_name: 'Vanilla Extract',
        status: 'approved',
        valid_from: '2024-01-01',
        valid_until: '2025-12-31',
        created_at: '2024-12-10T10:00:00Z',
        created_by: 'Sarah Johnson'
      },
      {
        id: '2',
        document_type: 'coa',
        title: 'Certificate of Analysis - Batch SFF-20241211-001',
        document_number: 'COA-SFF-20241211-001',
        version: '1.0',
        batch_number: 'SFF-20241211-001',
        status: 'approved',
        valid_from: '2024-12-11',
        created_at: '2024-12-11T16:00:00Z',
        created_by: 'Mike Wilson'
      },
      {
        id: '3',
        document_type: 'tds',
        title: 'Strawberry Flavor Technical Data Sheet',
        document_number: 'TDS-STR-001',
        version: '1.3',
        item_name: 'Strawberry Flavor',
        status: 'approved',
        valid_from: '2024-11-01',
        valid_until: '2025-10-31',
        created_at: '2024-11-15T14:30:00Z',
        created_by: 'Emily Davis'
      },
      {
        id: '4',
        document_type: 'lab_report',
        title: 'Microbiological Testing Report',
        document_number: 'LAB-20241210-003',
        version: '1.0',
        batch_number: 'SFF-20241211-001',
        status: 'review',
        created_at: '2024-12-10T09:15:00Z',
        created_by: 'John Smith'
      },
      {
        id: '5',
        document_type: 'quality_spec',
        title: 'Citrus Burst Quality Specifications',
        document_number: 'QS-CBF-001',
        version: '2.0',
        item_name: 'Citrus Burst Flavor',
        status: 'approved',
        valid_from: '2024-12-01',
        valid_until: '2025-11-30',
        created_at: '2024-12-01T11:20:00Z',
        created_by: 'Sarah Johnson'
      },
      {
        id: '6',
        document_type: 'msds',
        title: 'Propylene Glycol Safety Data Sheet',
        document_number: 'MSDS-PG-001',
        version: '1.0',
        item_name: 'Propylene Glycol',
        status: 'draft',
        created_at: '2024-12-09T13:45:00Z',
        created_by: 'Emily Davis'
      }
    ]

    setTimeout(() => {
      setDocuments(mockDocuments)
      setLoading(false)
    }, 1000)
  }, [])

  const handleAddDocument = () => {
    const doc: QualityDocument = {
      id: (documents.length + 1).toString(),
      document_type: newDocument.document_type,
      title: newDocument.title,
      document_number: newDocument.document_number,
      version: newDocument.version,
      item_name: newDocument.item_name || undefined,
      batch_number: newDocument.batch_number || undefined,
      status: 'draft',
      created_at: new Date().toISOString(),
      created_by: user?.username || 'Current User'
    }

    setDocuments([...documents, doc])
    setShowAddModal(false)
    setNewDocument({
      document_type: 'msds',
      title: '',
      document_number: '',
      version: '1.0',
      item_name: '',
      batch_number: ''
    })
  }

  const getDocumentTypeIcon = (type: string) => {
    switch (type) {
      case 'msds':
        return ShieldCheckIcon
      case 'coa':
        return ClipboardDocumentCheckIcon
      case 'tds':
        return DocumentTextIcon
      case 'lab_report':
        return BeakerIcon
      case 'quality_spec':
        return ClipboardDocumentCheckIcon
      default:
        return DocumentTextIcon
    }
  }

  const getDocumentTypeName = (type: string) => {
    switch (type) {
      case 'msds':
        return 'MSDS'
      case 'coa':
        return 'COA'
      case 'tds':
        return 'TDS'
      case 'lab_report':
        return 'Lab Report'
      case 'quality_spec':
        return 'Quality Spec'
      default:
        return type.toUpperCase()
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800'
      case 'review':
        return 'bg-yellow-100 text-yellow-800'
      case 'draft':
        return 'bg-gray-100 text-gray-800'
      case 'expired':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredDocuments = activeTab === 'all' 
    ? documents 
    : documents.filter(doc => {
        if (activeTab === 'lab_reports') return doc.document_type === 'lab_report'
        return doc.document_type === activeTab
      })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (!user) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Quality Management</h1>
            <p className="mt-2 text-sm text-gray-700">
              Manage quality documents, certificates, and compliance records
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button
              type="button"
              onClick={() => setShowAddModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
              New Document
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ShieldCheckIcon className="h-6 w-6 text-red-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">MSDS</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {documents.filter(d => d.document_type === 'msds').length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ClipboardDocumentCheckIcon className="h-6 w-6 text-green-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">COA</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {documents.filter(d => d.document_type === 'coa').length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DocumentTextIcon className="h-6 w-6 text-blue-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">TDS</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {documents.filter(d => d.document_type === 'tds').length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <BeakerIcon className="h-6 w-6 text-purple-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Lab Reports</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {documents.filter(d => d.document_type === 'lab_report').length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white shadow rounded-lg">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
              {[
                { key: 'all', name: 'All Documents' },
                { key: 'msds', name: 'MSDS' },
                { key: 'coa', name: 'COA' },
                { key: 'tds', name: 'TDS' },
                { key: 'lab_reports', name: 'Lab Reports' }
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={`${
                    activeTab === tab.key
                      ? 'border-green-500 text-green-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Documents Table */}
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              {activeTab === 'all' ? 'All Documents' : activeTab.toUpperCase()} ({filteredDocuments.length})
            </h3>
            
            {loading ? (
              <div className="animate-pulse">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-4 py-4">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Document
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Related Item
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Valid Until
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created
                      </th>
                      <th className="relative px-6 py-3">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredDocuments.map((doc) => {
                      const IconComponent = getDocumentTypeIcon(doc.document_type)
                      return (
                        <tr key={doc.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <IconComponent className="h-5 w-5 text-gray-400 mr-3" />
                              <div>
                                <div className="text-sm font-medium text-gray-900">{doc.title}</div>
                                <div className="text-sm text-gray-500">
                                  {doc.document_number} • v{doc.version}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {getDocumentTypeName(doc.document_type)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {doc.item_name || doc.batch_number || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(doc.status)}`}>
                              {doc.status.toUpperCase()}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {doc.valid_until ? formatDate(doc.valid_until) : '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatDate(doc.created_at)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button className="text-green-600 hover:text-green-900 mr-3">
                              View
                            </button>
                            <button className="text-green-600 hover:text-green-900">
                              Edit
                            </button>
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        {/* Add Document Modal */}
        <Modal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          title="Create New Quality Document"
          maxWidth="xl"
        >
          <form onSubmit={(e) => { e.preventDefault(); handleAddDocument(); }} className="space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label htmlFor="document_type" className="block text-sm font-medium text-gray-700">
                  Document Type
                </label>
                <select
                  id="document_type"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newDocument.document_type}
                  onChange={(e) => setNewDocument({ ...newDocument, document_type: e.target.value as any })}
                >
                  <option value="msds">MSDS - Material Safety Data Sheet</option>
                  <option value="coa">COA - Certificate of Analysis</option>
                  <option value="tds">TDS - Technical Data Sheet</option>
                  <option value="lab_report">Lab Report</option>
                  <option value="quality_spec">Quality Specification</option>
                </select>
              </div>
              <div>
                <label htmlFor="document_number" className="block text-sm font-medium text-gray-700">
                  Document Number
                </label>
                <input
                  type="text"
                  id="document_number"
                  required
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newDocument.document_number}
                  onChange={(e) => setNewDocument({ ...newDocument, document_number: e.target.value })}
                />
              </div>
              <div className="sm:col-span-2">
                <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                  Document Title
                </label>
                <input
                  type="text"
                  id="title"
                  required
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newDocument.title}
                  onChange={(e) => setNewDocument({ ...newDocument, title: e.target.value })}
                />
              </div>
              <div>
                <label htmlFor="version" className="block text-sm font-medium text-gray-700">
                  Version
                </label>
                <input
                  type="text"
                  id="version"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newDocument.version}
                  onChange={(e) => setNewDocument({ ...newDocument, version: e.target.value })}
                />
              </div>
              <div>
                <label htmlFor="item_name" className="block text-sm font-medium text-gray-700">
                  Related Item (Optional)
                </label>
                <input
                  type="text"
                  id="item_name"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newDocument.item_name}
                  onChange={(e) => setNewDocument({ ...newDocument, item_name: e.target.value })}
                />
              </div>
              <div className="sm:col-span-2">
                <label htmlFor="batch_number" className="block text-sm font-medium text-gray-700">
                  Related Batch Number (Optional)
                </label>
                <select
                  id="batch_number"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newDocument.batch_number}
                  onChange={(e) => setNewDocument({ ...newDocument, batch_number: e.target.value })}
                >
                  <option value="">Select Batch (Optional)</option>
                  <option value="SFF-20241211-001">SFF-20241211-001 - Strawberry Vanilla Blend</option>
                  <option value="SFF-20241211-002">SFF-20241211-002 - Classic Vanilla Extract</option>
                  <option value="SFF-20241212-001">SFF-20241212-001 - Citrus Burst Flavor</option>
                </select>
              </div>
            </div>
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={() => setShowAddModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700"
              >
                Create Document
              </button>
            </div>
          </form>
        </Modal>
      </div>
    </MainLayout>
  )
}
