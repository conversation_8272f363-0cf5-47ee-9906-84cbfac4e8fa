'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { useAuth } from '@/components/providers'
import { Modal } from '@/components/ui/modal'
import { DatabaseService, InventoryCategory } from '@/lib/database'
import {
  PlusIcon,
  MagnifyingGlassIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XMarkIcon,
  EyeIcon,
  DocumentDuplicateIcon,
  CurrencyDollarIcon,
  ArchiveBoxIcon,
  ClipboardDocumentListIcon
} from '@heroicons/react/24/outline'

interface InventoryItem {
  id: string
  name: string
  sku: string
  category_id: string
  category_name?: string
  current_stock: number
  minimum_stock: number
  unit_of_measure: string
  unit_cost: number
  description?: string
  is_active: boolean
  status?: 'in_stock' | 'low_stock' | 'out_of_stock'
}

export default function InventoryPage() {
  const { user } = useAuth()
  const [items, setItems] = useState<InventoryItem[]>([])
  const [categories, setCategories] = useState<InventoryCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [showAddModal, setShowAddModal] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)
  const [isCreating, setIsCreating] = useState(false)
  const [validationErrors, setValidationErrors] = useState<string[]>([])

  // Enhanced new item state with more professional fields
  const [newItem, setNewItem] = useState({
    name: '',
    sku: '',
    category_id: '',
    current_stock: 0,
    minimum_stock: 0,
    maximum_stock: 0,
    reorder_point: 0,
    unit_of_measure: 'liters',
    unit_cost: 0,
    supplier_name: '',
    supplier_code: '',
    batch_number: '',
    expiry_date: '',
    location: '',
    description: '',
    notes: '',
    is_hazardous: false,
    requires_refrigeration: false
  })

  // Filter and sort states
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [sortBy, setSortBy] = useState('name')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true)

        // Load inventory items and categories from database
        const [inventoryItems, inventoryCategories] = await Promise.all([
          DatabaseService.getInventoryItems(),
          DatabaseService.getInventoryCategories()
        ])

        // Process inventory items to add status and category name
        const processedItems = inventoryItems.map(item => ({
          ...item,
          current_stock: parseFloat(item.current_stock.toString()),
          minimum_stock: parseFloat(item.minimum_stock.toString()),
          unit_cost: parseFloat(item.unit_cost.toString()),
          status: parseFloat(item.current_stock.toString()) <= parseFloat(item.minimum_stock.toString())
            ? 'low_stock' as const
            : parseFloat(item.current_stock.toString()) === 0
            ? 'out_of_stock' as const
            : 'in_stock' as const,
          category_name: inventoryCategories.find(cat => cat.id === item.category_id)?.name || 'Unknown'
        }))

        setItems(processedItems)
        setCategories(inventoryCategories)

        // Set default category for new items
        if (inventoryCategories.length > 0 && !newItem.category_id) {
          setNewItem(prev => ({ ...prev, category_id: inventoryCategories[0].id }))
        }
      } catch (error) {
        console.error('Error loading inventory data:', error)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  // Enhanced filtering and sorting
  const filteredItems = items
    .filter(item => {
      // Search filter
      const matchesSearch = searchTerm === '' ||
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.category_name && item.category_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()))

      // Category filter
      const matchesCategory = selectedCategory === 'all' || item.category_id === selectedCategory

      // Status filter
      const matchesStatus = selectedStatus === 'all' || item.status === selectedStatus

      return matchesSearch && matchesCategory && matchesStatus
    })
    .sort((a, b) => {
      let aValue: any = a[sortBy as keyof InventoryItem]
      let bValue: any = b[sortBy as keyof InventoryItem]

      // Handle different data types
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (sortDirection === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
      }
    })

  // Calculate inventory statistics
  const inventoryStats = {
    totalItems: items.length,
    totalValue: items.reduce((sum, item) => sum + (item.current_stock * item.unit_cost), 0),
    lowStockItems: items.filter(item => item.status === 'low_stock').length,
    outOfStockItems: items.filter(item => item.status === 'out_of_stock').length,
    inStockItems: items.filter(item => item.status === 'in_stock').length
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in_stock':
        return 'bg-green-100 text-green-800'
      case 'low_stock':
        return 'bg-yellow-100 text-yellow-800'
      case 'out_of_stock':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'in_stock':
        return 'In Stock'
      case 'low_stock':
        return 'Low Stock'
      case 'out_of_stock':
        return 'Out of Stock'
      default:
        return 'Unknown'
    }
  }

  // Professional validation function
  const validateItem = () => {
    const errors: string[] = []

    // Required field validation
    if (!newItem.name.trim()) errors.push('Item name is required')
    if (!newItem.sku.trim()) errors.push('SKU is required')
    if (!newItem.category_id) errors.push('Category selection is required')
    if (!newItem.unit_of_measure) errors.push('Unit of measure is required')

    // Business logic validation
    if (newItem.name.length < 2) errors.push('Item name must be at least 2 characters')
    if (newItem.sku.length < 3) errors.push('SKU must be at least 3 characters')
    if (newItem.current_stock < 0) errors.push('Current stock cannot be negative')
    if (newItem.minimum_stock < 0) errors.push('Minimum stock cannot be negative')
    if (newItem.maximum_stock > 0 && newItem.maximum_stock < newItem.minimum_stock) {
      errors.push('Maximum stock must be greater than minimum stock')
    }
    if (newItem.reorder_point < 0) errors.push('Reorder point cannot be negative')
    if (newItem.unit_cost < 0) errors.push('Unit cost cannot be negative')

    // SKU uniqueness check
    if (items.some(item => item.sku.toLowerCase() === newItem.sku.toLowerCase())) {
      errors.push('SKU already exists')
    }

    // Date validation
    if (newItem.expiry_date && new Date(newItem.expiry_date) <= new Date()) {
      errors.push('Expiry date must be in the future')
    }

    // Supplier validation
    if (newItem.supplier_name && newItem.supplier_name.length < 2) {
      errors.push('Supplier name must be at least 2 characters')
    }

    setValidationErrors(errors)
    return errors.length === 0
  }

  // Generate SKU automatically
  const generateSKU = () => {
    const category = categories.find(cat => cat.id === newItem.category_id)
    const categoryCode = category ? category.name.substring(0, 3).toUpperCase() : 'GEN'
    const timestamp = Date.now().toString().slice(-6)
    const randomNum = Math.floor(Math.random() * 100).toString().padStart(2, '0')
    return `${categoryCode}-${timestamp}-${randomNum}`
  }

  // Auto-generate SKU when category changes
  useEffect(() => {
    if (newItem.category_id && !newItem.sku) {
      setNewItem(prev => ({ ...prev, sku: generateSKU() }))
    }
  }, [newItem.category_id])

  // Professional item creation with comprehensive data
  const handleAddItem = async () => {
    if (!validateItem()) return

    try {
      setIsCreating(true)

      const newInventoryItem = {
        name: newItem.name.trim(),
        sku: newItem.sku.toUpperCase().trim(),
        category_id: newItem.category_id,
        current_stock: newItem.current_stock,
        minimum_stock: newItem.minimum_stock,
        maximum_stock: newItem.maximum_stock || null,
        reorder_point: newItem.reorder_point || newItem.minimum_stock,
        unit_of_measure: newItem.unit_of_measure,
        unit_cost: newItem.unit_cost,
        supplier_name: newItem.supplier_name || null,
        supplier_code: newItem.supplier_code || null,
        batch_number: newItem.batch_number || null,
        expiry_date: newItem.expiry_date || null,
        location: newItem.location || null,
        description: newItem.description || null,
        notes: newItem.notes || null,
        is_hazardous: newItem.is_hazardous,
        requires_refrigeration: newItem.requires_refrigeration,
        is_active: true
      }

      const createdItem = await DatabaseService.createInventoryItem(newInventoryItem)

      if (createdItem) {
        // Add the new item to the local state with processed data
        const processedItem = {
          ...createdItem,
          current_stock: parseFloat(createdItem.current_stock.toString()),
          minimum_stock: parseFloat(createdItem.minimum_stock.toString()),
          unit_cost: parseFloat(createdItem.unit_cost.toString()),
          status: parseFloat(createdItem.current_stock.toString()) <= parseFloat(createdItem.minimum_stock.toString())
            ? 'low_stock' as const
            : parseFloat(createdItem.current_stock.toString()) === 0
            ? 'out_of_stock' as const
            : 'in_stock' as const,
          category_name: categories.find(cat => cat.id === createdItem.category_id)?.name || 'Unknown'
        }

        setItems([...items, processedItem])
        resetForm()
        setShowAddModal(false)

        alert(`✅ Inventory item "${newItem.name}" created successfully!\n\n📋 Item Details:\n• SKU: ${newItem.sku}\n• Category: ${categories.find(cat => cat.id === newItem.category_id)?.name}\n• Current Stock: ${newItem.current_stock} ${newItem.unit_of_measure}\n• Unit Cost: $${newItem.unit_cost.toFixed(2)}\n• Total Value: $${(newItem.current_stock * newItem.unit_cost).toFixed(2)}`)
      } else {
        alert('❌ Failed to create inventory item. Please try again.')
      }
    } catch (error) {
      alert('❌ Error creating inventory item. Please check the form and try again.')
    } finally {
      setIsCreating(false)
    }
  }

  // Reset form function
  const resetForm = () => {
    setCurrentStep(1)
    setNewItem({
      name: '',
      sku: '',
      category_id: categories.length > 0 ? categories[0].id : '',
      current_stock: 0,
      minimum_stock: 0,
      maximum_stock: 0,
      reorder_point: 0,
      unit_of_measure: 'liters',
      unit_cost: 0,
      supplier_name: '',
      supplier_code: '',
      batch_number: '',
      expiry_date: '',
      location: '',
      description: '',
      notes: '',
      is_hazardous: false,
      requires_refrigeration: false
    })
    setValidationErrors([])
  }

  // Navigation functions
  const nextStep = () => {
    if (currentStep < 3) setCurrentStep(currentStep + 1)
  }

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1)
  }

  if (!user) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Professional Inventory Management</h1>
            <p className="mt-2 text-sm text-gray-700">
              Comprehensive inventory tracking with real-time database integration and advanced analytics
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button
              type="button"
              onClick={() => { resetForm(); setShowAddModal(true); }}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
              Add Item
            </button>
          </div>
        </div>

        {/* Enhanced Statistics Cards */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-5">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ArchiveBoxIcon className="h-6 w-6 text-blue-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Items</dt>
                    <dd className="text-lg font-medium text-gray-900">{inventoryStats.totalItems}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CurrencyDollarIcon className="h-6 w-6 text-green-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Value</dt>
                    <dd className="text-lg font-medium text-gray-900">${inventoryStats.totalValue.toFixed(0)}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CheckCircleIcon className="h-6 w-6 text-green-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">In Stock</dt>
                    <dd className="text-lg font-medium text-gray-900">{inventoryStats.inStockItems}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Low Stock</dt>
                    <dd className="text-lg font-medium text-gray-900">{inventoryStats.lowStockItems}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <XMarkIcon className="h-6 w-6 text-red-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Out of Stock</dt>
                    <dd className="text-lg font-medium text-gray-900">{inventoryStats.outOfStockItems}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Search and Filters */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            {/* Search */}
            <div className="lg:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">Search Items</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                </div>
                <input
                  type="text"
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-green-500 focus:border-green-500"
                  placeholder="Search by name, SKU, category, or description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* Category Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
              <select
                className="block w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-green-500"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                <option value="all">All Categories</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>{category.name}</option>
                ))}
              </select>
            </div>

            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <select
                className="block w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-green-500"
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
              >
                <option value="all">All Status</option>
                <option value="in_stock">In Stock</option>
                <option value="low_stock">Low Stock</option>
                <option value="out_of_stock">Out of Stock</option>
              </select>
            </div>
          </div>

          {/* Sort Options */}
          <div className="mt-4 flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">Sort by:</label>
              <select
                className="border border-gray-300 rounded-md py-1 px-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-green-500"
                value={`${sortBy}-${sortDirection}`}
                onChange={(e) => {
                  const [field, direction] = e.target.value.split('-')
                  setSortBy(field)
                  setSortDirection(direction as 'asc' | 'desc')
                }}
              >
                <option value="name-asc">Name (A-Z)</option>
                <option value="name-desc">Name (Z-A)</option>
                <option value="sku-asc">SKU (A-Z)</option>
                <option value="sku-desc">SKU (Z-A)</option>
                <option value="current_stock-desc">Stock (High-Low)</option>
                <option value="current_stock-asc">Stock (Low-High)</option>
                <option value="unit_cost-desc">Cost (High-Low)</option>
                <option value="unit_cost-asc">Cost (Low-High)</option>
              </select>
            </div>
            <div className="text-sm text-gray-500">
              Showing {filteredItems.length} of {items.length} items
            </div>
          </div>
        </div>

        {/* Inventory Table */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Inventory Items ({filteredItems.length})
            </h3>
            
            {loading ? (
              <div className="animate-pulse">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-4 py-4">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Item
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Category
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Stock Level
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Unit Cost
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="relative px-6 py-3">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredItems.map((item) => (
                      <tr key={item.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{item.name}</div>
                            <div className="text-sm text-gray-500">{item.sku}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.category_name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {item.current_stock} {item.unit_of_measure}
                          </div>
                          <div className="text-sm text-gray-500">
                            Min: {item.minimum_stock} {item.unit_of_measure}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${item.unit_cost.toFixed(2)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(item.status || 'in_stock')}`}>
                            {item.status === 'low_stock' && (
                              <ExclamationTriangleIcon className="w-3 h-3 mr-1" />
                            )}
                            {getStatusText(item.status || 'in_stock')}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button className="text-green-600 hover:text-green-900">
                            Edit
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        {/* Professional Multi-Step Add Item Modal */}
        <Modal
          isOpen={showAddModal}
          onClose={() => { resetForm(); setShowAddModal(false); }}
          title={`Add New Inventory Item - Step ${currentStep} of 3`}
          maxWidth="3xl"
        >
          <div className="space-y-6">
            {/* Progress Steps */}
            <div className="flex items-center justify-between">
              {[1, 2, 3].map((step) => (
                <div key={step} className="flex items-center">
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                    step <= currentStep
                      ? 'bg-green-600 border-green-600 text-white'
                      : 'border-gray-300 text-gray-500'
                  }`}>
                    {step < currentStep ? (
                      <CheckCircleIcon className="w-5 h-5" />
                    ) : (
                      <span className="text-sm font-medium">{step}</span>
                    )}
                  </div>
                  <div className="ml-2 text-sm font-medium text-gray-900">
                    {step === 1 && 'Basic Information'}
                    {step === 2 && 'Stock & Pricing'}
                    {step === 3 && 'Additional Details'}
                  </div>
                  {step < 3 && (
                    <div className={`ml-4 w-16 h-0.5 ${
                      step < currentStep ? 'bg-green-600' : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              ))}
            </div>

            {/* Validation Errors */}
            {validationErrors.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">Please fix the following errors:</h3>
                    <ul className="mt-2 text-sm text-red-700 list-disc list-inside">
                      {validationErrors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {/* Step Content */}
            <div className="min-h-96">
              {currentStep === 1 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Basic Item Information</h3>
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Item Name *</label>
                      <input
                        type="text"
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newItem.name}
                        onChange={(e) => setNewItem({ ...newItem, name: e.target.value })}
                        placeholder="e.g., Vanilla Extract Premium"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">SKU *</label>
                      <div className="mt-1 flex rounded-md shadow-sm">
                        <input
                          type="text"
                          required
                          className="flex-1 block w-full rounded-l-md border-gray-300 focus:border-green-500 focus:ring-green-500"
                          value={newItem.sku}
                          onChange={(e) => setNewItem({ ...newItem, sku: e.target.value })}
                          placeholder="e.g., VAN-001"
                        />
                        <button
                          type="button"
                          onClick={() => setNewItem({ ...newItem, sku: generateSKU() })}
                          className="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 text-sm hover:bg-gray-100"
                        >
                          <DocumentDuplicateIcon className="h-4 w-4" />
                        </button>
                      </div>
                      <p className="mt-1 text-xs text-gray-500">Click the icon to auto-generate</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Category *</label>
                      <select
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newItem.category_id}
                        onChange={(e) => setNewItem({ ...newItem, category_id: e.target.value })}
                      >
                        <option value="">Select a category...</option>
                        {categories.map(category => (
                          <option key={category.id} value={category.id}>{category.name}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Unit of Measure *</label>
                      <select
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newItem.unit_of_measure}
                        onChange={(e) => setNewItem({ ...newItem, unit_of_measure: e.target.value })}
                      >
                        <option value="liters">Liters</option>
                        <option value="kg">Kilograms</option>
                        <option value="grams">Grams</option>
                        <option value="pieces">Pieces</option>
                        <option value="bottles">Bottles</option>
                        <option value="boxes">Boxes</option>
                        <option value="ml">Milliliters</option>
                        <option value="gallons">Gallons</option>
                      </select>
                    </div>
                    <div className="sm:col-span-2">
                      <label className="block text-sm font-medium text-gray-700">Description</label>
                      <textarea
                        rows={3}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newItem.description}
                        onChange={(e) => setNewItem({ ...newItem, description: e.target.value })}
                        placeholder="Detailed description of the item, its uses, and specifications..."
                      />
                    </div>
                  </div>
                </div>
              )}

              {currentStep === 2 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Stock Levels & Pricing</h3>
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Current Stock *</label>
                      <input
                        type="number"
                        min="0"
                        step="0.001"
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newItem.current_stock}
                        onChange={(e) => setNewItem({ ...newItem, current_stock: parseFloat(e.target.value) || 0 })}
                        placeholder="0.000"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Minimum Stock Level *</label>
                      <input
                        type="number"
                        min="0"
                        step="0.001"
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newItem.minimum_stock}
                        onChange={(e) => setNewItem({ ...newItem, minimum_stock: parseFloat(e.target.value) || 0 })}
                        placeholder="0.000"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Maximum Stock Level</label>
                      <input
                        type="number"
                        min="0"
                        step="0.001"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newItem.maximum_stock}
                        onChange={(e) => setNewItem({ ...newItem, maximum_stock: parseFloat(e.target.value) || 0 })}
                        placeholder="0.000"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Reorder Point</label>
                      <input
                        type="number"
                        min="0"
                        step="0.001"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newItem.reorder_point}
                        onChange={(e) => setNewItem({ ...newItem, reorder_point: parseFloat(e.target.value) || 0 })}
                        placeholder="0.000"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Unit Cost ($) *</label>
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newItem.unit_cost}
                        onChange={(e) => setNewItem({ ...newItem, unit_cost: parseFloat(e.target.value) || 0 })}
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Total Value</label>
                      <div className="mt-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm text-gray-900">
                        ${(newItem.current_stock * newItem.unit_cost).toFixed(2)}
                      </div>
                    </div>
                  </div>

                  {/* Stock Level Indicators */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Stock Level Analysis</h4>
                    <div className="grid grid-cols-1 gap-2 sm:grid-cols-3 text-xs">
                      <div className={`p-2 rounded ${newItem.current_stock > newItem.minimum_stock ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                        <span className="font-medium">Status:</span> {newItem.current_stock > newItem.minimum_stock ? 'Adequate Stock' : 'Below Minimum'}
                      </div>
                      <div className="p-2 bg-blue-100 text-blue-800 rounded">
                        <span className="font-medium">Days Supply:</span> {newItem.minimum_stock > 0 ? Math.floor(newItem.current_stock / newItem.minimum_stock * 30) : 'N/A'} days
                      </div>
                      <div className="p-2 bg-purple-100 text-purple-800 rounded">
                        <span className="font-medium">Turnover:</span> {newItem.current_stock > 0 ? 'Active' : 'No Stock'}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {currentStep === 3 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Additional Details & Supplier Information</h3>
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Supplier Name</label>
                      <input
                        type="text"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newItem.supplier_name}
                        onChange={(e) => setNewItem({ ...newItem, supplier_name: e.target.value })}
                        placeholder="e.g., Premium Ingredients Co."
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Supplier Code</label>
                      <input
                        type="text"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newItem.supplier_code}
                        onChange={(e) => setNewItem({ ...newItem, supplier_code: e.target.value })}
                        placeholder="e.g., SUP-001"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Batch Number</label>
                      <input
                        type="text"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newItem.batch_number}
                        onChange={(e) => setNewItem({ ...newItem, batch_number: e.target.value })}
                        placeholder="e.g., BATCH-2024-001"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Expiry Date</label>
                      <input
                        type="date"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newItem.expiry_date}
                        onChange={(e) => setNewItem({ ...newItem, expiry_date: e.target.value })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Storage Location</label>
                      <input
                        type="text"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newItem.location}
                        onChange={(e) => setNewItem({ ...newItem, location: e.target.value })}
                        placeholder="e.g., Warehouse A, Shelf 3"
                      />
                    </div>
                    <div className="sm:col-span-2">
                      <label className="block text-sm font-medium text-gray-700">Additional Notes</label>
                      <textarea
                        rows={3}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                        value={newItem.notes}
                        onChange={(e) => setNewItem({ ...newItem, notes: e.target.value })}
                        placeholder="Special handling instructions, quality notes, etc..."
                      />
                    </div>
                  </div>

                  {/* Special Properties */}
                  <div className="space-y-3">
                    <h4 className="text-sm font-medium text-gray-900">Special Properties</h4>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          className="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-500 focus:ring-green-500"
                          checked={newItem.is_hazardous}
                          onChange={(e) => setNewItem({ ...newItem, is_hazardous: e.target.checked })}
                        />
                        <span className="ml-2 text-sm text-gray-700">Hazardous Material</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          className="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-500 focus:ring-green-500"
                          checked={newItem.requires_refrigeration}
                          onChange={(e) => setNewItem({ ...newItem, requires_refrigeration: e.target.checked })}
                        />
                        <span className="ml-2 text-sm text-gray-700">Requires Refrigeration</span>
                      </label>
                    </div>
                  </div>

                  {/* Summary */}
                  <div className="bg-green-50 p-4 rounded-lg">
                    <h4 className="text-sm font-medium text-green-800 mb-2">Item Summary</h4>
                    <div className="grid grid-cols-2 gap-4 text-xs text-green-700">
                      <div><span className="font-medium">Name:</span> {newItem.name || 'Not specified'}</div>
                      <div><span className="font-medium">SKU:</span> {newItem.sku || 'Not specified'}</div>
                      <div><span className="font-medium">Category:</span> {categories.find(cat => cat.id === newItem.category_id)?.name || 'Not selected'}</div>
                      <div><span className="font-medium">Stock:</span> {newItem.current_stock} {newItem.unit_of_measure}</div>
                      <div><span className="font-medium">Unit Cost:</span> ${newItem.unit_cost.toFixed(2)}</div>
                      <div><span className="font-medium">Total Value:</span> ${(newItem.current_stock * newItem.unit_cost).toFixed(2)}</div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={currentStep === 1 ? () => { resetForm(); setShowAddModal(false); } : prevStep}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                disabled={isCreating}
              >
                {currentStep === 1 ? 'Cancel' : 'Previous'}
              </button>

              <div className="flex space-x-3">
                {currentStep < 3 ? (
                  <button
                    type="button"
                    onClick={nextStep}
                    disabled={currentStep === 1 && (!newItem.name || !newItem.sku || !newItem.category_id)}
                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400"
                  >
                    Next Step
                  </button>
                ) : (
                  <button
                    type="button"
                    onClick={handleAddItem}
                    disabled={isCreating}
                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400"
                  >
                    {isCreating ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Creating...
                      </>
                    ) : (
                      <>
                        <CheckCircleIcon className="-ml-1 mr-2 h-4 w-4" />
                        Create Item
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </Modal>
      </div>
    </MainLayout>
  )
}
