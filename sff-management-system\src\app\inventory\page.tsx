'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { useAuth } from '@/components/providers'
import { Modal } from '@/components/ui/modal'
import { PlusIcon, MagnifyingGlassIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline'

interface InventoryItem {
  id: string
  name: string
  sku: string
  category: string
  current_stock: number
  minimum_stock: number
  unit_of_measure: string
  unit_cost: number
  status: 'in_stock' | 'low_stock' | 'out_of_stock'
}

export default function InventoryPage() {
  const { user } = useAuth()
  const [items, setItems] = useState<InventoryItem[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [showAddModal, setShowAddModal] = useState(false)
  const [newItem, setNewItem] = useState({
    name: '',
    sku: '',
    category: 'Flavors',
    current_stock: 0,
    minimum_stock: 0,
    unit_of_measure: 'liters',
    unit_cost: 0
  })

  useEffect(() => {
    // Mock inventory data
    const mockItems: InventoryItem[] = [
      {
        id: '1',
        name: 'Vanilla Extract',
        sku: 'VAN-001',
        category: 'Flavors',
        current_stock: 5,
        minimum_stock: 10,
        unit_of_measure: 'liters',
        unit_cost: 25.50,
        status: 'low_stock'
      },
      {
        id: '2',
        name: 'Strawberry Flavor',
        sku: 'STR-001',
        category: 'Flavors',
        current_stock: 15,
        minimum_stock: 8,
        unit_of_measure: 'liters',
        unit_cost: 18.75,
        status: 'in_stock'
      },
      {
        id: '3',
        name: 'Propylene Glycol',
        sku: 'PG-001',
        category: 'Base Materials',
        current_stock: 50,
        minimum_stock: 20,
        unit_of_measure: 'liters',
        unit_cost: 12.00,
        status: 'in_stock'
      },
      {
        id: '4',
        name: 'Citric Acid',
        sku: 'CIT-001',
        category: 'Chemicals',
        current_stock: 8,
        minimum_stock: 12,
        unit_of_measure: 'kg',
        unit_cost: 8.50,
        status: 'low_stock'
      },
      {
        id: '5',
        name: 'Glass Bottles 100ml',
        sku: 'BOT-100',
        category: 'Packaging',
        current_stock: 200,
        minimum_stock: 500,
        unit_of_measure: 'pieces',
        unit_cost: 0.85,
        status: 'low_stock'
      },
      {
        id: '6',
        name: 'Ethyl Alcohol',
        sku: 'ETH-001',
        category: 'Base Materials',
        current_stock: 25,
        minimum_stock: 15,
        unit_of_measure: 'liters',
        unit_cost: 22.00,
        status: 'in_stock'
      }
    ]

    setTimeout(() => {
      setItems(mockItems)
      setLoading(false)
    }, 1000)
  }, [])

  const filteredItems = items.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.category.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in_stock':
        return 'bg-green-100 text-green-800'
      case 'low_stock':
        return 'bg-yellow-100 text-yellow-800'
      case 'out_of_stock':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'in_stock':
        return 'In Stock'
      case 'low_stock':
        return 'Low Stock'
      case 'out_of_stock':
        return 'Out of Stock'
      default:
        return 'Unknown'
    }
  }

  const handleAddItem = () => {
    const item: InventoryItem = {
      id: (items.length + 1).toString(),
      name: newItem.name,
      sku: newItem.sku,
      category: newItem.category,
      current_stock: newItem.current_stock,
      minimum_stock: newItem.minimum_stock,
      unit_of_measure: newItem.unit_of_measure,
      unit_cost: newItem.unit_cost,
      status: newItem.current_stock <= newItem.minimum_stock ? 'low_stock' : 'in_stock'
    }

    setItems([...items, item])
    setShowAddModal(false)
    setNewItem({
      name: '',
      sku: '',
      category: 'Flavors',
      current_stock: 0,
      minimum_stock: 0,
      unit_of_measure: 'liters',
      unit_cost: 0
    })
  }

  if (!user) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Inventory Management</h1>
            <p className="mt-2 text-sm text-gray-700">
              Manage your raw materials, ingredients, and packaging supplies
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button
              type="button"
              onClick={() => setShowAddModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
              Add Item
            </button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-green-500 focus:border-green-500"
              placeholder="Search inventory items..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Inventory Table */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Inventory Items ({filteredItems.length})
            </h3>
            
            {loading ? (
              <div className="animate-pulse">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-4 py-4">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Item
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Category
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Stock Level
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Unit Cost
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="relative px-6 py-3">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredItems.map((item) => (
                      <tr key={item.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{item.name}</div>
                            <div className="text-sm text-gray-500">{item.sku}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.category}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {item.current_stock} {item.unit_of_measure}
                          </div>
                          <div className="text-sm text-gray-500">
                            Min: {item.minimum_stock} {item.unit_of_measure}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${item.unit_cost.toFixed(2)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                            {item.status === 'low_stock' && (
                              <ExclamationTriangleIcon className="w-3 h-3 mr-1" />
                            )}
                            {getStatusText(item.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button className="text-green-600 hover:text-green-900">
                            Edit
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        {/* Add Item Modal */}
        <Modal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          title="Add New Inventory Item"
          maxWidth="xl"
        >
          <form onSubmit={(e) => { e.preventDefault(); handleAddItem(); }} className="space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Item Name
                </label>
                <input
                  type="text"
                  id="name"
                  required
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newItem.name}
                  onChange={(e) => setNewItem({ ...newItem, name: e.target.value })}
                />
              </div>
              <div>
                <label htmlFor="sku" className="block text-sm font-medium text-gray-700">
                  SKU
                </label>
                <input
                  type="text"
                  id="sku"
                  required
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newItem.sku}
                  onChange={(e) => setNewItem({ ...newItem, sku: e.target.value })}
                />
              </div>
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                  Category
                </label>
                <select
                  id="category"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newItem.category}
                  onChange={(e) => setNewItem({ ...newItem, category: e.target.value })}
                >
                  <option value="Flavors">Flavors</option>
                  <option value="Base Materials">Base Materials</option>
                  <option value="Chemicals">Chemicals</option>
                  <option value="Packaging">Packaging</option>
                </select>
              </div>
              <div>
                <label htmlFor="unit_of_measure" className="block text-sm font-medium text-gray-700">
                  Unit of Measure
                </label>
                <select
                  id="unit_of_measure"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newItem.unit_of_measure}
                  onChange={(e) => setNewItem({ ...newItem, unit_of_measure: e.target.value })}
                >
                  <option value="liters">Liters</option>
                  <option value="kg">Kilograms</option>
                  <option value="pieces">Pieces</option>
                  <option value="grams">Grams</option>
                </select>
              </div>
              <div>
                <label htmlFor="current_stock" className="block text-sm font-medium text-gray-700">
                  Current Stock
                </label>
                <input
                  type="number"
                  id="current_stock"
                  min="0"
                  step="0.1"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newItem.current_stock}
                  onChange={(e) => setNewItem({ ...newItem, current_stock: parseFloat(e.target.value) || 0 })}
                />
              </div>
              <div>
                <label htmlFor="minimum_stock" className="block text-sm font-medium text-gray-700">
                  Minimum Stock
                </label>
                <input
                  type="number"
                  id="minimum_stock"
                  min="0"
                  step="0.1"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newItem.minimum_stock}
                  onChange={(e) => setNewItem({ ...newItem, minimum_stock: parseFloat(e.target.value) || 0 })}
                />
              </div>
              <div className="sm:col-span-2">
                <label htmlFor="unit_cost" className="block text-sm font-medium text-gray-700">
                  Unit Cost ($)
                </label>
                <input
                  type="number"
                  id="unit_cost"
                  min="0"
                  step="0.01"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  value={newItem.unit_cost}
                  onChange={(e) => setNewItem({ ...newItem, unit_cost: parseFloat(e.target.value) || 0 })}
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={() => setShowAddModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700"
              >
                Add Item
              </button>
            </div>
          </form>
        </Modal>
      </div>
    </MainLayout>
  )
}
