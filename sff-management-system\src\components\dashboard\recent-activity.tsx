'use client'

import { useEffect, useState } from 'react'
import { createSupabaseClient } from '@/lib/supabase'
import { format } from 'date-fns'
import {
  CubeIcon,
  ClipboardDocumentListIcon,
  CogIcon,
  BeakerIcon,
} from '@heroicons/react/24/outline'

interface Activity {
  id: string
  type: 'inventory' | 'recipe' | 'production' | 'quality'
  title: string
  description: string
  timestamp: string
  user?: string
}

export function RecentActivity() {
  const [activities, setActivities] = useState<Activity[]>([])
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseClient()

  useEffect(() => {
    async function fetchRecentActivity() {
      try {
        // This is a simplified version - in a real app, you'd have a proper activity log table
        const recentActivities: Activity[] = [
          {
            id: '1',
            type: 'inventory',
            title: 'Low Stock Alert',
            description: 'Vanilla Extract is running low (5 units remaining)',
            timestamp: new Date().toISOString(),
          },
          {
            id: '2',
            type: 'production',
            title: 'Batch Completed',
            description: 'Production batch SFF-20241211-001 completed successfully',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          },
          {
            id: '3',
            type: 'quality',
            title: 'Quality Test Passed',
            description: 'COA generated for batch SFF-20241211-001',
            timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          },
          {
            id: '4',
            type: 'recipe',
            title: 'Recipe Updated',
            description: 'Strawberry Flavor v2.1 recipe modified',
            timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          },
        ]

        setActivities(recentActivities)
      } catch (error) {
        console.error('Error fetching recent activity:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchRecentActivity()
  }, [supabase])

  const getActivityIcon = (type: Activity['type']) => {
    switch (type) {
      case 'inventory':
        return CubeIcon
      case 'recipe':
        return ClipboardDocumentListIcon
      case 'production':
        return CogIcon
      case 'quality':
        return BeakerIcon
      default:
        return CubeIcon
    }
  }

  const getActivityColor = (type: Activity['type']) => {
    switch (type) {
      case 'inventory':
        return 'text-blue-600 bg-blue-100'
      case 'recipe':
        return 'text-green-600 bg-green-100'
      case 'production':
        return 'text-purple-600 bg-purple-100'
      case 'quality':
        return 'text-orange-600 bg-orange-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  if (loading) {
    return (
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex items-start space-x-3 animate-pulse">
                <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
        <div className="flow-root">
          <ul role="list" className="-mb-8">
            {activities.map((activity, activityIdx) => {
              const Icon = getActivityIcon(activity.type)
              const colorClasses = getActivityColor(activity.type)
              
              return (
                <li key={activity.id}>
                  <div className="relative pb-8">
                    {activityIdx !== activities.length - 1 ? (
                      <span
                        className="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200"
                        aria-hidden="true"
                      />
                    ) : null}
                    <div className="relative flex space-x-3">
                      <div>
                        <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${colorClasses}`}>
                          <Icon className="h-4 w-4" aria-hidden="true" />
                        </span>
                      </div>
                      <div className="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                        <div>
                          <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                          <p className="text-sm text-gray-500">{activity.description}</p>
                        </div>
                        <div className="whitespace-nowrap text-right text-sm text-gray-500">
                          <time dateTime={activity.timestamp}>
                            {format(new Date(activity.timestamp), 'MMM d, h:mm a')}
                          </time>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              )
            })}
          </ul>
        </div>
      </div>
    </div>
  )
}
